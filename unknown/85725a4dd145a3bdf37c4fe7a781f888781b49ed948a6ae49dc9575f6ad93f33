{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  EventSourceDriver = require('eventsource');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:eventsource');\n}\nfunction EventSourceReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  var es = this.es = new EventSourceDriver(url);\n  es.onmessage = function (e) {\n    debug('message', e.data);\n    self.emit('message', decodeURI(e.data));\n  };\n  es.onerror = function (e) {\n    debug('error', es.readyState, e);\n    // ES on reconnection has readyState = 0 or 1.\n    // on network error it's CLOSED = 2\n    var reason = es.readyState !== 2 ? 'network' : 'permanent';\n    self._cleanup();\n    self._close(reason);\n  };\n}\ninherits(EventSourceReceiver, EventEmitter);\nEventSourceReceiver.prototype.abort = function () {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\nEventSourceReceiver.prototype._cleanup = function () {\n  debug('cleanup');\n  var es = this.es;\n  if (es) {\n    es.onmessage = es.onerror = null;\n    es.close();\n    this.es = null;\n  }\n};\nEventSourceReceiver.prototype._close = function (reason) {\n  debug('close', reason);\n  var self = this;\n  // Safari and chrome < 15 crash if we close window before\n  // waiting for ES cleanup. See:\n  // https://code.google.com/p/chromium/issues/detail?id=89155\n  setTimeout(function () {\n    self.emit('close', null, reason);\n    self.removeAllListeners();\n  }, 200);\n};\nmodule.exports = EventSourceReceiver;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "EventSourceDriver", "debug", "process", "env", "NODE_ENV", "EventSourceReceiver", "url", "call", "self", "es", "onmessage", "e", "data", "emit", "decodeURI", "onerror", "readyState", "reason", "_cleanup", "_close", "prototype", "abort", "close", "setTimeout", "removeAllListeners", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/transport/receiver/eventsource.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , EventSourceDriver = require('eventsource')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:eventsource');\n}\n\nfunction EventSourceReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n\n  var self = this;\n  var es = this.es = new EventSourceDriver(url);\n  es.onmessage = function(e) {\n    debug('message', e.data);\n    self.emit('message', decodeURI(e.data));\n  };\n  es.onerror = function(e) {\n    debug('error', es.readyState, e);\n    // ES on reconnection has readyState = 0 or 1.\n    // on network error it's CLOSED = 2\n    var reason = (es.readyState !== 2 ? 'network' : 'permanent');\n    self._cleanup();\n    self._close(reason);\n  };\n}\n\ninherits(EventSourceReceiver, EventEmitter);\n\nEventSourceReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nEventSourceReceiver.prototype._cleanup = function() {\n  debug('cleanup');\n  var es = this.es;\n  if (es) {\n    es.onmessage = es.onerror = null;\n    es.close();\n    this.es = null;\n  }\n};\n\nEventSourceReceiver.prototype._close = function(reason) {\n  debug('close', reason);\n  var self = this;\n  // Safari and chrome < 15 crash if we close window before\n  // waiting for ES cleanup. See:\n  // https://code.google.com/p/chromium/issues/detail?id=89155\n  setTimeout(function() {\n    self.emit('close', null, reason);\n    self.removeAllListeners();\n  }, 200);\n};\n\nmodule.exports = EventSourceReceiver;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;EAC7CC,iBAAiB,GAAGF,OAAO,CAAC,aAAa,CAAC;AAG9C,IAAIG,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGH,OAAO,CAAC,OAAO,CAAC,CAAC,oCAAoC,CAAC;AAChE;AAEA,SAASO,mBAAmBA,CAACC,GAAG,EAAE;EAChCL,KAAK,CAACK,GAAG,CAAC;EACVP,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;EAEvB,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,EAAE,GAAG,IAAI,CAACA,EAAE,GAAG,IAAIT,iBAAiB,CAACM,GAAG,CAAC;EAC7CG,EAAE,CAACC,SAAS,GAAG,UAASC,CAAC,EAAE;IACzBV,KAAK,CAAC,SAAS,EAAEU,CAAC,CAACC,IAAI,CAAC;IACxBJ,IAAI,CAACK,IAAI,CAAC,SAAS,EAAEC,SAAS,CAACH,CAAC,CAACC,IAAI,CAAC,CAAC;EACzC,CAAC;EACDH,EAAE,CAACM,OAAO,GAAG,UAASJ,CAAC,EAAE;IACvBV,KAAK,CAAC,OAAO,EAAEQ,EAAE,CAACO,UAAU,EAAEL,CAAC,CAAC;IAChC;IACA;IACA,IAAIM,MAAM,GAAIR,EAAE,CAACO,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG,WAAY;IAC5DR,IAAI,CAACU,QAAQ,CAAC,CAAC;IACfV,IAAI,CAACW,MAAM,CAACF,MAAM,CAAC;EACrB,CAAC;AACH;AAEApB,QAAQ,CAACQ,mBAAmB,EAAEN,YAAY,CAAC;AAE3CM,mBAAmB,CAACe,SAAS,CAACC,KAAK,GAAG,YAAW;EAC/CpB,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAACiB,QAAQ,CAAC,CAAC;EACf,IAAI,CAACC,MAAM,CAAC,MAAM,CAAC;AACrB,CAAC;AAEDd,mBAAmB,CAACe,SAAS,CAACF,QAAQ,GAAG,YAAW;EAClDjB,KAAK,CAAC,SAAS,CAAC;EAChB,IAAIQ,EAAE,GAAG,IAAI,CAACA,EAAE;EAChB,IAAIA,EAAE,EAAE;IACNA,EAAE,CAACC,SAAS,GAAGD,EAAE,CAACM,OAAO,GAAG,IAAI;IAChCN,EAAE,CAACa,KAAK,CAAC,CAAC;IACV,IAAI,CAACb,EAAE,GAAG,IAAI;EAChB;AACF,CAAC;AAEDJ,mBAAmB,CAACe,SAAS,CAACD,MAAM,GAAG,UAASF,MAAM,EAAE;EACtDhB,KAAK,CAAC,OAAO,EAAEgB,MAAM,CAAC;EACtB,IAAIT,IAAI,GAAG,IAAI;EACf;EACA;EACA;EACAe,UAAU,CAAC,YAAW;IACpBf,IAAI,CAACK,IAAI,CAAC,OAAO,EAAE,IAAI,EAAEI,MAAM,CAAC;IAChCT,IAAI,CAACgB,kBAAkB,CAAC,CAAC;EAC3B,CAAC,EAAE,GAAG,CAAC;AACT,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGrB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}