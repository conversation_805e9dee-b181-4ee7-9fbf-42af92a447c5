{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\i-message.ts"], "sourcesContent": ["import type { <PERSON>rame } from './i-frame.js';\nimport { StompHeaders } from './stomp-headers.js';\n\n/**\n * Instance of Message will be passed to [subscription callback]{@link Client#subscribe}\n * and [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n * Since it is an extended {@link IFrame}, you can access [headers]{@link IFrame#headers}\n * and [body]{@link IFrame#body} as properties.\n *\n * Part of `@stomp/stompjs`.\n *\n * See [Client#subscribe]{@link Client#subscribe} for example.\n */\nexport interface IMessage extends IFrame {\n  /**\n   * When subscribing with manual acknowledgement, call this method on the message to ACK the message.\n   *\n   * See [Client#ack]{@link Client#ack} for an example.\n   */\n  ack: (headers?: StompHeaders) => void;\n\n  /**\n   * When subscribing with manual acknowledgement, call this method on the message to NACK the message.\n   *\n   * See [Client#nack]{@link Client#nack} for an example.\n   */\n  nack: (headers?: StompHeaders) => void;\n}\n\n/**\n * Aliased to {@link IMessage}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type Message = IMessage;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}