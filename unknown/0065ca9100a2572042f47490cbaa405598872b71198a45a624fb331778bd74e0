{"ast": null, "code": "/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n  constructor(client) {\n    this.client = client;\n  }\n  get outgoing() {\n    return this.client.heartbeatOutgoing;\n  }\n  set outgoing(value) {\n    this.client.heartbeatOutgoing = value;\n  }\n  get incoming() {\n    return this.client.heartbeatIncoming;\n  }\n  set incoming(value) {\n    this.client.heartbeatIncoming = value;\n  }\n}", "map": {"version": 3, "names": ["HeartbeatInfo", "constructor", "client", "outgoing", "heartbeatOutgoing", "value", "incoming", "heartbeatIncoming"], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\compatibility\\heartbeat-info.ts"], "sourcesContent": ["import { CompatClient } from './compat-client.js';\n\n/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n  constructor(private client: CompatClient) {}\n\n  get outgoing(): number {\n    return this.client.heartbeatOutgoing;\n  }\n\n  set outgoing(value: number) {\n    this.client.heartbeatOutgoing = value;\n  }\n\n  get incoming(): number {\n    return this.client.heartbeatIncoming;\n  }\n\n  set incoming(value: number) {\n    this.client.heartbeatIncoming = value;\n  }\n}\n"], "mappings": "AAEA;;;;;AAKA,OAAM,MAAOA,aAAa;EACxBC,YAAoBC,MAAoB;IAApB,KAAAA,MAAM,GAANA,MAAM;EAAiB;EAE3C,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACD,MAAM,CAACE,iBAAiB;EACtC;EAEA,IAAID,QAAQA,CAACE,KAAa;IACxB,IAAI,CAACH,MAAM,CAACE,iBAAiB,GAAGC,KAAK;EACvC;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACJ,MAAM,CAACK,iBAAiB;EACtC;EAEA,IAAID,QAAQA,CAACD,KAAa;IACxB,IAAI,CAACH,MAAM,CAACK,iBAAiB,GAAGF,KAAK;EACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}