!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).StompJs={})}(this,(function(e){"use strict";const t="\n",n="\0";class s{get body(){return!this._body&&this.isBinaryBody&&(this._body=(new TextDecoder).decode(this._binaryBody)),this._body||""}get binaryBody(){return this._binaryBody||this.isBinaryBody||(this._binaryBody=(new TextEncoder).encode(this._body)),this._binaryBody}constructor(e){const{command:t,headers:n,body:s,binaryBody:i,escapeHeaderValues:o,skipContentLengthHeader:r}=e;this.command=t,this.headers=Object.assign({},n||{}),i?(this._binaryBody=i,this.isBinaryBody=!0):(this._body=s||"",this.isBinaryBody=!1),this.escapeHeaderValues=o||!1,this.skipContentLengthHeader=r||!1}static fromRawFrame(e,t){const n={},i=e=>e.replace(/^\s+|\s+$/g,"");for(const o of e.headers.reverse()){o.indexOf(":");const r=i(o[0]);let c=i(o[1]);t&&"CONNECT"!==e.command&&"CONNECTED"!==e.command&&(c=s.hdrValueUnEscape(c)),n[r]=c}return new s({command:e.command,headers:n,binaryBody:e.binaryBody,escapeHeaderValues:t})}toString(){return this.serializeCmdAndHeaders()}serialize(){const e=this.serializeCmdAndHeaders();return this.isBinaryBody?s.toUnit8Array(e,this._binaryBody).buffer:e+this._body+n}serializeCmdAndHeaders(){const e=[this.command];this.skipContentLengthHeader&&delete this.headers["content-length"];for(const t of Object.keys(this.headers||{})){const n=this.headers[t];this.escapeHeaderValues&&"CONNECT"!==this.command&&"CONNECTED"!==this.command?e.push(`${t}:${s.hdrValueEscape(`${n}`)}`):e.push(`${t}:${n}`)}return(this.isBinaryBody||!this.isBodyEmpty()&&!this.skipContentLengthHeader)&&e.push(`content-length:${this.bodyLength()}`),e.join(t)+t+t}isBodyEmpty(){return 0===this.bodyLength()}bodyLength(){const e=this.binaryBody;return e?e.length:0}static sizeOfUTF8(e){return e?(new TextEncoder).encode(e).length:0}static toUnit8Array(e,t){const n=(new TextEncoder).encode(e),s=new Uint8Array([0]),i=new Uint8Array(n.length+t.length+s.length);return i.set(n),i.set(t,n.length),i.set(s,n.length+t.length),i}static marshall(e){return new s(e).serialize()}static hdrValueEscape(e){return e.replace(/\\/g,"\\\\").replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/:/g,"\\c")}static hdrValueUnEscape(e){return e.replace(/\\r/g,"\r").replace(/\\n/g,"\n").replace(/\\c/g,":").replace(/\\\\/g,"\\")}}class i{constructor(e,t){this.onFrame=e,this.onIncomingPing=t,this._encoder=new TextEncoder,this._decoder=new TextDecoder,this._token=[],this._initState()}parseChunk(e,t=!1){let n;if(n="string"==typeof e?this._encoder.encode(e):new Uint8Array(e),t&&0!==n[n.length-1]){const e=new Uint8Array(n.length+1);e.set(n,0),e[n.length]=0,n=e}for(let e=0;e<n.length;e++){const t=n[e];this._onByte(t)}}_collectFrame(e){0!==e&&13!==e&&(10!==e?(this._onByte=this._collectCommand,this._reinjectByte(e)):this.onIncomingPing())}_collectCommand(e){if(13!==e)return 10===e?(this._results.command=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaders)):void this._consumeByte(e)}_collectHeaders(e){13!==e&&(10!==e?(this._onByte=this._collectHeaderKey,this._reinjectByte(e)):this._setupCollectBody())}_reinjectByte(e){this._onByte(e)}_collectHeaderKey(e){if(58===e)return this._headerKey=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaderValue);this._consumeByte(e)}_collectHeaderValue(e){if(13!==e)return 10===e?(this._results.headers.push([this._headerKey,this._consumeTokenAsUTF8()]),this._headerKey=void 0,void(this._onByte=this._collectHeaders)):void this._consumeByte(e)}_setupCollectBody(){const e=this._results.headers.filter((e=>"content-length"===e[0]))[0];e?(this._bodyBytesRemaining=parseInt(e[1],10),this._onByte=this._collectBodyFixedSize):this._onByte=this._collectBodyNullTerminated}_collectBodyNullTerminated(e){0!==e?this._consumeByte(e):this._retrievedBody()}_collectBodyFixedSize(e){0!=this._bodyBytesRemaining--?this._consumeByte(e):this._retrievedBody()}_retrievedBody(){this._results.binaryBody=this._consumeTokenAsRaw();try{this.onFrame(this._results)}catch(e){console.log("Ignoring an exception thrown by a frame handler. Original exception: ",e)}this._initState()}_consumeByte(e){this._token.push(e)}_consumeTokenAsUTF8(){return this._decoder.decode(this._consumeTokenAsRaw())}_consumeTokenAsRaw(){const e=new Uint8Array(this._token);return this._token=[],e}_initState(){this._results={command:void 0,headers:[],binaryBody:void 0},this._token=[],this._headerKey=void 0,this._onByte=this._collectFrame}}var o,r,c,a;e.StompSocketState=void 0,(o=e.StompSocketState||(e.StompSocketState={}))[o.CONNECTING=0]="CONNECTING",o[o.OPEN=1]="OPEN",o[o.CLOSING=2]="CLOSING",o[o.CLOSED=3]="CLOSED",e.ActivationState=void 0,(r=e.ActivationState||(e.ActivationState={}))[r.ACTIVE=0]="ACTIVE",r[r.DEACTIVATING=1]="DEACTIVATING",r[r.INACTIVE=2]="INACTIVE",e.ReconnectionTimeMode=void 0,(c=e.ReconnectionTimeMode||(e.ReconnectionTimeMode={}))[c.LINEAR=0]="LINEAR",c[c.EXPONENTIAL=1]="EXPONENTIAL",e.TickerStrategy=void 0,(a=e.TickerStrategy||(e.TickerStrategy={})).Interval="interval",a.Worker="worker";class h{constructor(t,n=e.TickerStrategy.Interval,s){this._interval=t,this._strategy=n,this._debug=s,this._workerScript=`\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `}start(e){this.stop(),this.shouldUseWorker()?this.runWorker(e):this.runInterval(e)}stop(){this.disposeWorker(),this.disposeInterval()}shouldUseWorker(){return"undefined"!=typeof Worker&&this._strategy===e.TickerStrategy.Worker}runWorker(e){this._debug("Using runWorker for outgoing pings"),this._worker||(this._worker=new Worker(URL.createObjectURL(new Blob([this._workerScript],{type:"text/javascript"}))),this._worker.onmessage=t=>e(t.data))}runInterval(e){if(this._debug("Using runInterval for outgoing pings"),!this._timer){const t=Date.now();this._timer=setInterval((()=>{e(Date.now()-t)}),this._interval)}}disposeWorker(){this._worker&&(this._worker.terminate(),delete this._worker,this._debug("Outgoing ping disposeWorker"))}disposeInterval(){this._timer&&(clearInterval(this._timer),delete this._timer,this._debug("Outgoing ping disposeInterval"))}}class d{constructor(e){this.versions=e}supportedVersions(){return this.versions.join(",")}protocolVersions(){return this.versions.map((e=>`v${e.replace(".","")}.stomp`))}}d.V1_0="1.0",d.V1_1="1.1",d.V1_2="1.2",d.default=new d([d.V1_2,d.V1_1,d.V1_0]);class l{get connectedVersion(){return this._connectedVersion}get connected(){return this._connected}constructor(e,t,n){this._client=e,this._webSocket=t,this._connected=!1,this._serverFrameHandlers={CONNECTED:e=>{this.debug(`connected to server ${e.headers.server}`),this._connected=!0,this._connectedVersion=e.headers.version,this._connectedVersion===d.V1_2&&(this._escapeHeaderValues=!0),this._setupHeartbeat(e.headers),this.onConnect(e)},MESSAGE:e=>{const t=e.headers.subscription,n=this._subscriptions[t]||this.onUnhandledMessage,s=e,i=this,o=this._connectedVersion===d.V1_2?s.headers.ack:s.headers["message-id"];s.ack=(e={})=>i.ack(o,t,e),s.nack=(e={})=>i.nack(o,t,e),n(s)},RECEIPT:e=>{const t=this._receiptWatchers[e.headers["receipt-id"]];t?(t(e),delete this._receiptWatchers[e.headers["receipt-id"]]):this.onUnhandledReceipt(e)},ERROR:e=>{this.onStompError(e)}},this._counter=0,this._subscriptions={},this._receiptWatchers={},this._partialData="",this._escapeHeaderValues=!1,this._lastServerActivityTS=Date.now(),this.debug=n.debug,this.stompVersions=n.stompVersions,this.connectHeaders=n.connectHeaders,this.disconnectHeaders=n.disconnectHeaders,this.heartbeatIncoming=n.heartbeatIncoming,this.heartbeatOutgoing=n.heartbeatOutgoing,this.splitLargeFrames=n.splitLargeFrames,this.maxWebSocketChunkSize=n.maxWebSocketChunkSize,this.forceBinaryWSFrames=n.forceBinaryWSFrames,this.logRawCommunication=n.logRawCommunication,this.appendMissingNULLonIncoming=n.appendMissingNULLonIncoming,this.discardWebsocketOnCommFailure=n.discardWebsocketOnCommFailure,this.onConnect=n.onConnect,this.onDisconnect=n.onDisconnect,this.onStompError=n.onStompError,this.onWebSocketClose=n.onWebSocketClose,this.onWebSocketError=n.onWebSocketError,this.onUnhandledMessage=n.onUnhandledMessage,this.onUnhandledReceipt=n.onUnhandledReceipt,this.onUnhandledFrame=n.onUnhandledFrame}start(){const e=new i((e=>{const t=s.fromRawFrame(e,this._escapeHeaderValues);this.logRawCommunication||this.debug(`<<< ${t}`);(this._serverFrameHandlers[t.command]||this.onUnhandledFrame)(t)}),(()=>{this.debug("<<< PONG")}));this._webSocket.onmessage=t=>{if(this.debug("Received data"),this._lastServerActivityTS=Date.now(),this.logRawCommunication){const e=t.data instanceof ArrayBuffer?(new TextDecoder).decode(t.data):t.data;this.debug(`<<< ${e}`)}e.parseChunk(t.data,this.appendMissingNULLonIncoming)},this._webSocket.onclose=e=>{this.debug(`Connection closed to ${this._webSocket.url}`),this._cleanUp(),this.onWebSocketClose(e)},this._webSocket.onerror=e=>{this.onWebSocketError(e)},this._webSocket.onopen=()=>{const e=Object.assign({},this.connectHeaders);this.debug("Web Socket Opened..."),e["accept-version"]=this.stompVersions.supportedVersions(),e["heart-beat"]=[this.heartbeatOutgoing,this.heartbeatIncoming].join(","),this._transmit({command:"CONNECT",headers:e})}}_setupHeartbeat(n){if(n.version!==d.V1_1&&n.version!==d.V1_2)return;if(!n["heart-beat"])return;const[s,i]=n["heart-beat"].split(",").map((e=>parseInt(e,10)));if(0!==this.heartbeatOutgoing&&0!==i){const n=Math.max(this.heartbeatOutgoing,i);this.debug(`send PING every ${n}ms`),this._pinger=new h(n,this._client.heartbeatStrategy,this.debug),this._pinger.start((()=>{this._webSocket.readyState===e.StompSocketState.OPEN&&(this._webSocket.send(t),this.debug(">>> PING"))}))}if(0!==this.heartbeatIncoming&&0!==s){const e=Math.max(this.heartbeatIncoming,s);this.debug(`check PONG every ${e}ms`),this._ponger=setInterval((()=>{const t=Date.now()-this._lastServerActivityTS;t>2*e&&(this.debug(`did not receive server activity for the last ${t}ms`),this._closeOrDiscardWebsocket())}),e)}}_closeOrDiscardWebsocket(){this.discardWebsocketOnCommFailure?(this.debug("Discarding websocket, the underlying socket may linger for a while"),this.discardWebsocket()):(this.debug("Issuing close on the websocket"),this._closeWebsocket())}forceDisconnect(){this._webSocket&&(this._webSocket.readyState!==e.StompSocketState.CONNECTING&&this._webSocket.readyState!==e.StompSocketState.OPEN||this._closeOrDiscardWebsocket())}_closeWebsocket(){this._webSocket.onmessage=()=>{},this._webSocket.close()}discardWebsocket(){var e,t;"function"!=typeof this._webSocket.terminate&&(e=this._webSocket,t=e=>this.debug(e),e.terminate=function(){const n=()=>{};this.onerror=n,this.onmessage=n,this.onopen=n;const s=new Date,i=Math.random().toString().substring(2,8),o=this.onclose;this.onclose=e=>{const n=(new Date).getTime()-s.getTime();t(`Discarded socket (#${i})  closed after ${n}ms, with code/reason: ${e.code}/${e.reason}`)},this.close(),o?.call(e,{code:4001,reason:`Quick discarding socket (#${i}) without waiting for the shutdown sequence.`,wasClean:!1})}),this._webSocket.terminate()}_transmit(e){const{command:t,headers:n,body:i,binaryBody:o,skipContentLengthHeader:r}=e,c=new s({command:t,headers:n,body:i,binaryBody:o,escapeHeaderValues:this._escapeHeaderValues,skipContentLengthHeader:r});let a=c.serialize();if(this.logRawCommunication?this.debug(`>>> ${a}`):this.debug(`>>> ${c}`),this.forceBinaryWSFrames&&"string"==typeof a&&(a=(new TextEncoder).encode(a)),"string"==typeof a&&this.splitLargeFrames){let e=a;for(;e.length>0;){const t=e.substring(0,this.maxWebSocketChunkSize);e=e.substring(this.maxWebSocketChunkSize),this._webSocket.send(t),this.debug(`chunk sent = ${t.length}, remaining = ${e.length}`)}}else this._webSocket.send(a)}dispose(){if(this.connected)try{const e=Object.assign({},this.disconnectHeaders);e.receipt||(e.receipt="close-"+this._counter++),this.watchForReceipt(e.receipt,(e=>{this._closeWebsocket(),this._cleanUp(),this.onDisconnect(e)})),this._transmit({command:"DISCONNECT",headers:e})}catch(e){this.debug(`Ignoring error during disconnect ${e}`)}else this._webSocket.readyState!==e.StompSocketState.CONNECTING&&this._webSocket.readyState!==e.StompSocketState.OPEN||this._closeWebsocket()}_cleanUp(){this._connected=!1,this._pinger&&(this._pinger.stop(),this._pinger=void 0),this._ponger&&(clearInterval(this._ponger),this._ponger=void 0)}publish(e){const{destination:t,headers:n,body:s,binaryBody:i,skipContentLengthHeader:o}=e,r=Object.assign({destination:t},n);this._transmit({command:"SEND",headers:r,body:s,binaryBody:i,skipContentLengthHeader:o})}watchForReceipt(e,t){this._receiptWatchers[e]=t}subscribe(e,t,n={}){(n=Object.assign({},n)).id||(n.id="sub-"+this._counter++),n.destination=e,this._subscriptions[n.id]=t,this._transmit({command:"SUBSCRIBE",headers:n});const s=this;return{id:n.id,unsubscribe:e=>s.unsubscribe(n.id,e)}}unsubscribe(e,t={}){t=Object.assign({},t),delete this._subscriptions[e],t.id=e,this._transmit({command:"UNSUBSCRIBE",headers:t})}begin(e){const t=e||"tx-"+this._counter++;this._transmit({command:"BEGIN",headers:{transaction:t}});const n=this;return{id:t,commit(){n.commit(t)},abort(){n.abort(t)}}}commit(e){this._transmit({command:"COMMIT",headers:{transaction:e}})}abort(e){this._transmit({command:"ABORT",headers:{transaction:e}})}ack(e,t,n={}){n=Object.assign({},n),this._connectedVersion===d.V1_2?n.id=e:n["message-id"]=e,n.subscription=t,this._transmit({command:"ACK",headers:n})}nack(e,t,n={}){return n=Object.assign({},n),this._connectedVersion===d.V1_2?n.id=e:n["message-id"]=e,n.subscription=t,this._transmit({command:"NACK",headers:n})}}class m{get webSocket(){return this._stompHandler?._webSocket}get disconnectHeaders(){return this._disconnectHeaders}set disconnectHeaders(e){this._disconnectHeaders=e,this._stompHandler&&(this._stompHandler.disconnectHeaders=this._disconnectHeaders)}get connected(){return!!this._stompHandler&&this._stompHandler.connected}get connectedVersion(){return this._stompHandler?this._stompHandler.connectedVersion:void 0}get active(){return this.state===e.ActivationState.ACTIVE}_changeState(e){this.state=e,this.onChangeState(e)}constructor(t={}){this.stompVersions=d.default,this.connectionTimeout=0,this.reconnectDelay=5e3,this._nextReconnectDelay=0,this.maxReconnectDelay=9e5,this.reconnectTimeMode=e.ReconnectionTimeMode.LINEAR,this.heartbeatIncoming=1e4,this.heartbeatOutgoing=1e4,this.heartbeatStrategy=e.TickerStrategy.Interval,this.splitLargeFrames=!1,this.maxWebSocketChunkSize=8192,this.forceBinaryWSFrames=!1,this.appendMissingNULLonIncoming=!1,this.discardWebsocketOnCommFailure=!1,this.state=e.ActivationState.INACTIVE;const n=()=>{};this.debug=n,this.beforeConnect=n,this.onConnect=n,this.onDisconnect=n,this.onUnhandledMessage=n,this.onUnhandledReceipt=n,this.onUnhandledFrame=n,this.onStompError=n,this.onWebSocketClose=n,this.onWebSocketError=n,this.logRawCommunication=!1,this.onChangeState=n,this.connectHeaders={},this._disconnectHeaders={},this.configure(t)}configure(e){Object.assign(this,e),this.maxReconnectDelay>0&&this.maxReconnectDelay<this.reconnectDelay&&(this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`),this.maxReconnectDelay=this.reconnectDelay)}activate(){const t=()=>{this.active?this.debug("Already ACTIVE, ignoring request to activate"):(this._changeState(e.ActivationState.ACTIVE),this._nextReconnectDelay=this.reconnectDelay,this._connect())};this.state===e.ActivationState.DEACTIVATING?(this.debug("Waiting for deactivation to finish before activating"),this.deactivate().then((()=>{t()}))):t()}async _connect(){if(await this.beforeConnect(this),this._stompHandler)return void this.debug("There is already a stompHandler, skipping the call to connect");if(!this.active)return void this.debug("Client has been marked inactive, will not attempt to connect");this.connectionTimeout>0&&(this._connectionWatcher&&clearTimeout(this._connectionWatcher),this._connectionWatcher=setTimeout((()=>{this.connected||(this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`),this.forceDisconnect())}),this.connectionTimeout)),this.debug("Opening Web Socket...");const t=this._createWebSocket();this._stompHandler=new l(this,t,{debug:this.debug,stompVersions:this.stompVersions,connectHeaders:this.connectHeaders,disconnectHeaders:this._disconnectHeaders,heartbeatIncoming:this.heartbeatIncoming,heartbeatOutgoing:this.heartbeatOutgoing,heartbeatStrategy:this.heartbeatStrategy,splitLargeFrames:this.splitLargeFrames,maxWebSocketChunkSize:this.maxWebSocketChunkSize,forceBinaryWSFrames:this.forceBinaryWSFrames,logRawCommunication:this.logRawCommunication,appendMissingNULLonIncoming:this.appendMissingNULLonIncoming,discardWebsocketOnCommFailure:this.discardWebsocketOnCommFailure,onConnect:e=>{if(this._connectionWatcher&&(clearTimeout(this._connectionWatcher),this._connectionWatcher=void 0),!this.active)return this.debug("STOMP got connected while deactivate was issued, will disconnect now"),void this._disposeStompHandler();this.onConnect(e)},onDisconnect:e=>{this.onDisconnect(e)},onStompError:e=>{this.onStompError(e)},onWebSocketClose:t=>{this._stompHandler=void 0,this.state===e.ActivationState.DEACTIVATING&&this._changeState(e.ActivationState.INACTIVE),this.onWebSocketClose(t),this.active&&this._schedule_reconnect()},onWebSocketError:e=>{this.onWebSocketError(e)},onUnhandledMessage:e=>{this.onUnhandledMessage(e)},onUnhandledReceipt:e=>{this.onUnhandledReceipt(e)},onUnhandledFrame:e=>{this.onUnhandledFrame(e)}}),this._stompHandler.start()}_createWebSocket(){let e;if(this.webSocketFactory)e=this.webSocketFactory();else{if(!this.brokerURL)throw new Error("Either brokerURL or webSocketFactory must be provided");e=new WebSocket(this.brokerURL,this.stompVersions.protocolVersions())}return e.binaryType="arraybuffer",e}_schedule_reconnect(){this._nextReconnectDelay>0&&(this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`),this._reconnector=setTimeout((()=>{this.reconnectTimeMode===e.ReconnectionTimeMode.EXPONENTIAL&&(this._nextReconnectDelay=2*this._nextReconnectDelay,0!==this.maxReconnectDelay&&(this._nextReconnectDelay=Math.min(this._nextReconnectDelay,this.maxReconnectDelay))),this._connect()}),this._nextReconnectDelay))}async deactivate(t={}){const n=t.force||!1,s=this.active;let i;if(this.state===e.ActivationState.INACTIVE)return this.debug("Already INACTIVE, nothing more to do"),Promise.resolve();if(this._changeState(e.ActivationState.DEACTIVATING),this._nextReconnectDelay=0,this._reconnector&&(clearTimeout(this._reconnector),this._reconnector=void 0),!this._stompHandler||this.webSocket.readyState===e.StompSocketState.CLOSED)return this._changeState(e.ActivationState.INACTIVE),Promise.resolve();{const e=this._stompHandler.onWebSocketClose;i=new Promise(((t,n)=>{this._stompHandler.onWebSocketClose=n=>{e(n),t()}}))}return n?this._stompHandler?.discardWebsocket():s&&this._disposeStompHandler(),i}forceDisconnect(){this._stompHandler&&this._stompHandler.forceDisconnect()}_disposeStompHandler(){this._stompHandler&&this._stompHandler.dispose()}publish(e){this._checkConnection(),this._stompHandler.publish(e)}_checkConnection(){if(!this.connected)throw new TypeError("There is no underlying STOMP connection")}watchForReceipt(e,t){this._checkConnection(),this._stompHandler.watchForReceipt(e,t)}subscribe(e,t,n={}){return this._checkConnection(),this._stompHandler.subscribe(e,t,n)}unsubscribe(e,t={}){this._checkConnection(),this._stompHandler.unsubscribe(e,t)}begin(e){return this._checkConnection(),this._stompHandler.begin(e)}commit(e){this._checkConnection(),this._stompHandler.commit(e)}abort(e){this._checkConnection(),this._stompHandler.abort(e)}ack(e,t,n={}){this._checkConnection(),this._stompHandler.ack(e,t,n)}nack(e,t,n={}){this._checkConnection(),this._stompHandler.nack(e,t,n)}}class g{constructor(e){this.client=e}get outgoing(){return this.client.heartbeatOutgoing}set outgoing(e){this.client.heartbeatOutgoing=e}get incoming(){return this.client.heartbeatIncoming}set incoming(e){this.client.heartbeatIncoming=e}}class _ extends m{constructor(e){super(),this.maxWebSocketFrameSize=16384,this._heartbeatInfo=new g(this),this.reconnect_delay=0,this.webSocketFactory=e,this.debug=(...e)=>{console.log(...e)}}_parseConnect(...e){let t,n,s,i={};if(e.length<2)throw new Error("Connect requires at least 2 arguments");if("function"==typeof e[1])[i,n,s,t]=e;else if(6===e.length)[i.login,i.passcode,n,s,t,i.host]=e;else[i.login,i.passcode,n,s,t]=e;return[i,n,s,t]}connect(...e){const t=this._parseConnect(...e);t[0]&&(this.connectHeaders=t[0]),t[1]&&(this.onConnect=t[1]),t[2]&&(this.onStompError=t[2]),t[3]&&(this.onWebSocketClose=t[3]),super.activate()}disconnect(e,t={}){e&&(this.onDisconnect=e),this.disconnectHeaders=t,super.deactivate()}send(e,t={},n=""){const s=!1===(t=Object.assign({},t))["content-length"];s&&delete t["content-length"],this.publish({destination:e,headers:t,body:n,skipContentLengthHeader:s})}set reconnect_delay(e){this.reconnectDelay=e}get ws(){return this.webSocket}get version(){return this.connectedVersion}get onreceive(){return this.onUnhandledMessage}set onreceive(e){this.onUnhandledMessage=e}get onreceipt(){return this.onUnhandledReceipt}set onreceipt(e){this.onUnhandledReceipt=e}get heartbeat(){return this._heartbeatInfo}set heartbeat(e){this.heartbeatIncoming=e.incoming,this.heartbeatOutgoing=e.outgoing}}class u{static client(e,t){null==t&&(t=d.default.protocolVersions());return new _((()=>new(u.WebSocketClass||WebSocket)(e,t)))}static over(e){let t;return"function"==typeof e?t=e:(console.warn("Stomp.over did not receive a factory, auto reconnect will not work. Please see https://stomp-js.github.io/api-docs/latest/classes/Stomp.html#over"),t=()=>e),new _(t)}}u.WebSocketClass=null,e.Client=m,e.CompatClient=_,e.FrameImpl=s,e.Parser=i,e.Stomp=u,e.StompConfig=class{},e.StompHeaders=class{},e.Versions=d}));
