{"ast": null, "code": "'use strict';\n\nvar urlUtils = require('./utils/url'),\n  eventUtils = require('./utils/event'),\n  FacadeJS = require('./facade'),\n  InfoIframeReceiver = require('./info-iframe-receiver'),\n  iframeUtils = require('./utils/iframe'),\n  loc = require('./location');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:iframe-bootstrap');\n}\nmodule.exports = function (SockJS, availableTransports) {\n  var transportMap = {};\n  availableTransports.forEach(function (at) {\n    if (at.facadeTransport) {\n      transportMap[at.facadeTransport.transportName] = at.facadeTransport;\n    }\n  });\n\n  // hard-coded for the info iframe\n  // TODO see if we can make this more dynamic\n  transportMap[InfoIframeReceiver.transportName] = InfoIframeReceiver;\n  var parentOrigin;\n\n  /* eslint-disable camelcase */\n  SockJS.bootstrap_iframe = function () {\n    /* eslint-enable camelcase */\n    var facade;\n    iframeUtils.currentWindowId = loc.hash.slice(1);\n    var onMessage = function (e) {\n      if (e.source !== parent) {\n        return;\n      }\n      if (typeof parentOrigin === 'undefined') {\n        parentOrigin = e.origin;\n      }\n      if (e.origin !== parentOrigin) {\n        return;\n      }\n      var iframeMessage;\n      try {\n        iframeMessage = JSON.parse(e.data);\n      } catch (ignored) {\n        debug('bad json', e.data);\n        return;\n      }\n      if (iframeMessage.windowId !== iframeUtils.currentWindowId) {\n        return;\n      }\n      switch (iframeMessage.type) {\n        case 's':\n          var p;\n          try {\n            p = JSON.parse(iframeMessage.data);\n          } catch (ignored) {\n            debug('bad json', iframeMessage.data);\n            break;\n          }\n          var version = p[0];\n          var transport = p[1];\n          var transUrl = p[2];\n          var baseUrl = p[3];\n          debug(version, transport, transUrl, baseUrl);\n          // change this to semver logic\n          if (version !== SockJS.version) {\n            throw new Error('Incompatible SockJS! Main site uses:' + ' \"' + version + '\", the iframe:' + ' \"' + SockJS.version + '\".');\n          }\n          if (!urlUtils.isOriginEqual(transUrl, loc.href) || !urlUtils.isOriginEqual(baseUrl, loc.href)) {\n            throw new Error('Can\\'t connect to different domain from within an ' + 'iframe. (' + loc.href + ', ' + transUrl + ', ' + baseUrl + ')');\n          }\n          facade = new FacadeJS(new transportMap[transport](transUrl, baseUrl));\n          break;\n        case 'm':\n          facade._send(iframeMessage.data);\n          break;\n        case 'c':\n          if (facade) {\n            facade._close();\n          }\n          facade = null;\n          break;\n      }\n    };\n    eventUtils.attachEvent('message', onMessage);\n\n    // Start\n    iframeUtils.postMessage('s');\n  };\n};", "map": {"version": 3, "names": ["urlUtils", "require", "eventUtils", "FacadeJS", "InfoIframeReceiver", "iframe<PERSON><PERSON>s", "loc", "debug", "process", "env", "NODE_ENV", "module", "exports", "SockJS", "availableTransports", "transportMap", "for<PERSON>ach", "at", "facadeTransport", "transportName", "parent<PERSON><PERSON>in", "bootstrap_iframe", "facade", "currentWindowId", "hash", "slice", "onMessage", "e", "source", "parent", "origin", "iframeMessage", "JSON", "parse", "data", "ignored", "windowId", "type", "p", "version", "transport", "transUrl", "baseUrl", "Error", "isOriginEqual", "href", "_send", "_close", "attachEvent", "postMessage"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/iframe-bootstrap.js"], "sourcesContent": ["'use strict';\n\nvar urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , FacadeJS = require('./facade')\n  , InfoIframeReceiver = require('./info-iframe-receiver')\n  , iframeUtils = require('./utils/iframe')\n  , loc = require('./location')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:iframe-bootstrap');\n}\n\nmodule.exports = function(SockJS, availableTransports) {\n  var transportMap = {};\n  availableTransports.forEach(function(at) {\n    if (at.facadeTransport) {\n      transportMap[at.facadeTransport.transportName] = at.facadeTransport;\n    }\n  });\n\n  // hard-coded for the info iframe\n  // TODO see if we can make this more dynamic\n  transportMap[InfoIframeReceiver.transportName] = InfoIframeReceiver;\n  var parentOrigin;\n\n  /* eslint-disable camelcase */\n  SockJS.bootstrap_iframe = function() {\n    /* eslint-enable camelcase */\n    var facade;\n    iframeUtils.currentWindowId = loc.hash.slice(1);\n    var onMessage = function(e) {\n      if (e.source !== parent) {\n        return;\n      }\n      if (typeof parentOrigin === 'undefined') {\n        parentOrigin = e.origin;\n      }\n      if (e.origin !== parentOrigin) {\n        return;\n      }\n\n      var iframeMessage;\n      try {\n        iframeMessage = JSON.parse(e.data);\n      } catch (ignored) {\n        debug('bad json', e.data);\n        return;\n      }\n\n      if (iframeMessage.windowId !== iframeUtils.currentWindowId) {\n        return;\n      }\n      switch (iframeMessage.type) {\n      case 's':\n        var p;\n        try {\n          p = JSON.parse(iframeMessage.data);\n        } catch (ignored) {\n          debug('bad json', iframeMessage.data);\n          break;\n        }\n        var version = p[0];\n        var transport = p[1];\n        var transUrl = p[2];\n        var baseUrl = p[3];\n        debug(version, transport, transUrl, baseUrl);\n        // change this to semver logic\n        if (version !== SockJS.version) {\n          throw new Error('Incompatible SockJS! Main site uses:' +\n                    ' \"' + version + '\", the iframe:' +\n                    ' \"' + SockJS.version + '\".');\n        }\n\n        if (!urlUtils.isOriginEqual(transUrl, loc.href) ||\n            !urlUtils.isOriginEqual(baseUrl, loc.href)) {\n          throw new Error('Can\\'t connect to different domain from within an ' +\n                    'iframe. (' + loc.href + ', ' + transUrl + ', ' + baseUrl + ')');\n        }\n        facade = new FacadeJS(new transportMap[transport](transUrl, baseUrl));\n        break;\n      case 'm':\n        facade._send(iframeMessage.data);\n        break;\n      case 'c':\n        if (facade) {\n          facade._close();\n        }\n        facade = null;\n        break;\n      }\n    };\n\n    eventUtils.attachEvent('message', onMessage);\n\n    // Start\n    iframeUtils.postMessage('s');\n  };\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,QAAQ,GAAGF,OAAO,CAAC,UAAU,CAAC;EAC9BG,kBAAkB,GAAGH,OAAO,CAAC,wBAAwB,CAAC;EACtDI,WAAW,GAAGJ,OAAO,CAAC,gBAAgB,CAAC;EACvCK,GAAG,GAAGL,OAAO,CAAC,YAAY,CAAC;AAG/B,IAAIM,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGN,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC;AAC5D;AAEAU,MAAM,CAACC,OAAO,GAAG,UAASC,MAAM,EAAEC,mBAAmB,EAAE;EACrD,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrBD,mBAAmB,CAACE,OAAO,CAAC,UAASC,EAAE,EAAE;IACvC,IAAIA,EAAE,CAACC,eAAe,EAAE;MACtBH,YAAY,CAACE,EAAE,CAACC,eAAe,CAACC,aAAa,CAAC,GAAGF,EAAE,CAACC,eAAe;IACrE;EACF,CAAC,CAAC;;EAEF;EACA;EACAH,YAAY,CAACX,kBAAkB,CAACe,aAAa,CAAC,GAAGf,kBAAkB;EACnE,IAAIgB,YAAY;;EAEhB;EACAP,MAAM,CAACQ,gBAAgB,GAAG,YAAW;IACnC;IACA,IAAIC,MAAM;IACVjB,WAAW,CAACkB,eAAe,GAAGjB,GAAG,CAACkB,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC;IAC/C,IAAIC,SAAS,GAAG,SAAAA,CAASC,CAAC,EAAE;MAC1B,IAAIA,CAAC,CAACC,MAAM,KAAKC,MAAM,EAAE;QACvB;MACF;MACA,IAAI,OAAOT,YAAY,KAAK,WAAW,EAAE;QACvCA,YAAY,GAAGO,CAAC,CAACG,MAAM;MACzB;MACA,IAAIH,CAAC,CAACG,MAAM,KAAKV,YAAY,EAAE;QAC7B;MACF;MAEA,IAAIW,aAAa;MACjB,IAAI;QACFA,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACN,CAAC,CAACO,IAAI,CAAC;MACpC,CAAC,CAAC,OAAOC,OAAO,EAAE;QAChB5B,KAAK,CAAC,UAAU,EAAEoB,CAAC,CAACO,IAAI,CAAC;QACzB;MACF;MAEA,IAAIH,aAAa,CAACK,QAAQ,KAAK/B,WAAW,CAACkB,eAAe,EAAE;QAC1D;MACF;MACA,QAAQQ,aAAa,CAACM,IAAI;QAC1B,KAAK,GAAG;UACN,IAAIC,CAAC;UACL,IAAI;YACFA,CAAC,GAAGN,IAAI,CAACC,KAAK,CAACF,aAAa,CAACG,IAAI,CAAC;UACpC,CAAC,CAAC,OAAOC,OAAO,EAAE;YAChB5B,KAAK,CAAC,UAAU,EAAEwB,aAAa,CAACG,IAAI,CAAC;YACrC;UACF;UACA,IAAIK,OAAO,GAAGD,CAAC,CAAC,CAAC,CAAC;UAClB,IAAIE,SAAS,GAAGF,CAAC,CAAC,CAAC,CAAC;UACpB,IAAIG,QAAQ,GAAGH,CAAC,CAAC,CAAC,CAAC;UACnB,IAAII,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC;UAClB/B,KAAK,CAACgC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;UAC5C;UACA,IAAIH,OAAO,KAAK1B,MAAM,CAAC0B,OAAO,EAAE;YAC9B,MAAM,IAAII,KAAK,CAAC,sCAAsC,GAC5C,IAAI,GAAGJ,OAAO,GAAG,gBAAgB,GACjC,IAAI,GAAG1B,MAAM,CAAC0B,OAAO,GAAG,IAAI,CAAC;UACzC;UAEA,IAAI,CAACvC,QAAQ,CAAC4C,aAAa,CAACH,QAAQ,EAAEnC,GAAG,CAACuC,IAAI,CAAC,IAC3C,CAAC7C,QAAQ,CAAC4C,aAAa,CAACF,OAAO,EAAEpC,GAAG,CAACuC,IAAI,CAAC,EAAE;YAC9C,MAAM,IAAIF,KAAK,CAAC,oDAAoD,GAC1D,WAAW,GAAGrC,GAAG,CAACuC,IAAI,GAAG,IAAI,GAAGJ,QAAQ,GAAG,IAAI,GAAGC,OAAO,GAAG,GAAG,CAAC;UAC5E;UACApB,MAAM,GAAG,IAAInB,QAAQ,CAAC,IAAIY,YAAY,CAACyB,SAAS,CAAC,CAACC,QAAQ,EAAEC,OAAO,CAAC,CAAC;UACrE;QACF,KAAK,GAAG;UACNpB,MAAM,CAACwB,KAAK,CAACf,aAAa,CAACG,IAAI,CAAC;UAChC;QACF,KAAK,GAAG;UACN,IAAIZ,MAAM,EAAE;YACVA,MAAM,CAACyB,MAAM,CAAC,CAAC;UACjB;UACAzB,MAAM,GAAG,IAAI;UACb;MACF;IACF,CAAC;IAEDpB,UAAU,CAAC8C,WAAW,CAAC,SAAS,EAAEtB,SAAS,CAAC;;IAE5C;IACArB,WAAW,CAAC4C,WAAW,CAAC,GAAG,CAAC;EAC9B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}