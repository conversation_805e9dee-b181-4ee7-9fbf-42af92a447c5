[{"C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\styles\\theme.ts": "3", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\styles\\GlobalStyles.ts": "4", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\NotFoundPage.tsx": "5", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\contexts\\AuthContext.tsx": "6", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\RegisterPage.tsx": "7", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\LoginPage.tsx": "8", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\dashboard\\DashboardPage.tsx": "9", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\layout\\Layout.tsx": "10", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\common\\ErrorBoundary.tsx": "11", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\auth\\ProtectedRoute.tsx": "12", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\authService.ts": "13", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\layout\\Header.tsx": "14", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\api.ts": "15", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\appointments\\AppointmentsPage.tsx": "16", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\profile\\ProfilePage.tsx": "17", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\video-consultations\\VideoConsultationsPage.tsx": "18", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\prescriptions\\PrescriptionsPage.tsx": "19", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\ai-health-bot\\AIHealthBotPage.tsx": "20", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\chat\\ChatPage.tsx": "21", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\doctors\\DoctorsPage.tsx": "22", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\appointmentService.ts": "23", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\appointments\\AppointmentBookingModal.tsx": "24", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\appointments\\AppointmentDetailsModal.tsx": "25", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\apiService.ts": "26", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\dashboard\\DoctorDashboard.tsx": "27", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\dashboard\\PatientDashboard.tsx": "28", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\appointments\\AppointmentBooking.tsx": "29", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\PatientDashboard.tsx": "30", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\DoctorDashboard.tsx": "31", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\auth\\RoleBasedRoute.tsx": "32", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\SimpleDoctorDashboard.tsx": "33", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\SimplePatientDashboard.tsx": "34", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\TestPage.tsx": "35", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\SimpleRegisterPage.tsx": "36", "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\SimpleLoginPage.tsx": "37"}, {"size": 252, "mtime": 1749993850640, "results": "38", "hashOfConfig": "39"}, {"size": 3825, "mtime": 1750042033185, "results": "40", "hashOfConfig": "39"}, {"size": 4311, "mtime": 1749995284618, "results": "41", "hashOfConfig": "39"}, {"size": 9732, "mtime": 1750009802673, "results": "42", "hashOfConfig": "39"}, {"size": 1525, "mtime": 1749994756790, "results": "43", "hashOfConfig": "39"}, {"size": 6262, "mtime": 1749996666553, "results": "44", "hashOfConfig": "39"}, {"size": 16342, "mtime": 1750005595402, "results": "45", "hashOfConfig": "39"}, {"size": 10369, "mtime": 1750000537273, "results": "46", "hashOfConfig": "39"}, {"size": 1031, "mtime": 1750009676294, "results": "47", "hashOfConfig": "39"}, {"size": 617, "mtime": 1749993757860, "results": "48", "hashOfConfig": "39"}, {"size": 2808, "mtime": 1749994776485, "results": "49", "hashOfConfig": "39"}, {"size": 2064, "mtime": 1750004784135, "results": "50", "hashOfConfig": "39"}, {"size": 5249, "mtime": 1750011002339, "results": "51", "hashOfConfig": "39"}, {"size": 5829, "mtime": 1750001990529, "results": "52", "hashOfConfig": "39"}, {"size": 2808, "mtime": 1750006321199, "results": "53", "hashOfConfig": "39"}, {"size": 17124, "mtime": 1750002897197, "results": "54", "hashOfConfig": "39"}, {"size": 13002, "mtime": 1750000588623, "results": "55", "hashOfConfig": "39"}, {"size": 4008, "mtime": 1750042033575, "results": "56", "hashOfConfig": "39"}, {"size": 4040, "mtime": 1750002060488, "results": "57", "hashOfConfig": "39"}, {"size": 4039, "mtime": 1750002083119, "results": "58", "hashOfConfig": "39"}, {"size": 4032, "mtime": 1750002017888, "results": "59", "hashOfConfig": "39"}, {"size": 3967, "mtime": 1750002104955, "results": "60", "hashOfConfig": "39"}, {"size": 6320, "mtime": 1750002534841, "results": "61", "hashOfConfig": "39"}, {"size": 12967, "mtime": 1750002829021, "results": "62", "hashOfConfig": "39"}, {"size": 10506, "mtime": 1750002873881, "results": "63", "hashOfConfig": "39"}, {"size": 3529, "mtime": 1750002977193, "results": "64", "hashOfConfig": "39"}, {"size": 12259, "mtime": 1750005209326, "results": "65", "hashOfConfig": "39"}, {"size": 13686, "mtime": 1750005146362, "results": "66", "hashOfConfig": "39"}, {"size": 13104, "mtime": 1750009851084, "results": "67", "hashOfConfig": "39"}, {"size": 6573, "mtime": 1750010253016, "results": "68", "hashOfConfig": "39"}, {"size": 8087, "mtime": 1750010289764, "results": "69", "hashOfConfig": "39"}, {"size": 1209, "mtime": 1750009386847, "results": "70", "hashOfConfig": "39"}, {"size": 6090, "mtime": 1750010662290, "results": "71", "hashOfConfig": "39"}, {"size": 4661, "mtime": 1750010628593, "results": "72", "hashOfConfig": "39"}, {"size": 7784, "mtime": 1750011207067, "results": "73", "hashOfConfig": "39"}, {"size": 10140, "mtime": 1750011481792, "results": "74", "hashOfConfig": "39"}, {"size": 5269, "mtime": 1750011405813, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10qpgot", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\styles\\theme.ts", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\styles\\GlobalStyles.ts", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\RegisterPage.tsx", [], ["187"], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\LoginPage.tsx", ["188", "189", "190", "191", "192"], ["193"], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\dashboard\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\appointments\\AppointmentsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\profile\\ProfilePage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\video-consultations\\VideoConsultationsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\prescriptions\\PrescriptionsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\ai-health-bot\\AIHealthBotPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\chat\\ChatPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\doctors\\DoctorsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\appointmentService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\appointments\\AppointmentBookingModal.tsx", ["194", "195", "196", "197", "198"], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\appointments\\AppointmentDetailsModal.tsx", ["199"], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\services\\apiService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\dashboard\\DoctorDashboard.tsx", ["200", "201", "202", "203"], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\dashboard\\PatientDashboard.tsx", ["204", "205"], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\appointments\\AppointmentBooking.tsx", ["206", "207"], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\PatientDashboard.tsx", ["208", "209", "210", "211"], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\DoctorDashboard.tsx", ["212", "213", "214"], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\auth\\RoleBasedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\SimpleDoctorDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\components\\dashboard\\SimplePatientDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\SimpleRegisterPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\src\\pages\\auth\\SimpleLoginPage.tsx", [], [], {"ruleId": "215", "severity": 1, "message": "216", "line": 179, "column": 6, "nodeType": "217", "endLine": 179, "endColumn": 8, "suggestions": "218", "suppressions": "219"}, {"ruleId": "220", "severity": 1, "message": "221", "line": 6, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 6, "endColumn": 13}, {"ruleId": "220", "severity": 1, "message": "224", "line": 6, "column": 15, "nodeType": "222", "messageId": "223", "endLine": 6, "endColumn": 21}, {"ruleId": "220", "severity": 1, "message": "225", "line": 6, "column": 23, "nodeType": "222", "messageId": "223", "endLine": 6, "endColumn": 27}, {"ruleId": "220", "severity": 1, "message": "226", "line": 6, "column": 29, "nodeType": "222", "messageId": "223", "endLine": 6, "endColumn": 33}, {"ruleId": "220", "severity": 1, "message": "227", "line": 6, "column": 35, "nodeType": "222", "messageId": "223", "endLine": 6, "endColumn": 40}, {"ruleId": "215", "severity": 1, "message": "216", "line": 221, "column": 6, "nodeType": "217", "endLine": 221, "endColumn": 8, "suggestions": "228", "suppressions": "229"}, {"ruleId": "220", "severity": 1, "message": "230", "line": 3, "column": 23, "nodeType": "222", "messageId": "223", "endLine": 3, "endColumn": 28}, {"ruleId": "220", "severity": 1, "message": "231", "line": 6, "column": 30, "nodeType": "222", "messageId": "223", "endLine": 6, "endColumn": 45}, {"ruleId": "220", "severity": 1, "message": "232", "line": 188, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 188, "endColumn": 19}, {"ruleId": "220", "severity": 1, "message": "233", "line": 189, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 189, "endColumn": 19}, {"ruleId": "215", "severity": 1, "message": "234", "line": 205, "column": 6, "nodeType": "217", "endLine": 205, "endColumn": 40, "suggestions": "235"}, {"ruleId": "220", "severity": 1, "message": "236", "line": 11, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 11, "endColumn": 8}, {"ruleId": "220", "severity": 1, "message": "237", "line": 7, "column": 23, "nodeType": "222", "messageId": "223", "endLine": 7, "endColumn": 40}, {"ruleId": "220", "severity": 1, "message": "238", "line": 16, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 16, "endColumn": 14}, {"ruleId": "220", "severity": 1, "message": "239", "line": 17, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 17, "endColumn": 13}, {"ruleId": "220", "severity": 1, "message": "240", "line": 19, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 19, "endColumn": 7}, {"ruleId": "220", "severity": 1, "message": "240", "line": 19, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 19, "endColumn": 7}, {"ruleId": "220", "severity": 1, "message": "241", "line": 21, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 21, "endColumn": 14}, {"ruleId": "220", "severity": 1, "message": "242", "line": 160, "column": 11, "nodeType": "222", "messageId": "223", "endLine": 160, "endColumn": 16}, {"ruleId": "215", "severity": 1, "message": "243", "line": 230, "column": 6, "nodeType": "217", "endLine": 230, "endColumn": 8, "suggestions": "244"}, {"ruleId": "220", "severity": 1, "message": "245", "line": 1, "column": 27, "nodeType": "222", "messageId": "223", "endLine": 1, "endColumn": 36}, {"ruleId": "220", "severity": 1, "message": "246", "line": 5, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 19}, {"ruleId": "220", "severity": 1, "message": "247", "line": 5, "column": 21, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 25}, {"ruleId": "220", "severity": 1, "message": "248", "line": 145, "column": 24, "nodeType": "222", "messageId": "223", "endLine": 145, "endColumn": 39}, {"ruleId": "220", "severity": 1, "message": "246", "line": 5, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 19}, {"ruleId": "220", "severity": 1, "message": "247", "line": 5, "column": 21, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 25}, {"ruleId": "215", "severity": 1, "message": "249", "line": 255, "column": 6, "nodeType": "217", "endLine": 255, "endColumn": 8, "suggestions": "250"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'clearError'. Either include it or remove the dependency array.", "ArrayExpression", ["251"], ["252"], "@typescript-eslint/no-unused-vars", "'Eye' is defined but never used.", "Identifier", "unusedVar", "'EyeOff' is defined but never used.", "'Mail' is defined but never used.", "'Lock' is defined but never used.", "'Heart' is defined but never used.", ["253"], ["254"], "'Clock' is defined but never used.", "'AppointmentType' is defined but never used.", "'timeSlots' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTimeSlots'. Either include it or remove the dependency array.", ["255"], "'Phone' is defined but never used.", "'AppointmentStatus' is defined but never used.", "'CheckCircle' is defined but never used.", "'TrendingUp' is defined but never used.", "'Bell' is defined but never used.", "'AlertCircle' is defined but never used.", "'state' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'mockDoctors' and 'mockTimeSlots'. Either include them or remove the dependency array.", ["256"], "'useEffect' is defined but never used.", "'Container' is defined but never used.", "'Card' is defined but never used.", "'setAppointments' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'mockAppointments'. Either include it or remove the dependency array.", ["257"], {"desc": "258", "fix": "259"}, {"kind": "260", "justification": "261"}, {"desc": "258", "fix": "262"}, {"kind": "260", "justification": "261"}, {"desc": "263", "fix": "264"}, {"desc": "265", "fix": "266"}, {"desc": "267", "fix": "268"}, "Update the dependencies array to be: [clearError]", {"range": "269", "text": "270"}, "directive", "", {"range": "271", "text": "270"}, "Update the dependencies array to be: [formData.doctorId, formData.date, loadTimeSlots]", {"range": "272", "text": "273"}, "Update the dependencies array to be: [mockDoctors, mockTimeSlots]", {"range": "274", "text": "275"}, "Update the dependencies array to be: [mockAppointments]", {"range": "276", "text": "277"}, [5008, 5010], "[clearError]", [6254, 6256], [5966, 6000], "[formData.doctorId, formData.date, loadTimeSlots]", [5969, 5971], "[mockDoctors, mockTimeSlots]", [5869, 5871], "[mockAppointments]"]