{"ast": null, "code": "'use strict';\n\nvar crypto = require('crypto');\n\n// This string has length 32, a power of 2, so the modulus doesn't introduce a\n// bias.\nvar _randomStringChars = 'abcdefghijklmnopqrstuvwxyz012345';\nmodule.exports = {\n  string: function (length) {\n    var max = _randomStringChars.length;\n    var bytes = crypto.randomBytes(length);\n    var ret = [];\n    for (var i = 0; i < length; i++) {\n      ret.push(_randomStringChars.substr(bytes[i] % max, 1));\n    }\n    return ret.join('');\n  },\n  number: function (max) {\n    return Math.floor(Math.random() * max);\n  },\n  numberString: function (max) {\n    var t = ('' + (max - 1)).length;\n    var p = new Array(t + 1).join('0');\n    return (p + this.number(max)).slice(-t);\n  }\n};", "map": {"version": 3, "names": ["crypto", "require", "_randomStringChars", "module", "exports", "string", "length", "max", "bytes", "randomBytes", "ret", "i", "push", "substr", "join", "number", "Math", "floor", "random", "numberString", "t", "p", "Array", "slice"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/utils/random.js"], "sourcesContent": ["'use strict';\n\nvar crypto = require('crypto');\n\n// This string has length 32, a power of 2, so the modulus doesn't introduce a\n// bias.\nvar _randomStringChars = 'abcdefghijklmnopqrstuvwxyz012345';\nmodule.exports = {\n  string: function(length) {\n    var max = _randomStringChars.length;\n    var bytes = crypto.randomBytes(length);\n    var ret = [];\n    for (var i = 0; i < length; i++) {\n      ret.push(_randomStringChars.substr(bytes[i] % max, 1));\n    }\n    return ret.join('');\n  }\n\n, number: function(max) {\n    return Math.floor(Math.random() * max);\n  }\n\n, numberString: function(max) {\n    var t = ('' + (max - 1)).length;\n    var p = new Array(t + 1).join('0');\n    return (p + this.number(max)).slice(-t);\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;;AAE9B;AACA;AACA,IAAIC,kBAAkB,GAAG,kCAAkC;AAC3DC,MAAM,CAACC,OAAO,GAAG;EACfC,MAAM,EAAE,SAAAA,CAASC,MAAM,EAAE;IACvB,IAAIC,GAAG,GAAGL,kBAAkB,CAACI,MAAM;IACnC,IAAIE,KAAK,GAAGR,MAAM,CAACS,WAAW,CAACH,MAAM,CAAC;IACtC,IAAII,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC/BD,GAAG,CAACE,IAAI,CAACV,kBAAkB,CAACW,MAAM,CAACL,KAAK,CAACG,CAAC,CAAC,GAAGJ,GAAG,EAAE,CAAC,CAAC,CAAC;IACxD;IACA,OAAOG,GAAG,CAACI,IAAI,CAAC,EAAE,CAAC;EACrB,CAAC;EAEDC,MAAM,EAAE,SAAAA,CAASR,GAAG,EAAE;IACpB,OAAOS,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGX,GAAG,CAAC;EACxC,CAAC;EAEDY,YAAY,EAAE,SAAAA,CAASZ,GAAG,EAAE;IAC1B,IAAIa,CAAC,GAAG,CAAC,EAAE,IAAIb,GAAG,GAAG,CAAC,CAAC,EAAED,MAAM;IAC/B,IAAIe,CAAC,GAAG,IAAIC,KAAK,CAACF,CAAC,GAAG,CAAC,CAAC,CAACN,IAAI,CAAC,GAAG,CAAC;IAClC,OAAO,CAACO,CAAC,GAAG,IAAI,CAACN,MAAM,CAACR,GAAG,CAAC,EAAEgB,KAAK,CAAC,CAACH,CAAC,CAAC;EACzC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}