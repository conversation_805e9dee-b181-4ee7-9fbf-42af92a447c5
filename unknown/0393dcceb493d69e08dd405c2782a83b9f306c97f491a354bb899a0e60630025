{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\i-frame.ts"], "sourcesContent": ["import { StompHeaders } from './stomp-headers.js';\n\n/**\n * It represents a STOMP frame. Many of the callbacks pass an IFrame received from\n * the STOMP broker. For advanced usage you might need to access [headers]{@link IFrame#headers}.\n *\n * Part of `@stomp/stompjs`.\n *\n * {@link IMessage} is an extended IFrame.\n */\nexport interface IFrame {\n  /**\n   * STOMP Command\n   */\n  command: string;\n\n  /**\n   * Headers, key value pairs.\n   */\n  headers: StompHeaders;\n\n  /**\n   * Is this frame binary (based on whether body/binaryBody was passed when creating this frame).\n   */\n  isBinaryBody: boolean;\n\n  /**\n   * body of the frame as string\n   */\n  readonly body: string;\n\n  /**\n   * body as Uint8Array\n   */\n  readonly binaryBody: Uint8Array;\n}\n\n/**\n * Alias for {@link IFrame}\n */\nexport type Frame = IFrame;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}