{"ast": null, "code": "'use strict';\n\nvar random = require('../../utils/random'),\n  urlUtils = require('../../utils/url');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:jsonp');\n}\nvar form, area;\nfunction createIframe(id) {\n  debug('createIframe', id);\n  try {\n    // ie6 dynamic iframes with target=\"\" support (thanks <PERSON>)\n    return global.document.createElement('<iframe name=\"' + id + '\">');\n  } catch (x) {\n    var iframe = global.document.createElement('iframe');\n    iframe.name = id;\n    return iframe;\n  }\n}\nfunction createForm() {\n  debug('createForm');\n  form = global.document.createElement('form');\n  form.style.display = 'none';\n  form.style.position = 'absolute';\n  form.method = 'POST';\n  form.enctype = 'application/x-www-form-urlencoded';\n  form.acceptCharset = 'UTF-8';\n  area = global.document.createElement('textarea');\n  area.name = 'd';\n  form.appendChild(area);\n  global.document.body.appendChild(form);\n}\nmodule.exports = function (url, payload, callback) {\n  debug(url, payload);\n  if (!form) {\n    createForm();\n  }\n  var id = 'a' + random.string(8);\n  form.target = id;\n  form.action = urlUtils.addQuery(urlUtils.addPath(url, '/jsonp_send'), 'i=' + id);\n  var iframe = createIframe(id);\n  iframe.id = id;\n  iframe.style.display = 'none';\n  form.appendChild(iframe);\n  try {\n    area.value = payload;\n  } catch (e) {\n    // seriously broken browsers get here\n  }\n  form.submit();\n  var completed = function (err) {\n    debug('completed', id, err);\n    if (!iframe.onerror) {\n      return;\n    }\n    iframe.onreadystatechange = iframe.onerror = iframe.onload = null;\n    // Opera mini doesn't like if we GC iframe\n    // immediately, thus this timeout.\n    setTimeout(function () {\n      debug('cleaning up', id);\n      iframe.parentNode.removeChild(iframe);\n      iframe = null;\n    }, 500);\n    area.value = '';\n    // It is not possible to detect if the iframe succeeded or\n    // failed to submit our form.\n    callback(err);\n  };\n  iframe.onerror = function () {\n    debug('onerror', id);\n    completed();\n  };\n  iframe.onload = function () {\n    debug('onload', id);\n    completed();\n  };\n  iframe.onreadystatechange = function (e) {\n    debug('onreadystatechange', id, iframe.readyState, e);\n    if (iframe.readyState === 'complete') {\n      completed();\n    }\n  };\n  return function () {\n    debug('aborted', id);\n    completed(new Error('Aborted'));\n  };\n};", "map": {"version": 3, "names": ["random", "require", "urlUtils", "debug", "process", "env", "NODE_ENV", "form", "area", "createIframe", "id", "global", "document", "createElement", "x", "iframe", "name", "createForm", "style", "display", "position", "method", "enctype", "acceptCharset", "append<PERSON><PERSON><PERSON>", "body", "module", "exports", "url", "payload", "callback", "string", "target", "action", "<PERSON><PERSON><PERSON><PERSON>", "addPath", "value", "e", "submit", "completed", "err", "onerror", "onreadystatechange", "onload", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "readyState", "Error"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/transport/sender/jsonp.js"], "sourcesContent": ["'use strict';\n\nvar random = require('../../utils/random')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:jsonp');\n}\n\nvar form, area;\n\nfunction createIframe(id) {\n  debug('createIframe', id);\n  try {\n    // ie6 dynamic iframes with target=\"\" support (thanks <PERSON>)\n    return global.document.createElement('<iframe name=\"' + id + '\">');\n  } catch (x) {\n    var iframe = global.document.createElement('iframe');\n    iframe.name = id;\n    return iframe;\n  }\n}\n\nfunction createForm() {\n  debug('createForm');\n  form = global.document.createElement('form');\n  form.style.display = 'none';\n  form.style.position = 'absolute';\n  form.method = 'POST';\n  form.enctype = 'application/x-www-form-urlencoded';\n  form.acceptCharset = 'UTF-8';\n\n  area = global.document.createElement('textarea');\n  area.name = 'd';\n  form.appendChild(area);\n\n  global.document.body.appendChild(form);\n}\n\nmodule.exports = function(url, payload, callback) {\n  debug(url, payload);\n  if (!form) {\n    createForm();\n  }\n  var id = 'a' + random.string(8);\n  form.target = id;\n  form.action = urlUtils.addQuery(urlUtils.addPath(url, '/jsonp_send'), 'i=' + id);\n\n  var iframe = createIframe(id);\n  iframe.id = id;\n  iframe.style.display = 'none';\n  form.appendChild(iframe);\n\n  try {\n    area.value = payload;\n  } catch (e) {\n    // seriously broken browsers get here\n  }\n  form.submit();\n\n  var completed = function(err) {\n    debug('completed', id, err);\n    if (!iframe.onerror) {\n      return;\n    }\n    iframe.onreadystatechange = iframe.onerror = iframe.onload = null;\n    // Opera mini doesn't like if we GC iframe\n    // immediately, thus this timeout.\n    setTimeout(function() {\n      debug('cleaning up', id);\n      iframe.parentNode.removeChild(iframe);\n      iframe = null;\n    }, 500);\n    area.value = '';\n    // It is not possible to detect if the iframe succeeded or\n    // failed to submit our form.\n    callback(err);\n  };\n  iframe.onerror = function() {\n    debug('onerror', id);\n    completed();\n  };\n  iframe.onload = function() {\n    debug('onload', id);\n    completed();\n  };\n  iframe.onreadystatechange = function(e) {\n    debug('onreadystatechange', id, iframe.readyState, e);\n    if (iframe.readyState === 'complete') {\n      completed();\n    }\n  };\n  return function() {\n    debug('aborted', id);\n    completed(new Error('Aborted'));\n  };\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EACtCC,QAAQ,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAGzC,IAAIE,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC;AACxD;AAEA,IAAIM,IAAI,EAAEC,IAAI;AAEd,SAASC,YAAYA,CAACC,EAAE,EAAE;EACxBP,KAAK,CAAC,cAAc,EAAEO,EAAE,CAAC;EACzB,IAAI;IACF;IACA,OAAOC,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,GAAGH,EAAE,GAAG,IAAI,CAAC;EACpE,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,IAAIC,MAAM,GAAGJ,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpDE,MAAM,CAACC,IAAI,GAAGN,EAAE;IAChB,OAAOK,MAAM;EACf;AACF;AAEA,SAASE,UAAUA,CAAA,EAAG;EACpBd,KAAK,CAAC,YAAY,CAAC;EACnBI,IAAI,GAAGI,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EAC5CN,IAAI,CAACW,KAAK,CAACC,OAAO,GAAG,MAAM;EAC3BZ,IAAI,CAACW,KAAK,CAACE,QAAQ,GAAG,UAAU;EAChCb,IAAI,CAACc,MAAM,GAAG,MAAM;EACpBd,IAAI,CAACe,OAAO,GAAG,mCAAmC;EAClDf,IAAI,CAACgB,aAAa,GAAG,OAAO;EAE5Bf,IAAI,GAAGG,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;EAChDL,IAAI,CAACQ,IAAI,GAAG,GAAG;EACfT,IAAI,CAACiB,WAAW,CAAChB,IAAI,CAAC;EAEtBG,MAAM,CAACC,QAAQ,CAACa,IAAI,CAACD,WAAW,CAACjB,IAAI,CAAC;AACxC;AAEAmB,MAAM,CAACC,OAAO,GAAG,UAASC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAChD3B,KAAK,CAACyB,GAAG,EAAEC,OAAO,CAAC;EACnB,IAAI,CAACtB,IAAI,EAAE;IACTU,UAAU,CAAC,CAAC;EACd;EACA,IAAIP,EAAE,GAAG,GAAG,GAAGV,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAAC;EAC/BxB,IAAI,CAACyB,MAAM,GAAGtB,EAAE;EAChBH,IAAI,CAAC0B,MAAM,GAAG/B,QAAQ,CAACgC,QAAQ,CAAChC,QAAQ,CAACiC,OAAO,CAACP,GAAG,EAAE,aAAa,CAAC,EAAE,IAAI,GAAGlB,EAAE,CAAC;EAEhF,IAAIK,MAAM,GAAGN,YAAY,CAACC,EAAE,CAAC;EAC7BK,MAAM,CAACL,EAAE,GAAGA,EAAE;EACdK,MAAM,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;EAC7BZ,IAAI,CAACiB,WAAW,CAACT,MAAM,CAAC;EAExB,IAAI;IACFP,IAAI,CAAC4B,KAAK,GAAGP,OAAO;EACtB,CAAC,CAAC,OAAOQ,CAAC,EAAE;IACV;EAAA;EAEF9B,IAAI,CAAC+B,MAAM,CAAC,CAAC;EAEb,IAAIC,SAAS,GAAG,SAAAA,CAASC,GAAG,EAAE;IAC5BrC,KAAK,CAAC,WAAW,EAAEO,EAAE,EAAE8B,GAAG,CAAC;IAC3B,IAAI,CAACzB,MAAM,CAAC0B,OAAO,EAAE;MACnB;IACF;IACA1B,MAAM,CAAC2B,kBAAkB,GAAG3B,MAAM,CAAC0B,OAAO,GAAG1B,MAAM,CAAC4B,MAAM,GAAG,IAAI;IACjE;IACA;IACAC,UAAU,CAAC,YAAW;MACpBzC,KAAK,CAAC,aAAa,EAAEO,EAAE,CAAC;MACxBK,MAAM,CAAC8B,UAAU,CAACC,WAAW,CAAC/B,MAAM,CAAC;MACrCA,MAAM,GAAG,IAAI;IACf,CAAC,EAAE,GAAG,CAAC;IACPP,IAAI,CAAC4B,KAAK,GAAG,EAAE;IACf;IACA;IACAN,QAAQ,CAACU,GAAG,CAAC;EACf,CAAC;EACDzB,MAAM,CAAC0B,OAAO,GAAG,YAAW;IAC1BtC,KAAK,CAAC,SAAS,EAAEO,EAAE,CAAC;IACpB6B,SAAS,CAAC,CAAC;EACb,CAAC;EACDxB,MAAM,CAAC4B,MAAM,GAAG,YAAW;IACzBxC,KAAK,CAAC,QAAQ,EAAEO,EAAE,CAAC;IACnB6B,SAAS,CAAC,CAAC;EACb,CAAC;EACDxB,MAAM,CAAC2B,kBAAkB,GAAG,UAASL,CAAC,EAAE;IACtClC,KAAK,CAAC,oBAAoB,EAAEO,EAAE,EAAEK,MAAM,CAACgC,UAAU,EAAEV,CAAC,CAAC;IACrD,IAAItB,MAAM,CAACgC,UAAU,KAAK,UAAU,EAAE;MACpCR,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EACD,OAAO,YAAW;IAChBpC,KAAK,CAAC,SAAS,EAAEO,EAAE,CAAC;IACpB6B,SAAS,CAAC,IAAIS,KAAK,CAAC,SAAS,CAAC,CAAC;EACjC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}