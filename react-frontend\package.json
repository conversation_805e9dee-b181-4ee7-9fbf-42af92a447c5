{"name": "healthconnect-react", "version": "1.0.0", "private": true, "dependencies": {"@types/node": "^18.15.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "axios": "^1.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "socket.io-client": "^4.6.1", "styled-components": "^5.3.9", "typescript": "^4.9.5", "web-vitals": "^3.1.1", "@types/styled-components": "^5.1.26", "react-hook-form": "^7.43.5", "date-fns": "^2.29.3", "lucide-react": "^0.321.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}