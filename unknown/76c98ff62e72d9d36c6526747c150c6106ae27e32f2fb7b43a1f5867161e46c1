{"ast": null, "code": "'use strict';\n\nrequire('./shims');\nvar URL = require('url-parse'),\n  inherits = require('inherits'),\n  random = require('./utils/random'),\n  escape = require('./utils/escape'),\n  urlUtils = require('./utils/url'),\n  eventUtils = require('./utils/event'),\n  transport = require('./utils/transport'),\n  objectUtils = require('./utils/object'),\n  browser = require('./utils/browser'),\n  log = require('./utils/log'),\n  Event = require('./event/event'),\n  EventTarget = require('./event/eventtarget'),\n  loc = require('./location'),\n  CloseEvent = require('./event/close'),\n  TransportMessageEvent = require('./event/trans-message'),\n  InfoReceiver = require('./info-receiver');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:main');\n}\nvar transports;\n\n// follow constructor steps defined at http://dev.w3.org/html5/websockets/#the-websocket-interface\nfunction SockJS(url, protocols, options) {\n  if (!(this instanceof SockJS)) {\n    return new SockJS(url, protocols, options);\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'SockJS: 1 argument required, but only 0 present\");\n  }\n  EventTarget.call(this);\n  this.readyState = SockJS.CONNECTING;\n  this.extensions = '';\n  this.protocol = '';\n\n  // non-standard extension\n  options = options || {};\n  if (options.protocols_whitelist) {\n    log.warn(\"'protocols_whitelist' is DEPRECATED. Use 'transports' instead.\");\n  }\n  this._transportsWhitelist = options.transports;\n  this._transportOptions = options.transportOptions || {};\n  this._timeout = options.timeout || 0;\n  var sessionId = options.sessionId || 8;\n  if (typeof sessionId === 'function') {\n    this._generateSessionId = sessionId;\n  } else if (typeof sessionId === 'number') {\n    this._generateSessionId = function () {\n      return random.string(sessionId);\n    };\n  } else {\n    throw new TypeError('If sessionId is used in the options, it needs to be a number or a function.');\n  }\n  this._server = options.server || random.numberString(1000);\n\n  // Step 1 of WS spec - parse and validate the url. Issue #8\n  var parsedUrl = new URL(url);\n  if (!parsedUrl.host || !parsedUrl.protocol) {\n    throw new SyntaxError(\"The URL '\" + url + \"' is invalid\");\n  } else if (parsedUrl.hash) {\n    throw new SyntaxError('The URL must not contain a fragment');\n  } else if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {\n    throw new SyntaxError(\"The URL's scheme must be either 'http:' or 'https:'. '\" + parsedUrl.protocol + \"' is not allowed.\");\n  }\n  var secure = parsedUrl.protocol === 'https:';\n  // Step 2 - don't allow secure origin with an insecure protocol\n  if (loc.protocol === 'https:' && !secure) {\n    // exception is *********/8 and ::1 urls\n    if (!urlUtils.isLoopbackAddr(parsedUrl.hostname)) {\n      throw new Error('SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS');\n    }\n  }\n\n  // Step 3 - check port access - no need here\n  // Step 4 - parse protocols argument\n  if (!protocols) {\n    protocols = [];\n  } else if (!Array.isArray(protocols)) {\n    protocols = [protocols];\n  }\n\n  // Step 5 - check protocols argument\n  var sortedProtocols = protocols.sort();\n  sortedProtocols.forEach(function (proto, i) {\n    if (!proto) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is invalid.\");\n    }\n    if (i < sortedProtocols.length - 1 && proto === sortedProtocols[i + 1]) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is duplicated.\");\n    }\n  });\n\n  // Step 6 - convert origin\n  var o = urlUtils.getOrigin(loc.href);\n  this._origin = o ? o.toLowerCase() : null;\n\n  // remove the trailing slash\n  parsedUrl.set('pathname', parsedUrl.pathname.replace(/\\/+$/, ''));\n\n  // store the sanitized url\n  this.url = parsedUrl.href;\n  debug('using url', this.url);\n\n  // Step 7 - start connection in background\n  // obtain server info\n  // http://sockjs.github.io/sockjs-protocol/sockjs-protocol-0.3.3.html#section-26\n  this._urlInfo = {\n    nullOrigin: !browser.hasDomain(),\n    sameOrigin: urlUtils.isOriginEqual(this.url, loc.href),\n    sameScheme: urlUtils.isSchemeEqual(this.url, loc.href)\n  };\n  this._ir = new InfoReceiver(this.url, this._urlInfo);\n  this._ir.once('finish', this._receiveInfo.bind(this));\n}\ninherits(SockJS, EventTarget);\nfunction userSetCode(code) {\n  return code === 1000 || code >= 3000 && code <= 4999;\n}\nSockJS.prototype.close = function (code, reason) {\n  // Step 1\n  if (code && !userSetCode(code)) {\n    throw new Error('InvalidAccessError: Invalid code');\n  }\n  // Step 2.4 states the max is 123 bytes, but we are just checking length\n  if (reason && reason.length > 123) {\n    throw new SyntaxError('reason argument has an invalid length');\n  }\n\n  // Step 3.1\n  if (this.readyState === SockJS.CLOSING || this.readyState === SockJS.CLOSED) {\n    return;\n  }\n\n  // TODO look at docs to determine how to set this\n  var wasClean = true;\n  this._close(code || 1000, reason || 'Normal closure', wasClean);\n};\nSockJS.prototype.send = function (data) {\n  // #13 - convert anything non-string to string\n  // TODO this currently turns objects into [object Object]\n  if (typeof data !== 'string') {\n    data = '' + data;\n  }\n  if (this.readyState === SockJS.CONNECTING) {\n    throw new Error('InvalidStateError: The connection has not been established yet');\n  }\n  if (this.readyState !== SockJS.OPEN) {\n    return;\n  }\n  this._transport.send(escape.quote(data));\n};\nSockJS.version = require('./version');\nSockJS.CONNECTING = 0;\nSockJS.OPEN = 1;\nSockJS.CLOSING = 2;\nSockJS.CLOSED = 3;\nSockJS.prototype._receiveInfo = function (info, rtt) {\n  debug('_receiveInfo', rtt);\n  this._ir = null;\n  if (!info) {\n    this._close(1002, 'Cannot connect to server');\n    return;\n  }\n\n  // establish a round-trip timeout (RTO) based on the\n  // round-trip time (RTT)\n  this._rto = this.countRTO(rtt);\n  // allow server to override url used for the actual transport\n  this._transUrl = info.base_url ? info.base_url : this.url;\n  info = objectUtils.extend(info, this._urlInfo);\n  debug('info', info);\n  // determine list of desired and supported transports\n  var enabledTransports = transports.filterToEnabled(this._transportsWhitelist, info);\n  this._transports = enabledTransports.main;\n  debug(this._transports.length + ' enabled transports');\n  this._connect();\n};\nSockJS.prototype._connect = function () {\n  for (var Transport = this._transports.shift(); Transport; Transport = this._transports.shift()) {\n    debug('attempt', Transport.transportName);\n    if (Transport.needBody) {\n      if (!global.document.body || typeof global.document.readyState !== 'undefined' && global.document.readyState !== 'complete' && global.document.readyState !== 'interactive') {\n        debug('waiting for body');\n        this._transports.unshift(Transport);\n        eventUtils.attachEvent('load', this._connect.bind(this));\n        return;\n      }\n    }\n\n    // calculate timeout based on RTO and round trips. Default to 5s\n    var timeoutMs = Math.max(this._timeout, this._rto * Transport.roundTrips || 5000);\n    this._transportTimeoutId = setTimeout(this._transportTimeout.bind(this), timeoutMs);\n    debug('using timeout', timeoutMs);\n    var transportUrl = urlUtils.addPath(this._transUrl, '/' + this._server + '/' + this._generateSessionId());\n    var options = this._transportOptions[Transport.transportName];\n    debug('transport url', transportUrl);\n    var transportObj = new Transport(transportUrl, this._transUrl, options);\n    transportObj.on('message', this._transportMessage.bind(this));\n    transportObj.once('close', this._transportClose.bind(this));\n    transportObj.transportName = Transport.transportName;\n    this._transport = transportObj;\n    return;\n  }\n  this._close(2000, 'All transports failed', false);\n};\nSockJS.prototype._transportTimeout = function () {\n  debug('_transportTimeout');\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transport) {\n      this._transport.close();\n    }\n    this._transportClose(2007, 'Transport timed out');\n  }\n};\nSockJS.prototype._transportMessage = function (msg) {\n  debug('_transportMessage', msg);\n  var self = this,\n    type = msg.slice(0, 1),\n    content = msg.slice(1),\n    payload;\n\n  // first check for messages that don't need a payload\n  switch (type) {\n    case 'o':\n      this._open();\n      return;\n    case 'h':\n      this.dispatchEvent(new Event('heartbeat'));\n      debug('heartbeat', this.transport);\n      return;\n  }\n  if (content) {\n    try {\n      payload = JSON.parse(content);\n    } catch (e) {\n      debug('bad json', content);\n    }\n  }\n  if (typeof payload === 'undefined') {\n    debug('empty payload', content);\n    return;\n  }\n  switch (type) {\n    case 'a':\n      if (Array.isArray(payload)) {\n        payload.forEach(function (p) {\n          debug('message', self.transport, p);\n          self.dispatchEvent(new TransportMessageEvent(p));\n        });\n      }\n      break;\n    case 'm':\n      debug('message', this.transport, payload);\n      this.dispatchEvent(new TransportMessageEvent(payload));\n      break;\n    case 'c':\n      if (Array.isArray(payload) && payload.length === 2) {\n        this._close(payload[0], payload[1], true);\n      }\n      break;\n  }\n};\nSockJS.prototype._transportClose = function (code, reason) {\n  debug('_transportClose', this.transport, code, reason);\n  if (this._transport) {\n    this._transport.removeAllListeners();\n    this._transport = null;\n    this.transport = null;\n  }\n  if (!userSetCode(code) && code !== 2000 && this.readyState === SockJS.CONNECTING) {\n    this._connect();\n    return;\n  }\n  this._close(code, reason);\n};\nSockJS.prototype._open = function () {\n  debug('_open', this._transport && this._transport.transportName, this.readyState);\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transportTimeoutId) {\n      clearTimeout(this._transportTimeoutId);\n      this._transportTimeoutId = null;\n    }\n    this.readyState = SockJS.OPEN;\n    this.transport = this._transport.transportName;\n    this.dispatchEvent(new Event('open'));\n    debug('connected', this.transport);\n  } else {\n    // The server might have been restarted, and lost track of our\n    // connection.\n    this._close(1006, 'Server lost session');\n  }\n};\nSockJS.prototype._close = function (code, reason, wasClean) {\n  debug('_close', this.transport, code, reason, wasClean, this.readyState);\n  var forceFail = false;\n  if (this._ir) {\n    forceFail = true;\n    this._ir.close();\n    this._ir = null;\n  }\n  if (this._transport) {\n    this._transport.close();\n    this._transport = null;\n    this.transport = null;\n  }\n  if (this.readyState === SockJS.CLOSED) {\n    throw new Error('InvalidStateError: SockJS has already been closed');\n  }\n  this.readyState = SockJS.CLOSING;\n  setTimeout(function () {\n    this.readyState = SockJS.CLOSED;\n    if (forceFail) {\n      this.dispatchEvent(new Event('error'));\n    }\n    var e = new CloseEvent('close');\n    e.wasClean = wasClean || false;\n    e.code = code || 1000;\n    e.reason = reason;\n    this.dispatchEvent(e);\n    this.onmessage = this.onclose = this.onerror = null;\n    debug('disconnected');\n  }.bind(this), 0);\n};\n\n// See: http://www.erg.abdn.ac.uk/~gerrit/dccp/notes/ccid2/rto_estimator/\n// and RFC 2988.\nSockJS.prototype.countRTO = function (rtt) {\n  // In a local environment, when using IE8/9 and the `jsonp-polling`\n  // transport the time needed to establish a connection (the time that pass\n  // from the opening of the transport to the call of `_dispatchOpen`) is\n  // around 200msec (the lower bound used in the article above) and this\n  // causes spurious timeouts. For this reason we calculate a value slightly\n  // larger than that used in the article.\n  if (rtt > 100) {\n    return 4 * rtt; // rto > 400msec\n  }\n  return 300 + rtt; // 300msec < rto <= 400msec\n};\nmodule.exports = function (availableTransports) {\n  transports = transport(availableTransports);\n  require('./iframe-bootstrap')(SockJS, availableTransports);\n  return SockJS;\n};", "map": {"version": 3, "names": ["require", "URL", "inherits", "random", "escape", "urlUtils", "eventUtils", "transport", "objectUtils", "browser", "log", "Event", "EventTarget", "loc", "CloseEvent", "TransportMessageEvent", "InfoReceiver", "debug", "process", "env", "NODE_ENV", "transports", "SockJS", "url", "protocols", "options", "arguments", "length", "TypeError", "call", "readyState", "CONNECTING", "extensions", "protocol", "protocols_whitelist", "warn", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_transportOptions", "transportOptions", "_timeout", "timeout", "sessionId", "_generateSessionId", "string", "_server", "server", "numberString", "parsedUrl", "host", "SyntaxError", "hash", "secure", "isLoopbackAddr", "hostname", "Error", "Array", "isArray", "sortedProtocols", "sort", "for<PERSON>ach", "proto", "i", "o", "<PERSON><PERSON><PERSON><PERSON>", "href", "_origin", "toLowerCase", "set", "pathname", "replace", "_urlInfo", "<PERSON><PERSON><PERSON><PERSON>", "hasDomain", "<PERSON><PERSON><PERSON><PERSON>", "isOriginEqual", "sameScheme", "isSchemeEqual", "_ir", "once", "_receiveInfo", "bind", "userSetCode", "code", "prototype", "close", "reason", "CLOSING", "CLOSED", "<PERSON><PERSON><PERSON>", "_close", "send", "data", "OPEN", "_transport", "quote", "version", "info", "rtt", "_rto", "countRTO", "_transUrl", "base_url", "extend", "enabledTransports", "filterToEnabled", "_transports", "main", "_connect", "Transport", "shift", "transportName", "needBody", "global", "document", "body", "unshift", "attachEvent", "timeoutMs", "Math", "max", "roundTrips", "_transportTimeoutId", "setTimeout", "_transportTimeout", "transportUrl", "addPath", "transportObj", "on", "_transportMessage", "_transportClose", "msg", "self", "type", "slice", "content", "payload", "_open", "dispatchEvent", "JSON", "parse", "e", "p", "removeAllListeners", "clearTimeout", "forceFail", "onmessage", "onclose", "onerror", "module", "exports", "availableTransports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/main.js"], "sourcesContent": ["'use strict';\n\nrequire('./shims');\n\nvar URL = require('url-parse')\n  , inherits = require('inherits')\n  , random = require('./utils/random')\n  , escape = require('./utils/escape')\n  , urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , transport = require('./utils/transport')\n  , objectUtils = require('./utils/object')\n  , browser = require('./utils/browser')\n  , log = require('./utils/log')\n  , Event = require('./event/event')\n  , EventTarget = require('./event/eventtarget')\n  , loc = require('./location')\n  , CloseEvent = require('./event/close')\n  , TransportMessageEvent = require('./event/trans-message')\n  , InfoReceiver = require('./info-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:main');\n}\n\nvar transports;\n\n// follow constructor steps defined at http://dev.w3.org/html5/websockets/#the-websocket-interface\nfunction SockJS(url, protocols, options) {\n  if (!(this instanceof SockJS)) {\n    return new SockJS(url, protocols, options);\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'SockJS: 1 argument required, but only 0 present\");\n  }\n  EventTarget.call(this);\n\n  this.readyState = SockJS.CONNECTING;\n  this.extensions = '';\n  this.protocol = '';\n\n  // non-standard extension\n  options = options || {};\n  if (options.protocols_whitelist) {\n    log.warn(\"'protocols_whitelist' is DEPRECATED. Use 'transports' instead.\");\n  }\n  this._transportsWhitelist = options.transports;\n  this._transportOptions = options.transportOptions || {};\n  this._timeout = options.timeout || 0;\n\n  var sessionId = options.sessionId || 8;\n  if (typeof sessionId === 'function') {\n    this._generateSessionId = sessionId;\n  } else if (typeof sessionId === 'number') {\n    this._generateSessionId = function() {\n      return random.string(sessionId);\n    };\n  } else {\n    throw new TypeError('If sessionId is used in the options, it needs to be a number or a function.');\n  }\n\n  this._server = options.server || random.numberString(1000);\n\n  // Step 1 of WS spec - parse and validate the url. Issue #8\n  var parsedUrl = new URL(url);\n  if (!parsedUrl.host || !parsedUrl.protocol) {\n    throw new SyntaxError(\"The URL '\" + url + \"' is invalid\");\n  } else if (parsedUrl.hash) {\n    throw new SyntaxError('The URL must not contain a fragment');\n  } else if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {\n    throw new SyntaxError(\"The URL's scheme must be either 'http:' or 'https:'. '\" + parsedUrl.protocol + \"' is not allowed.\");\n  }\n\n  var secure = parsedUrl.protocol === 'https:';\n  // Step 2 - don't allow secure origin with an insecure protocol\n  if (loc.protocol === 'https:' && !secure) {\n    // exception is *********/8 and ::1 urls\n    if (!urlUtils.isLoopbackAddr(parsedUrl.hostname)) {\n      throw new Error('SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS');\n    }\n  }\n\n  // Step 3 - check port access - no need here\n  // Step 4 - parse protocols argument\n  if (!protocols) {\n    protocols = [];\n  } else if (!Array.isArray(protocols)) {\n    protocols = [protocols];\n  }\n\n  // Step 5 - check protocols argument\n  var sortedProtocols = protocols.sort();\n  sortedProtocols.forEach(function(proto, i) {\n    if (!proto) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is invalid.\");\n    }\n    if (i < (sortedProtocols.length - 1) && proto === sortedProtocols[i + 1]) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is duplicated.\");\n    }\n  });\n\n  // Step 6 - convert origin\n  var o = urlUtils.getOrigin(loc.href);\n  this._origin = o ? o.toLowerCase() : null;\n\n  // remove the trailing slash\n  parsedUrl.set('pathname', parsedUrl.pathname.replace(/\\/+$/, ''));\n\n  // store the sanitized url\n  this.url = parsedUrl.href;\n  debug('using url', this.url);\n\n  // Step 7 - start connection in background\n  // obtain server info\n  // http://sockjs.github.io/sockjs-protocol/sockjs-protocol-0.3.3.html#section-26\n  this._urlInfo = {\n    nullOrigin: !browser.hasDomain()\n  , sameOrigin: urlUtils.isOriginEqual(this.url, loc.href)\n  , sameScheme: urlUtils.isSchemeEqual(this.url, loc.href)\n  };\n\n  this._ir = new InfoReceiver(this.url, this._urlInfo);\n  this._ir.once('finish', this._receiveInfo.bind(this));\n}\n\ninherits(SockJS, EventTarget);\n\nfunction userSetCode(code) {\n  return code === 1000 || (code >= 3000 && code <= 4999);\n}\n\nSockJS.prototype.close = function(code, reason) {\n  // Step 1\n  if (code && !userSetCode(code)) {\n    throw new Error('InvalidAccessError: Invalid code');\n  }\n  // Step 2.4 states the max is 123 bytes, but we are just checking length\n  if (reason && reason.length > 123) {\n    throw new SyntaxError('reason argument has an invalid length');\n  }\n\n  // Step 3.1\n  if (this.readyState === SockJS.CLOSING || this.readyState === SockJS.CLOSED) {\n    return;\n  }\n\n  // TODO look at docs to determine how to set this\n  var wasClean = true;\n  this._close(code || 1000, reason || 'Normal closure', wasClean);\n};\n\nSockJS.prototype.send = function(data) {\n  // #13 - convert anything non-string to string\n  // TODO this currently turns objects into [object Object]\n  if (typeof data !== 'string') {\n    data = '' + data;\n  }\n  if (this.readyState === SockJS.CONNECTING) {\n    throw new Error('InvalidStateError: The connection has not been established yet');\n  }\n  if (this.readyState !== SockJS.OPEN) {\n    return;\n  }\n  this._transport.send(escape.quote(data));\n};\n\nSockJS.version = require('./version');\n\nSockJS.CONNECTING = 0;\nSockJS.OPEN = 1;\nSockJS.CLOSING = 2;\nSockJS.CLOSED = 3;\n\nSockJS.prototype._receiveInfo = function(info, rtt) {\n  debug('_receiveInfo', rtt);\n  this._ir = null;\n  if (!info) {\n    this._close(1002, 'Cannot connect to server');\n    return;\n  }\n\n  // establish a round-trip timeout (RTO) based on the\n  // round-trip time (RTT)\n  this._rto = this.countRTO(rtt);\n  // allow server to override url used for the actual transport\n  this._transUrl = info.base_url ? info.base_url : this.url;\n  info = objectUtils.extend(info, this._urlInfo);\n  debug('info', info);\n  // determine list of desired and supported transports\n  var enabledTransports = transports.filterToEnabled(this._transportsWhitelist, info);\n  this._transports = enabledTransports.main;\n  debug(this._transports.length + ' enabled transports');\n\n  this._connect();\n};\n\nSockJS.prototype._connect = function() {\n  for (var Transport = this._transports.shift(); Transport; Transport = this._transports.shift()) {\n    debug('attempt', Transport.transportName);\n    if (Transport.needBody) {\n      if (!global.document.body ||\n          (typeof global.document.readyState !== 'undefined' &&\n            global.document.readyState !== 'complete' &&\n            global.document.readyState !== 'interactive')) {\n        debug('waiting for body');\n        this._transports.unshift(Transport);\n        eventUtils.attachEvent('load', this._connect.bind(this));\n        return;\n      }\n    }\n\n    // calculate timeout based on RTO and round trips. Default to 5s\n    var timeoutMs = Math.max(this._timeout, (this._rto * Transport.roundTrips) || 5000);\n    this._transportTimeoutId = setTimeout(this._transportTimeout.bind(this), timeoutMs);\n    debug('using timeout', timeoutMs);\n\n    var transportUrl = urlUtils.addPath(this._transUrl, '/' + this._server + '/' + this._generateSessionId());\n    var options = this._transportOptions[Transport.transportName];\n    debug('transport url', transportUrl);\n    var transportObj = new Transport(transportUrl, this._transUrl, options);\n    transportObj.on('message', this._transportMessage.bind(this));\n    transportObj.once('close', this._transportClose.bind(this));\n    transportObj.transportName = Transport.transportName;\n    this._transport = transportObj;\n\n    return;\n  }\n  this._close(2000, 'All transports failed', false);\n};\n\nSockJS.prototype._transportTimeout = function() {\n  debug('_transportTimeout');\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transport) {\n      this._transport.close();\n    }\n\n    this._transportClose(2007, 'Transport timed out');\n  }\n};\n\nSockJS.prototype._transportMessage = function(msg) {\n  debug('_transportMessage', msg);\n  var self = this\n    , type = msg.slice(0, 1)\n    , content = msg.slice(1)\n    , payload\n    ;\n\n  // first check for messages that don't need a payload\n  switch (type) {\n    case 'o':\n      this._open();\n      return;\n    case 'h':\n      this.dispatchEvent(new Event('heartbeat'));\n      debug('heartbeat', this.transport);\n      return;\n  }\n\n  if (content) {\n    try {\n      payload = JSON.parse(content);\n    } catch (e) {\n      debug('bad json', content);\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    debug('empty payload', content);\n    return;\n  }\n\n  switch (type) {\n    case 'a':\n      if (Array.isArray(payload)) {\n        payload.forEach(function(p) {\n          debug('message', self.transport, p);\n          self.dispatchEvent(new TransportMessageEvent(p));\n        });\n      }\n      break;\n    case 'm':\n      debug('message', this.transport, payload);\n      this.dispatchEvent(new TransportMessageEvent(payload));\n      break;\n    case 'c':\n      if (Array.isArray(payload) && payload.length === 2) {\n        this._close(payload[0], payload[1], true);\n      }\n      break;\n  }\n};\n\nSockJS.prototype._transportClose = function(code, reason) {\n  debug('_transportClose', this.transport, code, reason);\n  if (this._transport) {\n    this._transport.removeAllListeners();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (!userSetCode(code) && code !== 2000 && this.readyState === SockJS.CONNECTING) {\n    this._connect();\n    return;\n  }\n\n  this._close(code, reason);\n};\n\nSockJS.prototype._open = function() {\n  debug('_open', this._transport && this._transport.transportName, this.readyState);\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transportTimeoutId) {\n      clearTimeout(this._transportTimeoutId);\n      this._transportTimeoutId = null;\n    }\n    this.readyState = SockJS.OPEN;\n    this.transport = this._transport.transportName;\n    this.dispatchEvent(new Event('open'));\n    debug('connected', this.transport);\n  } else {\n    // The server might have been restarted, and lost track of our\n    // connection.\n    this._close(1006, 'Server lost session');\n  }\n};\n\nSockJS.prototype._close = function(code, reason, wasClean) {\n  debug('_close', this.transport, code, reason, wasClean, this.readyState);\n  var forceFail = false;\n\n  if (this._ir) {\n    forceFail = true;\n    this._ir.close();\n    this._ir = null;\n  }\n  if (this._transport) {\n    this._transport.close();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (this.readyState === SockJS.CLOSED) {\n    throw new Error('InvalidStateError: SockJS has already been closed');\n  }\n\n  this.readyState = SockJS.CLOSING;\n  setTimeout(function() {\n    this.readyState = SockJS.CLOSED;\n\n    if (forceFail) {\n      this.dispatchEvent(new Event('error'));\n    }\n\n    var e = new CloseEvent('close');\n    e.wasClean = wasClean || false;\n    e.code = code || 1000;\n    e.reason = reason;\n\n    this.dispatchEvent(e);\n    this.onmessage = this.onclose = this.onerror = null;\n    debug('disconnected');\n  }.bind(this), 0);\n};\n\n// See: http://www.erg.abdn.ac.uk/~gerrit/dccp/notes/ccid2/rto_estimator/\n// and RFC 2988.\nSockJS.prototype.countRTO = function(rtt) {\n  // In a local environment, when using IE8/9 and the `jsonp-polling`\n  // transport the time needed to establish a connection (the time that pass\n  // from the opening of the transport to the call of `_dispatchOpen`) is\n  // around 200msec (the lower bound used in the article above) and this\n  // causes spurious timeouts. For this reason we calculate a value slightly\n  // larger than that used in the article.\n  if (rtt > 100) {\n    return 4 * rtt; // rto > 400msec\n  }\n  return 300 + rtt; // 300msec < rto <= 400msec\n};\n\nmodule.exports = function(availableTransports) {\n  transports = transport(availableTransports);\n  require('./iframe-bootstrap')(SockJS, availableTransports);\n  return SockJS;\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAAC,SAAS,CAAC;AAElB,IAAIC,GAAG,GAAGD,OAAO,CAAC,WAAW,CAAC;EAC1BE,QAAQ,GAAGF,OAAO,CAAC,UAAU,CAAC;EAC9BG,MAAM,GAAGH,OAAO,CAAC,gBAAgB,CAAC;EAClCI,MAAM,GAAGJ,OAAO,CAAC,gBAAgB,CAAC;EAClCK,QAAQ,GAAGL,OAAO,CAAC,aAAa,CAAC;EACjCM,UAAU,GAAGN,OAAO,CAAC,eAAe,CAAC;EACrCO,SAAS,GAAGP,OAAO,CAAC,mBAAmB,CAAC;EACxCQ,WAAW,GAAGR,OAAO,CAAC,gBAAgB,CAAC;EACvCS,OAAO,GAAGT,OAAO,CAAC,iBAAiB,CAAC;EACpCU,GAAG,GAAGV,OAAO,CAAC,aAAa,CAAC;EAC5BW,KAAK,GAAGX,OAAO,CAAC,eAAe,CAAC;EAChCY,WAAW,GAAGZ,OAAO,CAAC,qBAAqB,CAAC;EAC5Ca,GAAG,GAAGb,OAAO,CAAC,YAAY,CAAC;EAC3Bc,UAAU,GAAGd,OAAO,CAAC,eAAe,CAAC;EACrCe,qBAAqB,GAAGf,OAAO,CAAC,uBAAuB,CAAC;EACxDgB,YAAY,GAAGhB,OAAO,CAAC,iBAAiB,CAAC;AAG7C,IAAIiB,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGjB,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;AAChD;AAEA,IAAIqB,UAAU;;AAEd;AACA,SAASC,MAAMA,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,YAAYH,MAAM,CAAC,EAAE;IAC7B,OAAO,IAAIA,MAAM,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC5C;EACA,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,sEAAsE,CAAC;EAC7F;EACAhB,WAAW,CAACiB,IAAI,CAAC,IAAI,CAAC;EAEtB,IAAI,CAACC,UAAU,GAAGR,MAAM,CAACS,UAAU;EACnC,IAAI,CAACC,UAAU,GAAG,EAAE;EACpB,IAAI,CAACC,QAAQ,GAAG,EAAE;;EAElB;EACAR,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIA,OAAO,CAACS,mBAAmB,EAAE;IAC/BxB,GAAG,CAACyB,IAAI,CAAC,gEAAgE,CAAC;EAC5E;EACA,IAAI,CAACC,oBAAoB,GAAGX,OAAO,CAACJ,UAAU;EAC9C,IAAI,CAACgB,iBAAiB,GAAGZ,OAAO,CAACa,gBAAgB,IAAI,CAAC,CAAC;EACvD,IAAI,CAACC,QAAQ,GAAGd,OAAO,CAACe,OAAO,IAAI,CAAC;EAEpC,IAAIC,SAAS,GAAGhB,OAAO,CAACgB,SAAS,IAAI,CAAC;EACtC,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;IACnC,IAAI,CAACC,kBAAkB,GAAGD,SAAS;EACrC,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACxC,IAAI,CAACC,kBAAkB,GAAG,YAAW;MACnC,OAAOvC,MAAM,CAACwC,MAAM,CAACF,SAAS,CAAC;IACjC,CAAC;EACH,CAAC,MAAM;IACL,MAAM,IAAIb,SAAS,CAAC,6EAA6E,CAAC;EACpG;EAEA,IAAI,CAACgB,OAAO,GAAGnB,OAAO,CAACoB,MAAM,IAAI1C,MAAM,CAAC2C,YAAY,CAAC,IAAI,CAAC;;EAE1D;EACA,IAAIC,SAAS,GAAG,IAAI9C,GAAG,CAACsB,GAAG,CAAC;EAC5B,IAAI,CAACwB,SAAS,CAACC,IAAI,IAAI,CAACD,SAAS,CAACd,QAAQ,EAAE;IAC1C,MAAM,IAAIgB,WAAW,CAAC,WAAW,GAAG1B,GAAG,GAAG,cAAc,CAAC;EAC3D,CAAC,MAAM,IAAIwB,SAAS,CAACG,IAAI,EAAE;IACzB,MAAM,IAAID,WAAW,CAAC,qCAAqC,CAAC;EAC9D,CAAC,MAAM,IAAIF,SAAS,CAACd,QAAQ,KAAK,OAAO,IAAIc,SAAS,CAACd,QAAQ,KAAK,QAAQ,EAAE;IAC5E,MAAM,IAAIgB,WAAW,CAAC,wDAAwD,GAAGF,SAAS,CAACd,QAAQ,GAAG,mBAAmB,CAAC;EAC5H;EAEA,IAAIkB,MAAM,GAAGJ,SAAS,CAACd,QAAQ,KAAK,QAAQ;EAC5C;EACA,IAAIpB,GAAG,CAACoB,QAAQ,KAAK,QAAQ,IAAI,CAACkB,MAAM,EAAE;IACxC;IACA,IAAI,CAAC9C,QAAQ,CAAC+C,cAAc,CAACL,SAAS,CAACM,QAAQ,CAAC,EAAE;MAChD,MAAM,IAAIC,KAAK,CAAC,iGAAiG,CAAC;IACpH;EACF;;EAEA;EACA;EACA,IAAI,CAAC9B,SAAS,EAAE;IACdA,SAAS,GAAG,EAAE;EAChB,CAAC,MAAM,IAAI,CAAC+B,KAAK,CAACC,OAAO,CAAChC,SAAS,CAAC,EAAE;IACpCA,SAAS,GAAG,CAACA,SAAS,CAAC;EACzB;;EAEA;EACA,IAAIiC,eAAe,GAAGjC,SAAS,CAACkC,IAAI,CAAC,CAAC;EACtCD,eAAe,CAACE,OAAO,CAAC,UAASC,KAAK,EAAEC,CAAC,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV,MAAM,IAAIX,WAAW,CAAC,uBAAuB,GAAGW,KAAK,GAAG,eAAe,CAAC;IAC1E;IACA,IAAIC,CAAC,GAAIJ,eAAe,CAAC9B,MAAM,GAAG,CAAE,IAAIiC,KAAK,KAAKH,eAAe,CAACI,CAAC,GAAG,CAAC,CAAC,EAAE;MACxE,MAAM,IAAIZ,WAAW,CAAC,uBAAuB,GAAGW,KAAK,GAAG,kBAAkB,CAAC;IAC7E;EACF,CAAC,CAAC;;EAEF;EACA,IAAIE,CAAC,GAAGzD,QAAQ,CAAC0D,SAAS,CAAClD,GAAG,CAACmD,IAAI,CAAC;EACpC,IAAI,CAACC,OAAO,GAAGH,CAAC,GAAGA,CAAC,CAACI,WAAW,CAAC,CAAC,GAAG,IAAI;;EAEzC;EACAnB,SAAS,CAACoB,GAAG,CAAC,UAAU,EAAEpB,SAAS,CAACqB,QAAQ,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;;EAEjE;EACA,IAAI,CAAC9C,GAAG,GAAGwB,SAAS,CAACiB,IAAI;EACzB/C,KAAK,CAAC,WAAW,EAAE,IAAI,CAACM,GAAG,CAAC;;EAE5B;EACA;EACA;EACA,IAAI,CAAC+C,QAAQ,GAAG;IACdC,UAAU,EAAE,CAAC9D,OAAO,CAAC+D,SAAS,CAAC,CAAC;IAChCC,UAAU,EAAEpE,QAAQ,CAACqE,aAAa,CAAC,IAAI,CAACnD,GAAG,EAAEV,GAAG,CAACmD,IAAI,CAAC;IACtDW,UAAU,EAAEtE,QAAQ,CAACuE,aAAa,CAAC,IAAI,CAACrD,GAAG,EAAEV,GAAG,CAACmD,IAAI;EACvD,CAAC;EAED,IAAI,CAACa,GAAG,GAAG,IAAI7D,YAAY,CAAC,IAAI,CAACO,GAAG,EAAE,IAAI,CAAC+C,QAAQ,CAAC;EACpD,IAAI,CAACO,GAAG,CAACC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvD;AAEA9E,QAAQ,CAACoB,MAAM,EAAEV,WAAW,CAAC;AAE7B,SAASqE,WAAWA,CAACC,IAAI,EAAE;EACzB,OAAOA,IAAI,KAAK,IAAI,IAAKA,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAI,IAAK;AACxD;AAEA5D,MAAM,CAAC6D,SAAS,CAACC,KAAK,GAAG,UAASF,IAAI,EAAEG,MAAM,EAAE;EAC9C;EACA,IAAIH,IAAI,IAAI,CAACD,WAAW,CAACC,IAAI,CAAC,EAAE;IAC9B,MAAM,IAAI5B,KAAK,CAAC,kCAAkC,CAAC;EACrD;EACA;EACA,IAAI+B,MAAM,IAAIA,MAAM,CAAC1D,MAAM,GAAG,GAAG,EAAE;IACjC,MAAM,IAAIsB,WAAW,CAAC,uCAAuC,CAAC;EAChE;;EAEA;EACA,IAAI,IAAI,CAACnB,UAAU,KAAKR,MAAM,CAACgE,OAAO,IAAI,IAAI,CAACxD,UAAU,KAAKR,MAAM,CAACiE,MAAM,EAAE;IAC3E;EACF;;EAEA;EACA,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAI,CAACC,MAAM,CAACP,IAAI,IAAI,IAAI,EAAEG,MAAM,IAAI,gBAAgB,EAAEG,QAAQ,CAAC;AACjE,CAAC;AAEDlE,MAAM,CAAC6D,SAAS,CAACO,IAAI,GAAG,UAASC,IAAI,EAAE;EACrC;EACA;EACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAG,EAAE,GAAGA,IAAI;EAClB;EACA,IAAI,IAAI,CAAC7D,UAAU,KAAKR,MAAM,CAACS,UAAU,EAAE;IACzC,MAAM,IAAIuB,KAAK,CAAC,gEAAgE,CAAC;EACnF;EACA,IAAI,IAAI,CAACxB,UAAU,KAAKR,MAAM,CAACsE,IAAI,EAAE;IACnC;EACF;EACA,IAAI,CAACC,UAAU,CAACH,IAAI,CAACtF,MAAM,CAAC0F,KAAK,CAACH,IAAI,CAAC,CAAC;AAC1C,CAAC;AAEDrE,MAAM,CAACyE,OAAO,GAAG/F,OAAO,CAAC,WAAW,CAAC;AAErCsB,MAAM,CAACS,UAAU,GAAG,CAAC;AACrBT,MAAM,CAACsE,IAAI,GAAG,CAAC;AACftE,MAAM,CAACgE,OAAO,GAAG,CAAC;AAClBhE,MAAM,CAACiE,MAAM,GAAG,CAAC;AAEjBjE,MAAM,CAAC6D,SAAS,CAACJ,YAAY,GAAG,UAASiB,IAAI,EAAEC,GAAG,EAAE;EAClDhF,KAAK,CAAC,cAAc,EAAEgF,GAAG,CAAC;EAC1B,IAAI,CAACpB,GAAG,GAAG,IAAI;EACf,IAAI,CAACmB,IAAI,EAAE;IACT,IAAI,CAACP,MAAM,CAAC,IAAI,EAAE,0BAA0B,CAAC;IAC7C;EACF;;EAEA;EACA;EACA,IAAI,CAACS,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC;EAC9B;EACA,IAAI,CAACG,SAAS,GAAGJ,IAAI,CAACK,QAAQ,GAAGL,IAAI,CAACK,QAAQ,GAAG,IAAI,CAAC9E,GAAG;EACzDyE,IAAI,GAAGxF,WAAW,CAAC8F,MAAM,CAACN,IAAI,EAAE,IAAI,CAAC1B,QAAQ,CAAC;EAC9CrD,KAAK,CAAC,MAAM,EAAE+E,IAAI,CAAC;EACnB;EACA,IAAIO,iBAAiB,GAAGlF,UAAU,CAACmF,eAAe,CAAC,IAAI,CAACpE,oBAAoB,EAAE4D,IAAI,CAAC;EACnF,IAAI,CAACS,WAAW,GAAGF,iBAAiB,CAACG,IAAI;EACzCzF,KAAK,CAAC,IAAI,CAACwF,WAAW,CAAC9E,MAAM,GAAG,qBAAqB,CAAC;EAEtD,IAAI,CAACgF,QAAQ,CAAC,CAAC;AACjB,CAAC;AAEDrF,MAAM,CAAC6D,SAAS,CAACwB,QAAQ,GAAG,YAAW;EACrC,KAAK,IAAIC,SAAS,GAAG,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC,CAAC,EAAED,SAAS,EAAEA,SAAS,GAAG,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC,CAAC,EAAE;IAC9F5F,KAAK,CAAC,SAAS,EAAE2F,SAAS,CAACE,aAAa,CAAC;IACzC,IAAIF,SAAS,CAACG,QAAQ,EAAE;MACtB,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACC,IAAI,IACpB,OAAOF,MAAM,CAACC,QAAQ,CAACnF,UAAU,KAAK,WAAW,IAChDkF,MAAM,CAACC,QAAQ,CAACnF,UAAU,KAAK,UAAU,IACzCkF,MAAM,CAACC,QAAQ,CAACnF,UAAU,KAAK,aAAc,EAAE;QACnDb,KAAK,CAAC,kBAAkB,CAAC;QACzB,IAAI,CAACwF,WAAW,CAACU,OAAO,CAACP,SAAS,CAAC;QACnCtG,UAAU,CAAC8G,WAAW,CAAC,MAAM,EAAE,IAAI,CAACT,QAAQ,CAAC3B,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD;MACF;IACF;;IAEA;IACA,IAAIqC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChF,QAAQ,EAAG,IAAI,CAAC2D,IAAI,GAAGU,SAAS,CAACY,UAAU,IAAK,IAAI,CAAC;IACnF,IAAI,CAACC,mBAAmB,GAAGC,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC3C,IAAI,CAAC,IAAI,CAAC,EAAEqC,SAAS,CAAC;IACnFpG,KAAK,CAAC,eAAe,EAAEoG,SAAS,CAAC;IAEjC,IAAIO,YAAY,GAAGvH,QAAQ,CAACwH,OAAO,CAAC,IAAI,CAACzB,SAAS,EAAE,GAAG,GAAG,IAAI,CAACxD,OAAO,GAAG,GAAG,GAAG,IAAI,CAACF,kBAAkB,CAAC,CAAC,CAAC;IACzG,IAAIjB,OAAO,GAAG,IAAI,CAACY,iBAAiB,CAACuE,SAAS,CAACE,aAAa,CAAC;IAC7D7F,KAAK,CAAC,eAAe,EAAE2G,YAAY,CAAC;IACpC,IAAIE,YAAY,GAAG,IAAIlB,SAAS,CAACgB,YAAY,EAAE,IAAI,CAACxB,SAAS,EAAE3E,OAAO,CAAC;IACvEqG,YAAY,CAACC,EAAE,CAAC,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAAChD,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D8C,YAAY,CAAChD,IAAI,CAAC,OAAO,EAAE,IAAI,CAACmD,eAAe,CAACjD,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3D8C,YAAY,CAAChB,aAAa,GAAGF,SAAS,CAACE,aAAa;IACpD,IAAI,CAACjB,UAAU,GAAGiC,YAAY;IAE9B;EACF;EACA,IAAI,CAACrC,MAAM,CAAC,IAAI,EAAE,uBAAuB,EAAE,KAAK,CAAC;AACnD,CAAC;AAEDnE,MAAM,CAAC6D,SAAS,CAACwC,iBAAiB,GAAG,YAAW;EAC9C1G,KAAK,CAAC,mBAAmB,CAAC;EAC1B,IAAI,IAAI,CAACa,UAAU,KAAKR,MAAM,CAACS,UAAU,EAAE;IACzC,IAAI,IAAI,CAAC8D,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACT,KAAK,CAAC,CAAC;IACzB;IAEA,IAAI,CAAC6C,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC;EACnD;AACF,CAAC;AAED3G,MAAM,CAAC6D,SAAS,CAAC6C,iBAAiB,GAAG,UAASE,GAAG,EAAE;EACjDjH,KAAK,CAAC,mBAAmB,EAAEiH,GAAG,CAAC;EAC/B,IAAIC,IAAI,GAAG,IAAI;IACXC,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACtBC,OAAO,GAAGJ,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;IACtBE,OAAO;;EAGX;EACA,QAAQH,IAAI;IACV,KAAK,GAAG;MACN,IAAI,CAACI,KAAK,CAAC,CAAC;MACZ;IACF,KAAK,GAAG;MACN,IAAI,CAACC,aAAa,CAAC,IAAI9H,KAAK,CAAC,WAAW,CAAC,CAAC;MAC1CM,KAAK,CAAC,WAAW,EAAE,IAAI,CAACV,SAAS,CAAC;MAClC;EACJ;EAEA,IAAI+H,OAAO,EAAE;IACX,IAAI;MACFC,OAAO,GAAGG,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;IAC/B,CAAC,CAAC,OAAOM,CAAC,EAAE;MACV3H,KAAK,CAAC,UAAU,EAAEqH,OAAO,CAAC;IAC5B;EACF;EAEA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAClCtH,KAAK,CAAC,eAAe,EAAEqH,OAAO,CAAC;IAC/B;EACF;EAEA,QAAQF,IAAI;IACV,KAAK,GAAG;MACN,IAAI7E,KAAK,CAACC,OAAO,CAAC+E,OAAO,CAAC,EAAE;QAC1BA,OAAO,CAAC5E,OAAO,CAAC,UAASkF,CAAC,EAAE;UAC1B5H,KAAK,CAAC,SAAS,EAAEkH,IAAI,CAAC5H,SAAS,EAAEsI,CAAC,CAAC;UACnCV,IAAI,CAACM,aAAa,CAAC,IAAI1H,qBAAqB,CAAC8H,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC;MACJ;MACA;IACF,KAAK,GAAG;MACN5H,KAAK,CAAC,SAAS,EAAE,IAAI,CAACV,SAAS,EAAEgI,OAAO,CAAC;MACzC,IAAI,CAACE,aAAa,CAAC,IAAI1H,qBAAqB,CAACwH,OAAO,CAAC,CAAC;MACtD;IACF,KAAK,GAAG;MACN,IAAIhF,KAAK,CAACC,OAAO,CAAC+E,OAAO,CAAC,IAAIA,OAAO,CAAC5G,MAAM,KAAK,CAAC,EAAE;QAClD,IAAI,CAAC8D,MAAM,CAAC8C,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;MAC3C;MACA;EACJ;AACF,CAAC;AAEDjH,MAAM,CAAC6D,SAAS,CAAC8C,eAAe,GAAG,UAAS/C,IAAI,EAAEG,MAAM,EAAE;EACxDpE,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAACV,SAAS,EAAE2E,IAAI,EAAEG,MAAM,CAAC;EACtD,IAAI,IAAI,CAACQ,UAAU,EAAE;IACnB,IAAI,CAACA,UAAU,CAACiD,kBAAkB,CAAC,CAAC;IACpC,IAAI,CAACjD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACtF,SAAS,GAAG,IAAI;EACvB;EAEA,IAAI,CAAC0E,WAAW,CAACC,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,IAAI,IAAI,CAACpD,UAAU,KAAKR,MAAM,CAACS,UAAU,EAAE;IAChF,IAAI,CAAC4E,QAAQ,CAAC,CAAC;IACf;EACF;EAEA,IAAI,CAAClB,MAAM,CAACP,IAAI,EAAEG,MAAM,CAAC;AAC3B,CAAC;AAED/D,MAAM,CAAC6D,SAAS,CAACqD,KAAK,GAAG,YAAW;EAClCvH,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC4E,UAAU,IAAI,IAAI,CAACA,UAAU,CAACiB,aAAa,EAAE,IAAI,CAAChF,UAAU,CAAC;EACjF,IAAI,IAAI,CAACA,UAAU,KAAKR,MAAM,CAACS,UAAU,EAAE;IACzC,IAAI,IAAI,CAAC0F,mBAAmB,EAAE;MAC5BsB,YAAY,CAAC,IAAI,CAACtB,mBAAmB,CAAC;MACtC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IACjC;IACA,IAAI,CAAC3F,UAAU,GAAGR,MAAM,CAACsE,IAAI;IAC7B,IAAI,CAACrF,SAAS,GAAG,IAAI,CAACsF,UAAU,CAACiB,aAAa;IAC9C,IAAI,CAAC2B,aAAa,CAAC,IAAI9H,KAAK,CAAC,MAAM,CAAC,CAAC;IACrCM,KAAK,CAAC,WAAW,EAAE,IAAI,CAACV,SAAS,CAAC;EACpC,CAAC,MAAM;IACL;IACA;IACA,IAAI,CAACkF,MAAM,CAAC,IAAI,EAAE,qBAAqB,CAAC;EAC1C;AACF,CAAC;AAEDnE,MAAM,CAAC6D,SAAS,CAACM,MAAM,GAAG,UAASP,IAAI,EAAEG,MAAM,EAAEG,QAAQ,EAAE;EACzDvE,KAAK,CAAC,QAAQ,EAAE,IAAI,CAACV,SAAS,EAAE2E,IAAI,EAAEG,MAAM,EAAEG,QAAQ,EAAE,IAAI,CAAC1D,UAAU,CAAC;EACxE,IAAIkH,SAAS,GAAG,KAAK;EAErB,IAAI,IAAI,CAACnE,GAAG,EAAE;IACZmE,SAAS,GAAG,IAAI;IAChB,IAAI,CAACnE,GAAG,CAACO,KAAK,CAAC,CAAC;IAChB,IAAI,CAACP,GAAG,GAAG,IAAI;EACjB;EACA,IAAI,IAAI,CAACgB,UAAU,EAAE;IACnB,IAAI,CAACA,UAAU,CAACT,KAAK,CAAC,CAAC;IACvB,IAAI,CAACS,UAAU,GAAG,IAAI;IACtB,IAAI,CAACtF,SAAS,GAAG,IAAI;EACvB;EAEA,IAAI,IAAI,CAACuB,UAAU,KAAKR,MAAM,CAACiE,MAAM,EAAE;IACrC,MAAM,IAAIjC,KAAK,CAAC,mDAAmD,CAAC;EACtE;EAEA,IAAI,CAACxB,UAAU,GAAGR,MAAM,CAACgE,OAAO;EAChCoC,UAAU,CAAC,YAAW;IACpB,IAAI,CAAC5F,UAAU,GAAGR,MAAM,CAACiE,MAAM;IAE/B,IAAIyD,SAAS,EAAE;MACb,IAAI,CAACP,aAAa,CAAC,IAAI9H,KAAK,CAAC,OAAO,CAAC,CAAC;IACxC;IAEA,IAAIiI,CAAC,GAAG,IAAI9H,UAAU,CAAC,OAAO,CAAC;IAC/B8H,CAAC,CAACpD,QAAQ,GAAGA,QAAQ,IAAI,KAAK;IAC9BoD,CAAC,CAAC1D,IAAI,GAAGA,IAAI,IAAI,IAAI;IACrB0D,CAAC,CAACvD,MAAM,GAAGA,MAAM;IAEjB,IAAI,CAACoD,aAAa,CAACG,CAAC,CAAC;IACrB,IAAI,CAACK,SAAS,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI;IACnDlI,KAAK,CAAC,cAAc,CAAC;EACvB,CAAC,CAAC+D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC;;AAED;AACA;AACA1D,MAAM,CAAC6D,SAAS,CAACgB,QAAQ,GAAG,UAASF,GAAG,EAAE;EACxC;EACA;EACA;EACA;EACA;EACA;EACA,IAAIA,GAAG,GAAG,GAAG,EAAE;IACb,OAAO,CAAC,GAAGA,GAAG,CAAC,CAAC;EAClB;EACA,OAAO,GAAG,GAAGA,GAAG,CAAC,CAAC;AACpB,CAAC;AAEDmD,MAAM,CAACC,OAAO,GAAG,UAASC,mBAAmB,EAAE;EAC7CjI,UAAU,GAAGd,SAAS,CAAC+I,mBAAmB,CAAC;EAC3CtJ,OAAO,CAAC,oBAAoB,CAAC,CAACsB,MAAM,EAAEgI,mBAAmB,CAAC;EAC1D,OAAOhI,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}