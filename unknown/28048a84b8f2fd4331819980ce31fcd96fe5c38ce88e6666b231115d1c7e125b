{"ast": null, "code": "/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n   *\n   * You will be creating an instance of this class if you want to override\n   * supported versions to be declared during STOMP handshake.\n   */\n  constructor(versions) {\n    this.versions = versions;\n  }\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n  supportedVersions() {\n    return this.versions.join(',');\n  }\n  /**\n   * Used while creating a WebSocket\n   */\n  protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n}\n/**\n * Indicates protocol version 1.0\n */\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\nVersions.default = new Versions([Versions.V1_2, Versions.V1_1, Versions.V1_0]);", "map": {"version": 3, "names": ["Versions", "constructor", "versions", "supportedVersions", "join", "protocolVersions", "map", "x", "replace", "V1_0", "V1_1", "V1_2", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\versions.ts"], "sourcesContent": ["/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Indicates protocol version 1.0\n   */\n  public static V1_0 = '1.0';\n  /**\n   * Indicates protocol version 1.1\n   */\n  public static V1_1 = '1.1';\n  /**\n   * Indicates protocol version 1.2\n   */\n  public static V1_2 = '1.2';\n\n  /**\n   * @internal\n   */\n  public static default = new Versions([\n    Versions.V1_2,\n    Versions.V1_1,\n    Versions.V1_0,\n  ]);\n\n  /**\n   * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n   *\n   * You will be creating an instance of this class if you want to override\n   * supported versions to be declared during STOMP handshake.\n   */\n  constructor(public versions: string[]) {}\n\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n  public supportedVersions() {\n    return this.versions.join(',');\n  }\n\n  /**\n   * Used while creating a WebSocket\n   */\n  public protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n}\n"], "mappings": "AAAA;;;;;AAKA,OAAM,MAAOA,QAAQ;EAuBnB;;;;;;EAMAC,YAAmBC,QAAkB;IAAlB,KAAAA,QAAQ,GAARA,QAAQ;EAAa;EAExC;;;EAGOC,iBAAiBA,CAAA;IACtB,OAAO,IAAI,CAACD,QAAQ,CAACE,IAAI,CAAC,GAAG,CAAC;EAChC;EAEA;;;EAGOC,gBAAgBA,CAAA;IACrB,OAAO,IAAI,CAACH,QAAQ,CAACI,GAAG,CAACC,CAAC,IAAI,IAAIA,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,QAAQ,CAAC;EAC/D;;AA1CA;;;AAGcR,QAAA,CAAAS,IAAI,GAAG,KAAK;AAC1B;;;AAGcT,QAAA,CAAAU,IAAI,GAAG,KAAK;AAC1B;;;AAGcV,QAAA,CAAAW,IAAI,GAAG,KAAK;AAE1B;;;AAGcX,QAAA,CAAAY,OAAO,GAAG,IAAIZ,QAAQ,CAAC,CACnCA,QAAQ,CAACW,IAAI,EACbX,QAAQ,CAACU,IAAI,EACbV,QAAQ,CAACS,IAAI,CACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}