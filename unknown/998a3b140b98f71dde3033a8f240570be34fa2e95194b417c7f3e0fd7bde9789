{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  XhrReceiver = require('./receiver/xhr'),\n  XHRCorsObject = require('./sender/xhr-cors'),\n  XHRLocalObject = require('./sender/xhr-local'),\n  browser = require('../utils/browser');\nfunction XhrStreamingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XHRCorsObject);\n}\ninherits(XhrStreamingTransport, AjaxBasedTransport);\nXhrStreamingTransport.enabled = function (info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n  // Opera doesn't support xhr-streaming #60\n  // But it might be able to #92\n  if (browser.isOpera()) {\n    return false;\n  }\n  return XHRCorsObject.enabled;\n};\nXhrStreamingTransport.transportName = 'xhr-streaming';\nXhrStreamingTransport.roundTrips = 2; // preflight, ajax\n\n// <PERSON><PERSON> gets confused when a streaming ajax request is started\n// before onload. This causes the load indicator to spin indefinetely.\n// Only require body when used in a browser\nXhrStreamingTransport.needBody = !!global.document;\nmodule.exports = XhrStreamingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XhrReceiver", "XHRCorsObject", "XHRLocalObject", "browser", "XhrStreamingTransport", "transUrl", "enabled", "Error", "call", "info", "<PERSON><PERSON><PERSON><PERSON>", "isOpera", "transportName", "roundTrips", "needBody", "global", "document", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/transport/xhr-streaming.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , browser = require('../utils/browser')\n  ;\n\nfunction XhrStreamingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrStreamingTransport, AjaxBasedTransport);\n\nXhrStreamingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n  // Opera doesn't support xhr-streaming #60\n  // But it might be able to #92\n  if (browser.isOpera()) {\n    return false;\n  }\n\n  return XHRCorsObject.enabled;\n};\n\nXhrStreamingTransport.transportName = 'xhr-streaming';\nXhrStreamingTransport.roundTrips = 2; // preflight, ajax\n\n// <PERSON><PERSON> gets confused when a streaming ajax request is started\n// before onload. This causes the load indicator to spin indefinetely.\n// Only require body when used in a browser\nXhrStreamingTransport.needBody = !!global.document;\n\nmodule.exports = XhrStreamingTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,kBAAkB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;EAChDE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;EACvCG,aAAa,GAAGH,OAAO,CAAC,mBAAmB,CAAC;EAC5CI,cAAc,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;EAC9CK,OAAO,GAAGL,OAAO,CAAC,kBAAkB,CAAC;AAGzC,SAASM,qBAAqBA,CAACC,QAAQ,EAAE;EACvC,IAAI,CAACH,cAAc,CAACI,OAAO,IAAI,CAACL,aAAa,CAACK,OAAO,EAAE;IACrD,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACAR,kBAAkB,CAACS,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAE,gBAAgB,EAAEL,WAAW,EAAEC,aAAa,CAAC;AACvF;AAEAJ,QAAQ,CAACO,qBAAqB,EAAEL,kBAAkB,CAAC;AAEnDK,qBAAqB,CAACE,OAAO,GAAG,UAASG,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACC,UAAU,EAAE;IACnB,OAAO,KAAK;EACd;EACA;EACA;EACA,IAAIP,OAAO,CAACQ,OAAO,CAAC,CAAC,EAAE;IACrB,OAAO,KAAK;EACd;EAEA,OAAOV,aAAa,CAACK,OAAO;AAC9B,CAAC;AAEDF,qBAAqB,CAACQ,aAAa,GAAG,eAAe;AACrDR,qBAAqB,CAACS,UAAU,GAAG,CAAC,CAAC,CAAC;;AAEtC;AACA;AACA;AACAT,qBAAqB,CAACU,QAAQ,GAAG,CAAC,CAACC,MAAM,CAACC,QAAQ;AAElDC,MAAM,CAACC,OAAO,GAAGd,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}