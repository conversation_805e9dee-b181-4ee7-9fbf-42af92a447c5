{"ast": null, "code": "/**\n * Possible states for the IStompSocket\n */\nexport var StompSocketState;\n(function (StompSocketState) {\n  StompSocketState[StompSocketState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n  StompSocketState[StompSocketState[\"OPEN\"] = 1] = \"OPEN\";\n  StompSocketState[StompSocketState[\"CLOSING\"] = 2] = \"CLOSING\";\n  StompSocketState[StompSocketState[\"CLOSED\"] = 3] = \"CLOSED\";\n})(StompSocketState || (StompSocketState = {}));\n/**\n * Possible activation state\n */\nexport var ActivationState;\n(function (ActivationState) {\n  ActivationState[ActivationState[\"ACTIVE\"] = 0] = \"ACTIVE\";\n  ActivationState[ActivationState[\"DEACTIVATING\"] = 1] = \"DEACTIVATING\";\n  ActivationState[ActivationState[\"INACTIVE\"] = 2] = \"INACTIVE\";\n})(ActivationState || (ActivationState = {}));\n/**\n * Possible reconnection wait time modes\n */\nexport var ReconnectionTimeMode;\n(function (ReconnectionTimeMode) {\n  ReconnectionTimeMode[ReconnectionTimeMode[\"LINEAR\"] = 0] = \"LINEAR\";\n  ReconnectionTimeMode[ReconnectionTimeMode[\"EXPONENTIAL\"] = 1] = \"EXPONENTIAL\";\n})(ReconnectionTimeMode || (ReconnectionTimeMode = {}));\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nexport var TickerStrategy;\n(function (TickerStrategy) {\n  TickerStrategy[\"Interval\"] = \"interval\";\n  TickerStrategy[\"Worker\"] = \"worker\";\n})(TickerStrategy || (TickerStrategy = {}));", "map": {"version": 3, "names": ["StompSocketState", "ActivationState", "ReconnectionTimeMode", "TickerStrategy"], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\types.ts"], "sourcesContent": ["import type { IFrame } from './i-frame.js';\nimport type { IMessage } from './i-message.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { Versions } from './versions.js';\n\n/**\n * This callback will receive a `string` as a parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type debugFnType = (msg: string) => void;\n\n/**\n * This callback will receive a {@link IMessage} as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type messageCallbackType = (message: IMessage) => void;\n\n/**\n * This callback will receive a {@link IFrame} as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type frameCallbackType = ((frame: IFrame) => void) | (() => void);\n\n/**\n * This callback will receive a [CloseEvent]{@link https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent}\n * as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type closeEventCallbackType<T = any> = (evt: T) => void;\n\n/**\n * This callback will receive an [Event]{@link https://developer.mozilla.org/en-US/docs/Web/API/Event}\n * as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type wsErrorCallbackType<T = any> = (evt: T) => void;\n\n/**\n * Parameters for [Client#publish]{@link Client#publish}.\n * Aliased as publishParams as well.\n *\n * Part of `@stomp/stompjs`.\n */\nexport interface IPublishParams {\n  /**\n   * destination end point\n   */\n  destination: string;\n  /**\n   * headers (optional)\n   */\n  headers?: StompHeaders;\n  /**\n   * body (optional)\n   */\n  body?: string;\n  /**\n   * binary body (optional)\n   */\n  binaryBody?: Uint8Array;\n  /**\n   * By default, a `content-length` header will be added in the Frame to the broker.\n   * Set it to `true` for the header to be skipped.\n   */\n  skipContentLengthHeader?: boolean;\n}\n\n/**\n * Backward compatibility, switch to {@link IPublishParams}.\n */\nexport type publishParams = IPublishParams;\n\n/**\n * Used in {@link IRawFrameType}\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport type RawHeaderType = [string, string];\n\n/**\n * The parser yield frames in this structure\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport interface IRawFrameType {\n  command: string | undefined;\n  headers: RawHeaderType[];\n  binaryBody: Uint8Array | undefined;\n}\n\n/**\n * @internal\n */\nexport interface IStompSocketMessageEvent {\n  data?: string | ArrayBuffer;\n}\n\n/**\n * Copied from Websocket interface to avoid dom typelib dependency.\n *\n * @internal\n */\nexport interface IStompSocket {\n  url: string;\n  onclose: ((ev?: any) => any) | undefined | null;\n  onerror: ((ev: any) => any) | undefined | null;\n  onmessage: ((ev: IStompSocketMessageEvent) => any) | undefined | null;\n  onopen: ((ev?: any) => any) | undefined | null;\n  terminate?: (() => any) | undefined | null;\n\n  /**\n   * Returns a string that indicates how binary data from the socket is exposed to scripts:\n   * We support only 'arraybuffer'.\n   */\n  binaryType?: string;\n\n  /**\n   * Returns the state of the socket connection. It can have the values of StompSocketState.\n   */\n  readonly readyState: number;\n\n  /**\n   * Closes the connection.\n   */\n  close(): void;\n  /**\n   * Transmits data using the connection. data can be a string or an ArrayBuffer.\n   */\n  send(data: string | ArrayBuffer): void;\n}\n\n/**\n * Possible states for the IStompSocket\n */\nexport enum StompSocketState {\n  CONNECTING,\n  OPEN,\n  CLOSING,\n  CLOSED,\n}\n\n/**\n * Possible activation state\n */\nexport enum ActivationState {\n  ACTIVE,\n  DEACTIVATING,\n  INACTIVE,\n}\n\n/**\n * Possible reconnection wait time modes\n */\nexport enum ReconnectionTimeMode {\n  LINEAR,\n  EXPONENTIAL\n}\n\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nexport enum TickerStrategy {\n  Interval = 'interval',\n  Worker = 'worker'\n}\n\n/**\n * @internal\n */\nexport interface IStomptHandlerConfig {\n  debug: debugFnType;\n  stompVersions: Versions;\n  connectHeaders: StompHeaders;\n  disconnectHeaders: StompHeaders;\n  heartbeatIncoming: number;\n  heartbeatOutgoing: number;\n  heartbeatStrategy: TickerStrategy;\n  splitLargeFrames: boolean;\n  maxWebSocketChunkSize: number;\n  forceBinaryWSFrames: boolean;\n  logRawCommunication: boolean;\n  appendMissingNULLonIncoming: boolean;\n  discardWebsocketOnCommFailure: boolean;\n  onConnect: frameCallbackType;\n  onDisconnect: frameCallbackType;\n  onStompError: frameCallbackType;\n  onWebSocketClose: closeEventCallbackType;\n  onWebSocketError: wsErrorCallbackType;\n  onUnhandledMessage: messageCallbackType;\n  onUnhandledReceipt: frameCallbackType;\n  onUnhandledFrame: frameCallbackType;\n}\n"], "mappings": "AA4IA;;;AAGA,WAAYA,gBAKX;AALD,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,kCAAU;EACVA,gBAAA,CAAAA,gBAAA,sBAAI;EACJA,gBAAA,CAAAA,gBAAA,4BAAO;EACPA,gBAAA,CAAAA,gBAAA,0BAAM;AACR,CAAC,EALWA,gBAAgB,KAAhBA,gBAAgB;AAO5B;;;AAGA,WAAYC,eAIX;AAJD,WAAYA,eAAe;EACzBA,eAAA,CAAAA,eAAA,0BAAM;EACNA,eAAA,CAAAA,eAAA,sCAAY;EACZA,eAAA,CAAAA,eAAA,8BAAQ;AACV,CAAC,EAJWA,eAAe,KAAfA,eAAe;AAM3B;;;AAGA,WAAYC,oBAGX;AAHD,WAAYA,oBAAoB;EAC9BA,oBAAA,CAAAA,oBAAA,0BAAM;EACNA,oBAAA,CAAAA,oBAAA,oCAAW;AACb,CAAC,EAHWA,oBAAoB,KAApBA,oBAAoB;AAKhC;;;AAGA,WAAYC,cAGX;AAHD,WAAYA,cAAc;EACxBA,cAAA,yBAAqB;EACrBA,cAAA,qBAAiB;AACnB,CAAC,EAHWA,cAAc,KAAdA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}