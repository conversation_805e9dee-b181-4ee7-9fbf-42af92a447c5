{"ast": null, "code": "'use strict';\n\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n  if (!port) return false;\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n      return port !== 80;\n    case 'https':\n    case 'wss':\n      return port !== 443;\n    case 'ftp':\n      return port !== 21;\n    case 'gopher':\n      return port !== 70;\n    case 'file':\n      return false;\n  }\n  return port !== 0;\n};", "map": {"version": 3, "names": ["module", "exports", "required", "port", "protocol", "split"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/requires-port/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n\n  if (!port) return false;\n\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n    return port !== 80;\n\n    case 'https':\n    case 'wss':\n    return port !== 443;\n\n    case 'ftp':\n    return port !== 21;\n\n    case 'gopher':\n    return port !== 70;\n\n    case 'file':\n    return false;\n  }\n\n  return port !== 0;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACjDA,QAAQ,GAAGA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACjCF,IAAI,GAAG,CAACA,IAAI;EAEZ,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EAEvB,QAAQC,QAAQ;IACd,KAAK,MAAM;IACX,KAAK,IAAI;MACT,OAAOD,IAAI,KAAK,EAAE;IAElB,KAAK,OAAO;IACZ,KAAK,KAAK;MACV,OAAOA,IAAI,KAAK,GAAG;IAEnB,KAAK,KAAK;MACV,OAAOA,IAAI,KAAK,EAAE;IAElB,KAAK,QAAQ;MACb,OAAOA,IAAI,KAAK,EAAE;IAElB,KAAK,MAAM;MACX,OAAO,KAAK;EACd;EAEA,OAAOA,IAAI,KAAK,CAAC;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}