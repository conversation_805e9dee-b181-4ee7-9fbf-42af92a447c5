{"ast": null, "code": "import { Client } from '../client.js';\nimport { HeartbeatInfo } from './heartbeat-info.js';\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](https://stomp-js.github.io/guide/stompjs/upgrading-stompjs.html)\n */\nexport class CompatClient extends Client {\n  /**\n   * Available for backward compatibility, please shift to using {@link Client}\n   * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   *\n   * **Deprecated**\n   *\n   * @internal\n   */\n  constructor(webSocketFactory) {\n    super();\n    /**\n     * It is no op now. No longer needed. Large packets work out of the box.\n     */\n    this.maxWebSocketFrameSize = 16 * 1024;\n    this._heartbeatInfo = new HeartbeatInfo(this);\n    this.reconnect_delay = 0;\n    this.webSocketFactory = webSocketFactory;\n    // Default from previous version\n    this.debug = (...message) => {\n      console.log(...message);\n    };\n  }\n  _parseConnect(...args) {\n    let closeEventCallback;\n    let connectCallback;\n    let errorCallback;\n    let headers = {};\n    if (args.length < 2) {\n      throw new Error('Connect requires at least 2 arguments');\n    }\n    if (typeof args[1] === 'function') {\n      [headers, connectCallback, errorCallback, closeEventCallback] = args;\n    } else {\n      switch (args.length) {\n        case 6:\n          [headers.login, headers.passcode, connectCallback, errorCallback, closeEventCallback, headers.host] = args;\n          break;\n        default:\n          [headers.login, headers.passcode, connectCallback, errorCallback, closeEventCallback] = args;\n      }\n    }\n    return [headers, connectCallback, errorCallback, closeEventCallback];\n  }\n  /**\n   * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n   *\n   * **Deprecated**\n   *\n   * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n   * version with headers to pass your broker specific options.\n   *\n   * overloads:\n   * - connect(headers, connectCallback)\n   * - connect(headers, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback)\n   * - connect(login, passcode, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n   *\n   * params:\n   * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n   * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n   * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n   * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n   * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  connect(...args) {\n    const out = this._parseConnect(...args);\n    if (out[0]) {\n      this.connectHeaders = out[0];\n    }\n    if (out[1]) {\n      this.onConnect = out[1];\n    }\n    if (out[2]) {\n      this.onStompError = out[2];\n    }\n    if (out[3]) {\n      this.onWebSocketClose = out[3];\n    }\n    super.activate();\n  }\n  /**\n   * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n   *\n   * **Deprecated**\n   *\n   * See:\n   * [Client#onDisconnect]{@link Client#onDisconnect}, and\n   * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  disconnect(disconnectCallback, headers = {}) {\n    if (disconnectCallback) {\n      this.onDisconnect = disconnectCallback;\n    }\n    this.disconnectHeaders = headers;\n    super.deactivate();\n  }\n  /**\n   * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n   *\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations. The headers will, typically, be available to the subscriber.\n   * However, there may be special purpose headers corresponding to your STOMP broker.\n   *\n   *  **Deprecated**, use [Client#publish]{@link Client#publish}\n   *\n   * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n   *\n   * ```javascript\n   *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n   *\n   *        // If you want to send a message with a body, you must also pass the headers argument.\n   *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n   * ```\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  send(destination, headers = {}, body = '') {\n    headers = Object.assign({}, headers);\n    const skipContentLengthHeader = headers['content-length'] === false;\n    if (skipContentLengthHeader) {\n      delete headers['content-length'];\n    }\n    this.publish({\n      destination,\n      headers: headers,\n      body,\n      skipContentLengthHeader\n    });\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   *\n   * **Deprecated**\n   */\n  set reconnect_delay(value) {\n    this.reconnectDelay = value;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n   *\n   * **Deprecated**\n   */\n  get ws() {\n    return this.webSocket;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n   *\n   * **Deprecated**\n   */\n  get version() {\n    return this.connectedVersion;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  get onreceive() {\n    return this.onUnhandledMessage;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  set onreceive(value) {\n    this.onUnhandledMessage = value;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n   *\n   * **Deprecated**\n   */\n  get onreceipt() {\n    return this.onUnhandledReceipt;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   *\n   * **Deprecated**\n   */\n  set onreceipt(value) {\n    this.onUnhandledReceipt = value;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  get heartbeat() {\n    return this._heartbeatInfo;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  set heartbeat(value) {\n    this.heartbeatIncoming = value.incoming;\n    this.heartbeatOutgoing = value.outgoing;\n  }\n}", "map": {"version": 3, "names": ["Client", "HeartbeatInfo", "CompatClient", "constructor", "webSocketFactory", "maxWebSocketFrameSize", "_heartbeatInfo", "reconnect_delay", "debug", "message", "console", "log", "_parseConnect", "args", "closeEventCallback", "connectCallback", "<PERSON><PERSON><PERSON><PERSON>", "headers", "length", "Error", "login", "passcode", "host", "connect", "out", "connectHeaders", "onConnect", "onStompError", "onWebSocketClose", "activate", "disconnect", "disconnectCallback", "onDisconnect", "disconnectHeaders", "deactivate", "send", "destination", "body", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publish", "value", "reconnectDelay", "ws", "webSocket", "version", "connectedVersion", "onreceive", "onUnhandledMessage", "onreceipt", "onUnhandledReceipt", "heartbeat", "heartbeatIncoming", "incoming", "heartbeatOutgoing", "outgoing"], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\compatibility\\compat-client.ts"], "sourcesContent": ["import { Client } from '../client.js';\nimport { StompHeaders } from '../stomp-headers.js';\nimport { frameCallbackType, messageCallbackType } from '../types.js';\nimport { HeartbeatInfo } from './heartbeat-info.js';\n\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](https://stomp-js.github.io/guide/stompjs/upgrading-stompjs.html)\n */\nexport class CompatClient extends Client {\n  /**\n   * It is no op now. No longer needed. Large packets work out of the box.\n   */\n  public maxWebSocketFrameSize: number = 16 * 1024;\n\n  /**\n   * Available for backward compatibility, please shift to using {@link Client}\n   * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   *\n   * **Deprecated**\n   *\n   * @internal\n   */\n  constructor(webSocketFactory: () => any) {\n    super();\n    this.reconnect_delay = 0;\n    this.webSocketFactory = webSocketFactory;\n    // Default from previous version\n    this.debug = (...message: any[]) => {\n      console.log(...message);\n    };\n  }\n\n  private _parseConnect(...args: any[]): any {\n    let closeEventCallback;\n    let connectCallback;\n    let errorCallback;\n    let headers: StompHeaders = {};\n    if (args.length < 2) {\n      throw new Error('Connect requires at least 2 arguments');\n    }\n    if (typeof args[1] === 'function') {\n      [headers, connectCallback, errorCallback, closeEventCallback] = args;\n    } else {\n      switch (args.length) {\n        case 6:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n            headers.host,\n          ] = args;\n          break;\n        default:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n          ] = args;\n      }\n    }\n\n    return [headers, connectCallback, errorCallback, closeEventCallback];\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n   *\n   * **Deprecated**\n   *\n   * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n   * version with headers to pass your broker specific options.\n   *\n   * overloads:\n   * - connect(headers, connectCallback)\n   * - connect(headers, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback)\n   * - connect(login, passcode, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n   *\n   * params:\n   * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n   * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n   * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n   * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n   * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public connect(...args: any[]): void {\n    const out = this._parseConnect(...args);\n\n    if (out[0]) {\n      this.connectHeaders = out[0];\n    }\n    if (out[1]) {\n      this.onConnect = out[1];\n    }\n    if (out[2]) {\n      this.onStompError = out[2];\n    }\n    if (out[3]) {\n      this.onWebSocketClose = out[3];\n    }\n\n    super.activate();\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n   *\n   * **Deprecated**\n   *\n   * See:\n   * [Client#onDisconnect]{@link Client#onDisconnect}, and\n   * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public disconnect(\n    disconnectCallback?: any,\n    headers: StompHeaders = {}\n  ): void {\n    if (disconnectCallback) {\n      this.onDisconnect = disconnectCallback;\n    }\n    this.disconnectHeaders = headers;\n\n    super.deactivate();\n  }\n\n  /**\n   * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n   *\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations. The headers will, typically, be available to the subscriber.\n   * However, there may be special purpose headers corresponding to your STOMP broker.\n   *\n   *  **Deprecated**, use [Client#publish]{@link Client#publish}\n   *\n   * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n   *\n   * ```javascript\n   *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n   *\n   *        // If you want to send a message with a body, you must also pass the headers argument.\n   *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n   * ```\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public send(\n    destination: string,\n    headers: { [key: string]: any } = {},\n    body: string = ''\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    const skipContentLengthHeader = headers['content-length'] === false;\n    if (skipContentLengthHeader) {\n      delete headers['content-length'];\n    }\n    this.publish({\n      destination,\n      headers: headers as StompHeaders,\n      body,\n      skipContentLengthHeader,\n    });\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   *\n   * **Deprecated**\n   */\n  set reconnect_delay(value: number) {\n    this.reconnectDelay = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n   *\n   * **Deprecated**\n   */\n  get ws(): any {\n    return this.webSocket;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n   *\n   * **Deprecated**\n   */\n  get version() {\n    return this.connectedVersion;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  get onreceive(): messageCallbackType {\n    return this.onUnhandledMessage;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  set onreceive(value: messageCallbackType) {\n    this.onUnhandledMessage = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n   *\n   * **Deprecated**\n   */\n  get onreceipt(): frameCallbackType {\n    return this.onUnhandledReceipt;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   *\n   * **Deprecated**\n   */\n  set onreceipt(value: frameCallbackType) {\n    this.onUnhandledReceipt = value;\n  }\n\n  private _heartbeatInfo: HeartbeatInfo = new HeartbeatInfo(this);\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  get heartbeat() {\n    return this._heartbeatInfo;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  set heartbeat(value: { incoming: number; outgoing: number }) {\n    this.heartbeatIncoming = value.incoming;\n    this.heartbeatOutgoing = value.outgoing;\n  }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAGrC,SAASC,aAAa,QAAQ,qBAAqB;AAEnD;;;;;;;;;AASA,OAAM,MAAOC,YAAa,SAAQF,MAAM;EAMtC;;;;;;;;EAQAG,YAAYC,gBAA2B;IACrC,KAAK,EAAE;IAdT;;;IAGO,KAAAC,qBAAqB,GAAW,EAAE,GAAG,IAAI;IAoOxC,KAAAC,cAAc,GAAkB,IAAIL,aAAa,CAAC,IAAI,CAAC;IAxN7D,IAAI,CAACM,eAAe,GAAG,CAAC;IACxB,IAAI,CAACH,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAACI,KAAK,GAAG,CAAC,GAAGC,OAAc,KAAI;MACjCC,OAAO,CAACC,GAAG,CAAC,GAAGF,OAAO,CAAC;IACzB,CAAC;EACH;EAEQG,aAAaA,CAAC,GAAGC,IAAW;IAClC,IAAIC,kBAAkB;IACtB,IAAIC,eAAe;IACnB,IAAIC,aAAa;IACjB,IAAIC,OAAO,GAAiB,EAAE;IAC9B,IAAIJ,IAAI,CAACK,MAAM,GAAG,CAAC,EAAE;MACnB,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IACA,IAAI,OAAON,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;MACjC,CAACI,OAAO,EAAEF,eAAe,EAAEC,aAAa,EAAEF,kBAAkB,CAAC,GAAGD,IAAI;IACtE,CAAC,MAAM;MACL,QAAQA,IAAI,CAACK,MAAM;QACjB,KAAK,CAAC;UACJ,CACED,OAAO,CAACG,KAAK,EACbH,OAAO,CAACI,QAAQ,EAChBN,eAAe,EACfC,aAAa,EACbF,kBAAkB,EAClBG,OAAO,CAACK,IAAI,CACb,GAAGT,IAAI;UACR;QACF;UACE,CACEI,OAAO,CAACG,KAAK,EACbH,OAAO,CAACI,QAAQ,EAChBN,eAAe,EACfC,aAAa,EACbF,kBAAkB,CACnB,GAAGD,IAAI;MACZ;IACF;IAEA,OAAO,CAACI,OAAO,EAAEF,eAAe,EAAEC,aAAa,EAAEF,kBAAkB,CAAC;EACtE;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BOS,OAAOA,CAAC,GAAGV,IAAW;IAC3B,MAAMW,GAAG,GAAG,IAAI,CAACZ,aAAa,CAAC,GAAGC,IAAI,CAAC;IAEvC,IAAIW,GAAG,CAAC,CAAC,CAAC,EAAE;MACV,IAAI,CAACC,cAAc,GAAGD,GAAG,CAAC,CAAC,CAAC;IAC9B;IACA,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE;MACV,IAAI,CAACE,SAAS,GAAGF,GAAG,CAAC,CAAC,CAAC;IACzB;IACA,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE;MACV,IAAI,CAACG,YAAY,GAAGH,GAAG,CAAC,CAAC,CAAC;IAC5B;IACA,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE;MACV,IAAI,CAACI,gBAAgB,GAAGJ,GAAG,CAAC,CAAC,CAAC;IAChC;IAEA,KAAK,CAACK,QAAQ,EAAE;EAClB;EAEA;;;;;;;;;;;EAWOC,UAAUA,CACfC,kBAAwB,EACxBd,OAAA,GAAwB,EAAE;IAE1B,IAAIc,kBAAkB,EAAE;MACtB,IAAI,CAACC,YAAY,GAAGD,kBAAkB;IACxC;IACA,IAAI,CAACE,iBAAiB,GAAGhB,OAAO;IAEhC,KAAK,CAACiB,UAAU,EAAE;EACpB;EAEA;;;;;;;;;;;;;;;;;;;;EAoBOC,IAAIA,CACTC,WAAmB,EACnBnB,OAAA,GAAkC,EAAE,EACpCoB,IAAA,GAAe,EAAE;IAEjBpB,OAAO,GAAIqB,MAAc,CAACC,MAAM,CAAC,EAAE,EAAEtB,OAAO,CAAC;IAE7C,MAAMuB,uBAAuB,GAAGvB,OAAO,CAAC,gBAAgB,CAAC,KAAK,KAAK;IACnE,IAAIuB,uBAAuB,EAAE;MAC3B,OAAOvB,OAAO,CAAC,gBAAgB,CAAC;IAClC;IACA,IAAI,CAACwB,OAAO,CAAC;MACXL,WAAW;MACXnB,OAAO,EAAEA,OAAuB;MAChCoB,IAAI;MACJG;KACD,CAAC;EACJ;EAEA;;;;;EAKA,IAAIjC,eAAeA,CAACmC,KAAa;IAC/B,IAAI,CAACC,cAAc,GAAGD,KAAK;EAC7B;EAEA;;;;;EAKA,IAAIE,EAAEA,CAAA;IACJ,OAAO,IAAI,CAACC,SAAS;EACvB;EAEA;;;;;EAKA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,gBAAgB;EAC9B;EAEA;;;;;EAKA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,kBAAkB;EAChC;EAEA;;;;;EAKA,IAAID,SAASA,CAACN,KAA0B;IACtC,IAAI,CAACO,kBAAkB,GAAGP,KAAK;EACjC;EAEA;;;;;;EAMA,IAAIQ,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,kBAAkB;EAChC;EAEA;;;;;EAKA,IAAID,SAASA,CAACR,KAAwB;IACpC,IAAI,CAACS,kBAAkB,GAAGT,KAAK;EACjC;EAIA;;;;;;EAMA,IAAIU,SAASA,CAAA;IACX,OAAO,IAAI,CAAC9C,cAAc;EAC5B;EAEA;;;;;;EAMA,IAAI8C,SAASA,CAACV,KAA6C;IACzD,IAAI,CAACW,iBAAiB,GAAGX,KAAK,CAACY,QAAQ;IACvC,IAAI,CAACC,iBAAiB,GAAGb,KAAK,CAACc,QAAQ;EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}