{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\i-transaction.ts"], "sourcesContent": ["/**\n * A Transaction is created by calling [Client#begin]{@link Client#begin}\n *\n * Part of `@stomp/stompjs`.\n */\nexport interface ITransaction {\n  /**\n   * You will need to access this to send, ack, or nack within this transaction.\n   */\n  id: string;\n\n  /**\n   * Commit this transaction. See [Client#commit]{@link Client#commit} for an example.\n   */\n  commit: () => void;\n\n  /**\n   * Abort this transaction. See [Client#abort]{@link Client#abort} for an example.\n   */\n  abort: () => void;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}