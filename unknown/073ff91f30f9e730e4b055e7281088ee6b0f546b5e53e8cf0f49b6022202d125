{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new\\\\Meditech\\\\react-frontend\\\\src\\\\components\\\\video\\\\VideoCallComponent.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport styled from 'styled-components';\nimport { Video, VideoOff, Mic, MicOff, Monitor, MonitorOff, PhoneOff, Users, Clock, Maximize2, Minimize2 } from 'lucide-react';\nimport { videoCallService } from '../../services/videoCallService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCallContainer = styled.div`\n  position: ${props => props.isFullscreen ? 'fixed' : 'relative'};\n  top: ${props => props.isFullscreen ? '0' : 'auto'};\n  left: ${props => props.isFullscreen ? '0' : 'auto'};\n  width: ${props => props.isFullscreen ? '100vw' : '100%'};\n  height: ${props => props.isFullscreen ? '100vh' : '600px'};\n  background: #1a1a1a;\n  border-radius: ${props => props.isFullscreen ? '0' : '12px'};\n  overflow: hidden;\n  z-index: ${props => props.isFullscreen ? '9999' : '1'};\n  display: flex;\n  flex-direction: column;\n`;\n_c = VideoCallContainer;\nconst VideoArea = styled.div`\n  flex: 1;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c2 = VideoArea;\nconst RemoteVideo = styled.video`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  background: #2a2a2a;\n`;\n_c3 = RemoteVideo;\nconst LocalVideo = styled.video`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  width: 200px;\n  height: 150px;\n  border-radius: 8px;\n  object-fit: cover;\n  background: #2a2a2a;\n  border: 2px solid #4a5568;\n  z-index: 10;\n`;\n_c4 = LocalVideo;\nconst WaitingMessage = styled.div`\n  color: white;\n  font-size: 18px;\n  text-align: center;\n  padding: 40px;\n`;\n_c5 = WaitingMessage;\nconst ControlsBar = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  padding: 20px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 15px;\n  backdrop-filter: blur(10px);\n`;\n_c6 = ControlsBar;\nconst ControlButton = styled.button`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  background: ${props => props.danger ? '#e53e3e' : props.active ? '#3182ce' : 'rgba(255, 255, 255, 0.1)'};\n  \n  color: white;\n  \n  &:hover {\n    background: ${props => props.danger ? '#c53030' : props.active ? '#2c5aa0' : 'rgba(255, 255, 255, 0.2)'};\n    transform: scale(1.05);\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c7 = ControlButton;\nconst StatusBar = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  color: white;\n  padding: 10px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 14px;\n`;\n_c8 = StatusBar;\nconst ConnectionStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  \n  &::before {\n    content: '';\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: ${props => props.status === 'connected' ? '#48bb78' : props.status === 'connecting' ? '#ed8936' : '#e53e3e'};\n  }\n`;\n_c9 = ConnectionStatus;\nconst ParticipantInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n_c0 = ParticipantInfo;\nconst CallTimer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  font-weight: 500;\n`;\n_c1 = CallTimer;\nconst VideoCallComponent = ({\n  consultation,\n  onCallEnd,\n  onError\n}) => {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionState, setConnectionState] = useState('new');\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isScreenSharing, setIsScreenSharing] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [callDuration, setCallDuration] = useState(0);\n  const [participants, setParticipants] = useState([]);\n  const [isInitializing, setIsInitializing] = useState(true);\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const callStartTimeRef = useRef(null);\n  useEffect(() => {\n    initializeCall();\n    return () => {\n      videoCallService.cleanup();\n    };\n  }, []);\n  useEffect(() => {\n    // Call timer\n    let interval;\n    if (isConnected && callStartTimeRef.current) {\n      interval = setInterval(() => {\n        const now = new Date();\n        const duration = Math.floor((now.getTime() - callStartTimeRef.current.getTime()) / 1000);\n        setCallDuration(duration);\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [isConnected]);\n  const initializeCall = async () => {\n    try {\n      setIsInitializing(true);\n\n      // Connect WebSocket\n      await videoCallService.connectWebSocket();\n\n      // Get user media\n      const stream = await videoCallService.getUserMedia();\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n\n      // Set up event handlers\n      videoCallService.setOnRemoteStream(stream => {\n        if (remoteVideoRef.current) {\n          remoteVideoRef.current.srcObject = stream;\n        }\n        setIsConnected(true);\n        callStartTimeRef.current = new Date();\n      });\n      videoCallService.setOnConnectionStateChange(state => {\n        setConnectionState(state);\n        if (state === 'failed' || state === 'disconnected') {\n          onError('Connection lost. Please try reconnecting.');\n        }\n      });\n      videoCallService.setOnCallEnded(() => {\n        onCallEnd();\n      });\n\n      // Join the room\n      const userRole = getCurrentUserRole();\n      await videoCallService.joinRoom(consultation.roomId, userRole);\n      setIsInitializing(false);\n    } catch (error) {\n      console.error('Error initializing call:', error);\n      onError('Failed to initialize video call. Please check your camera and microphone permissions.');\n      setIsInitializing(false);\n    }\n  };\n  const getCurrentUserRole = () => {\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    return user.role === 'DOCTOR' ? 'DOCTOR' : 'PATIENT';\n  };\n  const handleToggleAudio = () => {\n    const muted = videoCallService.toggleAudio();\n    setIsAudioMuted(muted);\n  };\n  const handleToggleVideo = () => {\n    const muted = videoCallService.toggleVideo();\n    setIsVideoMuted(muted);\n  };\n  const handleToggleScreenShare = async () => {\n    try {\n      if (isScreenSharing) {\n        await videoCallService.stopScreenShare();\n        setIsScreenSharing(false);\n      } else {\n        await videoCallService.startScreenShare();\n        setIsScreenSharing(true);\n      }\n    } catch (error) {\n      console.error('Error toggling screen share:', error);\n      onError('Failed to toggle screen sharing.');\n    }\n  };\n  const handleEndCall = async () => {\n    try {\n      await videoCallService.leaveRoom();\n      onCallEnd();\n    } catch (error) {\n      console.error('Error ending call:', error);\n      onCallEnd(); // End call anyway\n    }\n  };\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getConnectionStatusText = () => {\n    switch (connectionState) {\n      case 'connected':\n        return 'Connected';\n      case 'connecting':\n        return 'Connecting...';\n      case 'disconnected':\n        return 'Disconnected';\n      case 'failed':\n        return 'Connection Failed';\n      default:\n        return 'Initializing...';\n    }\n  };\n  const getConnectionStatusType = () => {\n    switch (connectionState) {\n      case 'connected':\n        return 'connected';\n      case 'connecting':\n        return 'connecting';\n      default:\n        return 'disconnected';\n    }\n  };\n  if (isInitializing) {\n    return /*#__PURE__*/_jsxDEV(VideoCallContainer, {\n      isFullscreen: false,\n      children: /*#__PURE__*/_jsxDEV(VideoArea, {\n        children: /*#__PURE__*/_jsxDEV(WaitingMessage, {\n          children: [\"Initializing video call...\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            children: \"Please allow camera and microphone access\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(VideoCallContainer, {\n    isFullscreen: isFullscreen,\n    children: [/*#__PURE__*/_jsxDEV(StatusBar, {\n      children: [/*#__PURE__*/_jsxDEV(ConnectionStatus, {\n        status: getConnectionStatusType(),\n        children: getConnectionStatusText()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ParticipantInfo, {\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [participants.length + 1, \" participants\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), isConnected && /*#__PURE__*/_jsxDEV(CallTimer, {\n        children: [/*#__PURE__*/_jsxDEV(Clock, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), formatDuration(callDuration)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(VideoArea, {\n      children: [!isConnected ? /*#__PURE__*/_jsxDEV(WaitingMessage, {\n        children: \"Waiting for other participant to join...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(RemoteVideo, {\n        ref: remoteVideoRef,\n        autoPlay: true,\n        playsInline: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(LocalVideo, {\n        ref: localVideoRef,\n        autoPlay: true,\n        playsInline: true,\n        muted: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ControlsBar, {\n      children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n        active: !isAudioMuted,\n        onClick: handleToggleAudio,\n        title: isAudioMuted ? 'Unmute' : 'Mute',\n        children: isAudioMuted ? /*#__PURE__*/_jsxDEV(MicOff, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 27\n        }, this) : /*#__PURE__*/_jsxDEV(Mic, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        active: !isVideoMuted,\n        onClick: handleToggleVideo,\n        title: isVideoMuted ? 'Turn on camera' : 'Turn off camera',\n        children: isVideoMuted ? /*#__PURE__*/_jsxDEV(VideoOff, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 27\n        }, this) : /*#__PURE__*/_jsxDEV(Video, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        active: isScreenSharing,\n        onClick: handleToggleScreenShare,\n        title: isScreenSharing ? 'Stop sharing' : 'Share screen',\n        children: isScreenSharing ? /*#__PURE__*/_jsxDEV(MonitorOff, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(Monitor, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 57\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        onClick: toggleFullscreen,\n        title: isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen',\n        children: isFullscreen ? /*#__PURE__*/_jsxDEV(Minimize2, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 27\n        }, this) : /*#__PURE__*/_jsxDEV(Maximize2, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        danger: true,\n        onClick: handleEndCall,\n        title: \"End call\",\n        children: /*#__PURE__*/_jsxDEV(PhoneOff, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCallComponent, \"kzAs6kgJceVksjy+kJcoPhrLVaw=\");\n_c10 = VideoCallComponent;\nexport default VideoCallComponent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"VideoCallContainer\");\n$RefreshReg$(_c2, \"VideoArea\");\n$RefreshReg$(_c3, \"RemoteVideo\");\n$RefreshReg$(_c4, \"LocalVideo\");\n$RefreshReg$(_c5, \"WaitingMessage\");\n$RefreshReg$(_c6, \"ControlsBar\");\n$RefreshReg$(_c7, \"ControlButton\");\n$RefreshReg$(_c8, \"StatusBar\");\n$RefreshReg$(_c9, \"ConnectionStatus\");\n$RefreshReg$(_c0, \"ParticipantInfo\");\n$RefreshReg$(_c1, \"CallTimer\");\n$RefreshReg$(_c10, \"VideoCallComponent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "styled", "Video", "VideoOff", "Mic", "<PERSON><PERSON><PERSON><PERSON>", "Monitor", "MonitorOff", "PhoneOff", "Users", "Clock", "Maximize2", "Minimize2", "videoCallService", "jsxDEV", "_jsxDEV", "VideoCallContainer", "div", "props", "isFullscreen", "_c", "VideoArea", "_c2", "RemoteVideo", "video", "_c3", "LocalVideo", "_c4", "WaitingMessage", "_c5", "ControlsBar", "_c6", "ControlButton", "button", "danger", "active", "_c7", "StatusBar", "_c8", "ConnectionStatus", "status", "_c9", "ParticipantInfo", "_c0", "CallTimer", "_c1", "VideoCallComponent", "consultation", "onCallEnd", "onError", "_s", "isConnected", "setIsConnected", "connectionState", "setConnectionState", "isAudioMuted", "setIsAudioMuted", "isVideoMuted", "setIsVideoMuted", "isScreenSharing", "setIsScreenSharing", "setIsFullscreen", "callDuration", "setCallDuration", "participants", "setParticipants", "isInitializing", "setIsInitializing", "localVideoRef", "remoteVideoRef", "callStartTimeRef", "initializeCall", "cleanup", "interval", "current", "setInterval", "now", "Date", "duration", "Math", "floor", "getTime", "clearInterval", "connectWebSocket", "stream", "getUserMedia", "srcObject", "setOnRemoteStream", "setOnConnectionStateChange", "state", "setOnCallEnded", "userRole", "getCurrentUserRole", "joinRoom", "roomId", "error", "console", "user", "JSON", "parse", "localStorage", "getItem", "role", "handleToggleAudio", "muted", "toggleAudio", "handleToggleVideo", "toggleVideo", "handleToggleScreenShare", "stopScreenShare", "startScreenShare", "handleEndCall", "leaveRoom", "toggleFullscreen", "formatDuration", "seconds", "mins", "secs", "toString", "padStart", "getConnectionStatusText", "getConnectionStatusType", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "length", "ref", "autoPlay", "playsInline", "onClick", "title", "_c10", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/src/components/video/VideoCallComponent.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport styled from 'styled-components';\nimport { \n  Video, \n  VideoOff, \n  Mic, \n  MicOff, \n  Monitor, \n  MonitorOff, \n  Phone, \n  PhoneOff,\n  MessageCircle,\n  Settings,\n  Users,\n  Clock,\n  Maximize2,\n  Minimize2\n} from 'lucide-react';\nimport { videoCallService, VideoConsultation, CallParticipant } from '../../services/videoCallService';\n\nconst VideoCallContainer = styled.div<{ isFullscreen: boolean }>`\n  position: ${props => props.isFullscreen ? 'fixed' : 'relative'};\n  top: ${props => props.isFullscreen ? '0' : 'auto'};\n  left: ${props => props.isFullscreen ? '0' : 'auto'};\n  width: ${props => props.isFullscreen ? '100vw' : '100%'};\n  height: ${props => props.isFullscreen ? '100vh' : '600px'};\n  background: #1a1a1a;\n  border-radius: ${props => props.isFullscreen ? '0' : '12px'};\n  overflow: hidden;\n  z-index: ${props => props.isFullscreen ? '9999' : '1'};\n  display: flex;\n  flex-direction: column;\n`;\n\nconst VideoArea = styled.div`\n  flex: 1;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst RemoteVideo = styled.video`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  background: #2a2a2a;\n`;\n\nconst LocalVideo = styled.video`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  width: 200px;\n  height: 150px;\n  border-radius: 8px;\n  object-fit: cover;\n  background: #2a2a2a;\n  border: 2px solid #4a5568;\n  z-index: 10;\n`;\n\nconst WaitingMessage = styled.div`\n  color: white;\n  font-size: 18px;\n  text-align: center;\n  padding: 40px;\n`;\n\nconst ControlsBar = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  padding: 20px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 15px;\n  backdrop-filter: blur(10px);\n`;\n\nconst ControlButton = styled.button<{ active?: boolean; danger?: boolean }>`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  background: ${props => \n    props.danger ? '#e53e3e' : \n    props.active ? '#3182ce' : \n    'rgba(255, 255, 255, 0.1)'\n  };\n  \n  color: white;\n  \n  &:hover {\n    background: ${props => \n      props.danger ? '#c53030' : \n      props.active ? '#2c5aa0' : \n      'rgba(255, 255, 255, 0.2)'\n    };\n    transform: scale(1.05);\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst StatusBar = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  color: white;\n  padding: 10px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 14px;\n`;\n\nconst ConnectionStatus = styled.div<{ status: string }>`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  \n  &::before {\n    content: '';\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: ${props => \n      props.status === 'connected' ? '#48bb78' :\n      props.status === 'connecting' ? '#ed8936' :\n      '#e53e3e'\n    };\n  }\n`;\n\nconst ParticipantInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n\nconst CallTimer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  font-weight: 500;\n`;\n\ninterface VideoCallComponentProps {\n  consultation: VideoConsultation;\n  onCallEnd: () => void;\n  onError: (error: string) => void;\n}\n\nconst VideoCallComponent: React.FC<VideoCallComponentProps> = ({\n  consultation,\n  onCallEnd,\n  onError\n}) => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionState, setConnectionState] = useState<RTCPeerConnectionState>('new');\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isScreenSharing, setIsScreenSharing] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [callDuration, setCallDuration] = useState(0);\n  const [participants, setParticipants] = useState<CallParticipant[]>([]);\n  const [isInitializing, setIsInitializing] = useState(true);\n\n  const localVideoRef = useRef<HTMLVideoElement>(null);\n  const remoteVideoRef = useRef<HTMLVideoElement>(null);\n  const callStartTimeRef = useRef<Date | null>(null);\n\n  useEffect(() => {\n    initializeCall();\n    return () => {\n      videoCallService.cleanup();\n    };\n  }, []);\n\n  useEffect(() => {\n    // Call timer\n    let interval: NodeJS.Timeout;\n    if (isConnected && callStartTimeRef.current) {\n      interval = setInterval(() => {\n        const now = new Date();\n        const duration = Math.floor((now.getTime() - callStartTimeRef.current!.getTime()) / 1000);\n        setCallDuration(duration);\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [isConnected]);\n\n  const initializeCall = async () => {\n    try {\n      setIsInitializing(true);\n\n      // Connect WebSocket\n      await videoCallService.connectWebSocket();\n\n      // Get user media\n      const stream = await videoCallService.getUserMedia();\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n\n      // Set up event handlers\n      videoCallService.setOnRemoteStream((stream) => {\n        if (remoteVideoRef.current) {\n          remoteVideoRef.current.srcObject = stream;\n        }\n        setIsConnected(true);\n        callStartTimeRef.current = new Date();\n      });\n\n      videoCallService.setOnConnectionStateChange((state) => {\n        setConnectionState(state);\n        if (state === 'failed' || state === 'disconnected') {\n          onError('Connection lost. Please try reconnecting.');\n        }\n      });\n\n      videoCallService.setOnCallEnded(() => {\n        onCallEnd();\n      });\n\n      // Join the room\n      const userRole = getCurrentUserRole();\n      await videoCallService.joinRoom(consultation.roomId, userRole);\n\n      setIsInitializing(false);\n    } catch (error) {\n      console.error('Error initializing call:', error);\n      onError('Failed to initialize video call. Please check your camera and microphone permissions.');\n      setIsInitializing(false);\n    }\n  };\n\n  const getCurrentUserRole = (): 'DOCTOR' | 'PATIENT' => {\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    return user.role === 'DOCTOR' ? 'DOCTOR' : 'PATIENT';\n  };\n\n  const handleToggleAudio = () => {\n    const muted = videoCallService.toggleAudio();\n    setIsAudioMuted(muted);\n  };\n\n  const handleToggleVideo = () => {\n    const muted = videoCallService.toggleVideo();\n    setIsVideoMuted(muted);\n  };\n\n  const handleToggleScreenShare = async () => {\n    try {\n      if (isScreenSharing) {\n        await videoCallService.stopScreenShare();\n        setIsScreenSharing(false);\n      } else {\n        await videoCallService.startScreenShare();\n        setIsScreenSharing(true);\n      }\n    } catch (error) {\n      console.error('Error toggling screen share:', error);\n      onError('Failed to toggle screen sharing.');\n    }\n  };\n\n  const handleEndCall = async () => {\n    try {\n      await videoCallService.leaveRoom();\n      onCallEnd();\n    } catch (error) {\n      console.error('Error ending call:', error);\n      onCallEnd(); // End call anyway\n    }\n  };\n\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  const formatDuration = (seconds: number): string => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getConnectionStatusText = (): string => {\n    switch (connectionState) {\n      case 'connected': return 'Connected';\n      case 'connecting': return 'Connecting...';\n      case 'disconnected': return 'Disconnected';\n      case 'failed': return 'Connection Failed';\n      default: return 'Initializing...';\n    }\n  };\n\n  const getConnectionStatusType = (): string => {\n    switch (connectionState) {\n      case 'connected': return 'connected';\n      case 'connecting': return 'connecting';\n      default: return 'disconnected';\n    }\n  };\n\n  if (isInitializing) {\n    return (\n      <VideoCallContainer isFullscreen={false}>\n        <VideoArea>\n          <WaitingMessage>\n            Initializing video call...\n            <br />\n            <small>Please allow camera and microphone access</small>\n          </WaitingMessage>\n        </VideoArea>\n      </VideoCallContainer>\n    );\n  }\n\n  return (\n    <VideoCallContainer isFullscreen={isFullscreen}>\n      <StatusBar>\n        <ConnectionStatus status={getConnectionStatusType()}>\n          {getConnectionStatusText()}\n        </ConnectionStatus>\n        \n        <ParticipantInfo>\n          <Users size={16} />\n          <span>{participants.length + 1} participants</span>\n        </ParticipantInfo>\n        \n        {isConnected && (\n          <CallTimer>\n            <Clock size={16} />\n            {formatDuration(callDuration)}\n          </CallTimer>\n        )}\n      </StatusBar>\n\n      <VideoArea>\n        {!isConnected ? (\n          <WaitingMessage>\n            Waiting for other participant to join...\n          </WaitingMessage>\n        ) : (\n          <RemoteVideo\n            ref={remoteVideoRef}\n            autoPlay\n            playsInline\n          />\n        )}\n        \n        <LocalVideo\n          ref={localVideoRef}\n          autoPlay\n          playsInline\n          muted\n        />\n      </VideoArea>\n\n      <ControlsBar>\n        <ControlButton\n          active={!isAudioMuted}\n          onClick={handleToggleAudio}\n          title={isAudioMuted ? 'Unmute' : 'Mute'}\n        >\n          {isAudioMuted ? <MicOff size={20} /> : <Mic size={20} />}\n        </ControlButton>\n\n        <ControlButton\n          active={!isVideoMuted}\n          onClick={handleToggleVideo}\n          title={isVideoMuted ? 'Turn on camera' : 'Turn off camera'}\n        >\n          {isVideoMuted ? <VideoOff size={20} /> : <Video size={20} />}\n        </ControlButton>\n\n        <ControlButton\n          active={isScreenSharing}\n          onClick={handleToggleScreenShare}\n          title={isScreenSharing ? 'Stop sharing' : 'Share screen'}\n        >\n          {isScreenSharing ? <MonitorOff size={20} /> : <Monitor size={20} />}\n        </ControlButton>\n\n        <ControlButton\n          onClick={toggleFullscreen}\n          title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}\n        >\n          {isFullscreen ? <Minimize2 size={20} /> : <Maximize2 size={20} />}\n        </ControlButton>\n\n        <ControlButton\n          danger\n          onClick={handleEndCall}\n          title=\"End call\"\n        >\n          <PhoneOff size={20} />\n        </ControlButton>\n      </ControlsBar>\n    </VideoCallContainer>\n  );\n};\n\nexport default VideoCallComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SACEC,KAAK,EACLC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,UAAU,EAEVC,QAAQ,EAGRC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,QACJ,cAAc;AACrB,SAASC,gBAAgB,QAA4C,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvG,MAAMC,kBAAkB,GAAGf,MAAM,CAACgB,GAA8B;AAChE,cAAcC,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,OAAO,GAAG,UAAU;AAChE,SAASD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,GAAG,GAAG,MAAM;AACnD,UAAUD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,GAAG,GAAG,MAAM;AACpD,WAAWD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,OAAO,GAAG,MAAM;AACzD,YAAYD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,OAAO,GAAG,OAAO;AAC3D;AACA,mBAAmBD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,GAAG,GAAG,MAAM;AAC7D;AACA,aAAaD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,MAAM,GAAG,GAAG;AACvD;AACA;AACA,CAAC;AAACC,EAAA,GAZIJ,kBAAkB;AAcxB,MAAMK,SAAS,GAAGpB,MAAM,CAACgB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAGtB,MAAM,CAACuB,KAAK;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,WAAW;AAOjB,MAAMG,UAAU,GAAGzB,MAAM,CAACuB,KAAK;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,UAAU;AAahB,MAAME,cAAc,GAAG3B,MAAM,CAACgB,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GALID,cAAc;AAOpB,MAAME,WAAW,GAAG7B,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GARID,WAAW;AAUjB,MAAME,aAAa,GAAG/B,MAAM,CAACgC,MAA8C;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBf,KAAK,IACjBA,KAAK,CAACgB,MAAM,GAAG,SAAS,GACxBhB,KAAK,CAACiB,MAAM,GAAG,SAAS,GACxB,0BAA0B;AAC9B;AACA;AACA;AACA;AACA,kBACkBjB,KAAK,IACjBA,KAAK,CAACgB,MAAM,GAAG,SAAS,GACxBhB,KAAK,CAACiB,MAAM,GAAG,SAAS,GACxB,0BAA0B;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,GAAA,GAjCIJ,aAAa;AAmCnB,MAAMK,SAAS,GAAGpC,MAAM,CAACgB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GARID,SAAS;AAUf,MAAME,gBAAgB,GAAGtC,MAAM,CAACgB,GAAuB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBC,KAAK,IACjBA,KAAK,CAACsB,MAAM,KAAK,WAAW,GAAG,SAAS,GACxCtB,KAAK,CAACsB,MAAM,KAAK,YAAY,GAAG,SAAS,GACzC,SAAS;AACf;AACA,CACC;AAACC,GAAA,GAhBIF,gBAAgB;AAkBtB,MAAMG,eAAe,GAAGzC,MAAM,CAACgB,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAJID,eAAe;AAMrB,MAAME,SAAS,GAAG3C,MAAM,CAACgB,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC4B,GAAA,GALID,SAAS;AAaf,MAAME,kBAAqD,GAAGA,CAAC;EAC7DC,YAAY;EACZC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAyB,KAAK,CAAC;EACrF,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqB,YAAY,EAAE0C,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAoB,EAAE,CAAC;EACvE,MAAM,CAACoE,cAAc,EAAEC,iBAAiB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMsE,aAAa,GAAGpE,MAAM,CAAmB,IAAI,CAAC;EACpD,MAAMqE,cAAc,GAAGrE,MAAM,CAAmB,IAAI,CAAC;EACrD,MAAMsE,gBAAgB,GAAGtE,MAAM,CAAc,IAAI,CAAC;EAElDD,SAAS,CAAC,MAAM;IACdwE,cAAc,CAAC,CAAC;IAChB,OAAO,MAAM;MACX1D,gBAAgB,CAAC2D,OAAO,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENzE,SAAS,CAAC,MAAM;IACd;IACA,IAAI0E,QAAwB;IAC5B,IAAItB,WAAW,IAAImB,gBAAgB,CAACI,OAAO,EAAE;MAC3CD,QAAQ,GAAGE,WAAW,CAAC,MAAM;QAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,GAAGX,gBAAgB,CAACI,OAAO,CAAEO,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;QACzFlB,eAAe,CAACe,QAAQ,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV;IACA,OAAO,MAAMI,aAAa,CAACT,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACtB,WAAW,CAAC,CAAC;EAEjB,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFJ,iBAAiB,CAAC,IAAI,CAAC;;MAEvB;MACA,MAAMtD,gBAAgB,CAACsE,gBAAgB,CAAC,CAAC;;MAEzC;MACA,MAAMC,MAAM,GAAG,MAAMvE,gBAAgB,CAACwE,YAAY,CAAC,CAAC;MACpD,IAAIjB,aAAa,CAACM,OAAO,EAAE;QACzBN,aAAa,CAACM,OAAO,CAACY,SAAS,GAAGF,MAAM;MAC1C;;MAEA;MACAvE,gBAAgB,CAAC0E,iBAAiB,CAAEH,MAAM,IAAK;QAC7C,IAAIf,cAAc,CAACK,OAAO,EAAE;UAC1BL,cAAc,CAACK,OAAO,CAACY,SAAS,GAAGF,MAAM;QAC3C;QACAhC,cAAc,CAAC,IAAI,CAAC;QACpBkB,gBAAgB,CAACI,OAAO,GAAG,IAAIG,IAAI,CAAC,CAAC;MACvC,CAAC,CAAC;MAEFhE,gBAAgB,CAAC2E,0BAA0B,CAAEC,KAAK,IAAK;QACrDnC,kBAAkB,CAACmC,KAAK,CAAC;QACzB,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,cAAc,EAAE;UAClDxC,OAAO,CAAC,2CAA2C,CAAC;QACtD;MACF,CAAC,CAAC;MAEFpC,gBAAgB,CAAC6E,cAAc,CAAC,MAAM;QACpC1C,SAAS,CAAC,CAAC;MACb,CAAC,CAAC;;MAEF;MACA,MAAM2C,QAAQ,GAAGC,kBAAkB,CAAC,CAAC;MACrC,MAAM/E,gBAAgB,CAACgF,QAAQ,CAAC9C,YAAY,CAAC+C,MAAM,EAAEH,QAAQ,CAAC;MAE9DxB,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD9C,OAAO,CAAC,uFAAuF,CAAC;MAChGkB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMyB,kBAAkB,GAAGA,CAAA,KAA4B;IACrD,MAAMK,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D,OAAOJ,IAAI,CAACK,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;EACtD,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG3F,gBAAgB,CAAC4F,WAAW,CAAC,CAAC;IAC5CjD,eAAe,CAACgD,KAAK,CAAC;EACxB,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMF,KAAK,GAAG3F,gBAAgB,CAAC8F,WAAW,CAAC,CAAC;IAC5CjD,eAAe,CAAC8C,KAAK,CAAC;EACxB,CAAC;EAED,MAAMI,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACF,IAAIjD,eAAe,EAAE;QACnB,MAAM9C,gBAAgB,CAACgG,eAAe,CAAC,CAAC;QACxCjD,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM/C,gBAAgB,CAACiG,gBAAgB,CAAC,CAAC;QACzClD,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD9C,OAAO,CAAC,kCAAkC,CAAC;IAC7C;EACF,CAAC;EAED,MAAM8D,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMlG,gBAAgB,CAACmG,SAAS,CAAC,CAAC;MAClChE,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C/C,SAAS,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMiE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpD,eAAe,CAAC,CAAC1C,YAAY,CAAC;EAChC,CAAC;EAED,MAAM+F,cAAc,GAAIC,OAAe,IAAa;IAClD,MAAMC,IAAI,GAAGrC,IAAI,CAACC,KAAK,CAACmC,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;EAED,MAAMC,uBAAuB,GAAGA,CAAA,KAAc;IAC5C,QAAQnE,eAAe;MACrB,KAAK,WAAW;QAAE,OAAO,WAAW;MACpC,KAAK,YAAY;QAAE,OAAO,eAAe;MACzC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,QAAQ;QAAE,OAAO,mBAAmB;MACzC;QAAS,OAAO,iBAAiB;IACnC;EACF,CAAC;EAED,MAAMoE,uBAAuB,GAAGA,CAAA,KAAc;IAC5C,QAAQpE,eAAe;MACrB,KAAK,WAAW;QAAE,OAAO,WAAW;MACpC,KAAK,YAAY;QAAE,OAAO,YAAY;MACtC;QAAS,OAAO,cAAc;IAChC;EACF,CAAC;EAED,IAAIa,cAAc,EAAE;IAClB,oBACEnD,OAAA,CAACC,kBAAkB;MAACG,YAAY,EAAE,KAAM;MAAAuG,QAAA,eACtC3G,OAAA,CAACM,SAAS;QAAAqG,QAAA,eACR3G,OAAA,CAACa,cAAc;UAAA8F,QAAA,GAAC,4BAEd,eAAA3G,OAAA;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/G,OAAA;YAAA2G,QAAA,EAAO;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEzB;EAEA,oBACE/G,OAAA,CAACC,kBAAkB;IAACG,YAAY,EAAEA,YAAa;IAAAuG,QAAA,gBAC7C3G,OAAA,CAACsB,SAAS;MAAAqF,QAAA,gBACR3G,OAAA,CAACwB,gBAAgB;QAACC,MAAM,EAAEiF,uBAAuB,CAAC,CAAE;QAAAC,QAAA,EACjDF,uBAAuB,CAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEnB/G,OAAA,CAAC2B,eAAe;QAAAgF,QAAA,gBACd3G,OAAA,CAACN,KAAK;UAACsH,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnB/G,OAAA;UAAA2G,QAAA,GAAO1D,YAAY,CAACgE,MAAM,GAAG,CAAC,EAAC,eAAa;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EAEjB3E,WAAW,iBACVpC,OAAA,CAAC6B,SAAS;QAAA8E,QAAA,gBACR3G,OAAA,CAACL,KAAK;UAACqH,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAClBZ,cAAc,CAACpD,YAAY,CAAC;MAAA;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACZ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAEZ/G,OAAA,CAACM,SAAS;MAAAqG,QAAA,GACP,CAACvE,WAAW,gBACXpC,OAAA,CAACa,cAAc;QAAA8F,QAAA,EAAC;MAEhB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC,gBAEjB/G,OAAA,CAACQ,WAAW;QACV0G,GAAG,EAAE5D,cAAe;QACpB6D,QAAQ;QACRC,WAAW;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACF,eAED/G,OAAA,CAACW,UAAU;QACTuG,GAAG,EAAE7D,aAAc;QACnB8D,QAAQ;QACRC,WAAW;QACX3B,KAAK;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEZ/G,OAAA,CAACe,WAAW;MAAA4F,QAAA,gBACV3G,OAAA,CAACiB,aAAa;QACZG,MAAM,EAAE,CAACoB,YAAa;QACtB6E,OAAO,EAAE7B,iBAAkB;QAC3B8B,KAAK,EAAE9E,YAAY,GAAG,QAAQ,GAAG,MAAO;QAAAmE,QAAA,EAEvCnE,YAAY,gBAAGxC,OAAA,CAACV,MAAM;UAAC0H,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/G,OAAA,CAACX,GAAG;UAAC2H,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAEhB/G,OAAA,CAACiB,aAAa;QACZG,MAAM,EAAE,CAACsB,YAAa;QACtB2E,OAAO,EAAE1B,iBAAkB;QAC3B2B,KAAK,EAAE5E,YAAY,GAAG,gBAAgB,GAAG,iBAAkB;QAAAiE,QAAA,EAE1DjE,YAAY,gBAAG1C,OAAA,CAACZ,QAAQ;UAAC4H,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/G,OAAA,CAACb,KAAK;UAAC6H,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eAEhB/G,OAAA,CAACiB,aAAa;QACZG,MAAM,EAAEwB,eAAgB;QACxByE,OAAO,EAAExB,uBAAwB;QACjCyB,KAAK,EAAE1E,eAAe,GAAG,cAAc,GAAG,cAAe;QAAA+D,QAAA,EAExD/D,eAAe,gBAAG5C,OAAA,CAACR,UAAU;UAACwH,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/G,OAAA,CAACT,OAAO;UAACyH,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAEhB/G,OAAA,CAACiB,aAAa;QACZoG,OAAO,EAAEnB,gBAAiB;QAC1BoB,KAAK,EAAElH,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;QAAAuG,QAAA,EAE5DvG,YAAY,gBAAGJ,OAAA,CAACH,SAAS;UAACmH,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/G,OAAA,CAACJ,SAAS;UAACoH,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAEhB/G,OAAA,CAACiB,aAAa;QACZE,MAAM;QACNkG,OAAO,EAAErB,aAAc;QACvBsB,KAAK,EAAC,UAAU;QAAAX,QAAA,eAEhB3G,OAAA,CAACP,QAAQ;UAACuH,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEzB,CAAC;AAAC5E,EAAA,CAzPIJ,kBAAqD;AAAAwF,IAAA,GAArDxF,kBAAqD;AA2P3D,eAAeA,kBAAkB;AAAC,IAAA1B,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAyF,IAAA;AAAAC,YAAA,CAAAnH,EAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}