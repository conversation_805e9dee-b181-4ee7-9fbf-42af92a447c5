import React from 'react';
import styled from 'styled-components';
import { Video, Monitor, Users, Shield } from 'lucide-react';

const VideoContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.xl};
  background: ${({ theme }) => theme.colors.background};
  min-height: 100vh;
`;

const ComingSoonCard = styled.div`
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
  padding: ${({ theme }) => theme.spacing.xxl};
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
`;

const IconContainer = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing.xl};
  color: white;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  line-height: 1.6;
`;

const FeatureList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-top: ${({ theme }) => theme.spacing.xl};
`;

const FeatureCard = styled.div`
  background: ${({ theme }) => theme.colors.background};
  padding: ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border: 2px solid ${({ theme }) => theme.colors.border};
`;

const FeatureIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: #8B5CF6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const FeatureTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const FeatureDescription = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0;
  line-height: 1.5;
`;

const VideoConsultationsPage: React.FC = () => {
  const features = [
    {
      icon: Video,
      title: 'HD Video Calls',
      description: 'Crystal clear video consultations with adaptive quality based on connection'
    },
    {
      icon: Monitor,
      title: 'Screen Sharing',
      description: 'Share medical records, test results, and educational materials during calls'
    },
    {
      icon: Users,
      title: 'Multi-party Calls',
      description: 'Include family members or specialists in consultations when needed'
    },
    {
      icon: Shield,
      title: 'Secure & Private',
      description: 'End-to-end encrypted video calls ensuring complete patient privacy'
    }
  ];

  return (
    <VideoContainer>
      <ComingSoonCard>
        <IconContainer>
          <Video size={60} />
        </IconContainer>
        
        <Title>Video Consultations</Title>
        <Subtitle>
          Our telemedicine platform is being developed to provide secure, 
          high-quality video consultations between patients and healthcare providers. 
          Experience healthcare from the comfort of your home.
        </Subtitle>

        <FeatureList>
          {features.map((feature, index) => (
            <FeatureCard key={index}>
              <FeatureIcon>
                <feature.icon size={24} />
              </FeatureIcon>
              <FeatureTitle>{feature.title}</FeatureTitle>
              <FeatureDescription>{feature.description}</FeatureDescription>
            </FeatureCard>
          ))}
        </FeatureList>
      </ComingSoonCard>
    </VideoContainer>
  );
};

export default VideoConsultationsPage;
