{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../src/parser.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,IAAI,GAAG,CAAC,CAAC;AACf;;GAEG;AACH,MAAM,EAAE,GAAG,EAAE,CAAC;AACd;;GAEG;AACH,MAAM,EAAE,GAAG,EAAE,CAAC;AACd;;GAEG;AACH,MAAM,KAAK,GAAG,EAAE,CAAC;AAEjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCG;AACH,MAAM,OAAO,MAAM;IAcjB,YACS,OAA0C,EAC1C,cAA0B;QAD1B,YAAO,GAAP,OAAO,CAAmC;QAC1C,mBAAc,GAAd,cAAc,CAAY;QAflB,aAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;QAC7B,aAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;QAKtC,WAAM,GAAa,EAAE,CAAC;QAW5B,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEM,UAAU,CACf,OAA6B,EAC7B,8BAAuC,KAAK;QAE5C,IAAI,KAAiB,CAAC;QAEtB,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;QAED,oDAAoD;QACpD,yCAAyC;QACzC,EAAE;QACF,oEAAoE;QACpE,IAAI,2BAA2B,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACjE,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvD,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC5B,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAChC,KAAK,GAAG,aAAa,CAAC;QACxB,CAAC;QAED,yCAAyC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAED,wDAAwD;IACxD,8EAA8E;IAEtE,aAAa,CAAC,IAAY;QAChC,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAClB,SAAS;YACT,OAAO;QACT,CAAC;QACD,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,YAAY;YACZ,OAAO;QACT,CAAC;QACD,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,gBAAgB;YAChB,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEO,eAAe,CAAC,IAAY;QAClC,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,YAAY;YACZ,OAAO;QACT,CAAC;QACD,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,eAAe,CAAC,IAAY;QAClC,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,YAAY;YACZ,OAAO;QACT,CAAC;QACD,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEO,aAAa,CAAC,IAAY;QAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAEO,iBAAiB,CAAC,IAAY;QACpC,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACxC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACtC,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,YAAY;YACZ,OAAO;QACT,CAAC;QACD,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;gBACzB,IAAI,CAAC,UAAoB;gBACzB,IAAI,CAAC,mBAAmB,EAAE;aAC3B,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;YAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;YACpC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,iBAAiB;QACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CACtD,CAAC,MAAwB,EAAE,EAAE;YAC3B,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,gBAAgB,CAAC;QACxC,CAAC,CACF,CAAC,CAAC,CAAC,CAAC;QAEL,IAAI,mBAAmB,EAAE,CAAC;YACxB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAChE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACjD,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,IAAY;QAC7C,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,qBAAqB,CAAC,IAAY;QACxC,mEAAmE;QACnE,IAAK,IAAI,CAAC,mBAA8B,EAAE,KAAK,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAErD,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CACT,uEAAuE,EACvE,CAAC,CACF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,6BAA6B;IAErB,YAAY,CAAC,IAAY;QAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEO,mBAAmB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;IACzD,CAAC;IAEO,kBAAkB;QACxB,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC,QAAQ,GAAG;YACd,OAAO,EAAE,SAAS;YAClB,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,SAAS;SACtB,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAE5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;IACpC,CAAC;CACF"}