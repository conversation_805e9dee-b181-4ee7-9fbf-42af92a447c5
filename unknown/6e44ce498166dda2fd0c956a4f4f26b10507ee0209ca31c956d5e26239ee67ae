{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils/event'),\n  urlUtils = require('../utils/url'),\n  inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  WebsocketDriver = require('./driver/websocket');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:websocket');\n}\nfunction WebSocketTransport(transUrl, ignore, options) {\n  if (!WebSocketTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n  debug('constructor', transUrl);\n  var self = this;\n  var url = urlUtils.addPath(transUrl, '/websocket');\n  if (url.slice(0, 5) === 'https') {\n    url = 'wss' + url.slice(5);\n  } else {\n    url = 'ws' + url.slice(4);\n  }\n  this.url = url;\n  this.ws = new WebsocketDriver(this.url, [], options);\n  this.ws.onmessage = function (e) {\n    debug('message event', e.data);\n    self.emit('message', e.data);\n  };\n  // Firefox has an interesting bug. If a websocket connection is\n  // created after onunload, it stays alive even when user\n  // navigates away from the page. In such situation let's lie -\n  // let's not open the ws connection at all. See:\n  // https://github.com/sockjs/sockjs-client/issues/28\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=696085\n  this.unloadRef = utils.unloadAdd(function () {\n    debug('unload');\n    self.ws.close();\n  });\n  this.ws.onclose = function (e) {\n    debug('close event', e.code, e.reason);\n    self.emit('close', e.code, e.reason);\n    self._cleanup();\n  };\n  this.ws.onerror = function (e) {\n    debug('error event', e);\n    self.emit('close', 1006, 'WebSocket connection broken');\n    self._cleanup();\n  };\n}\ninherits(WebSocketTransport, EventEmitter);\nWebSocketTransport.prototype.send = function (data) {\n  var msg = '[' + data + ']';\n  debug('send', msg);\n  this.ws.send(msg);\n};\nWebSocketTransport.prototype.close = function () {\n  debug('close');\n  var ws = this.ws;\n  this._cleanup();\n  if (ws) {\n    ws.close();\n  }\n};\nWebSocketTransport.prototype._cleanup = function () {\n  debug('_cleanup');\n  var ws = this.ws;\n  if (ws) {\n    ws.onmessage = ws.onclose = ws.onerror = null;\n  }\n  utils.unloadDel(this.unloadRef);\n  this.unloadRef = this.ws = null;\n  this.removeAllListeners();\n};\nWebSocketTransport.enabled = function () {\n  debug('enabled');\n  return !!WebsocketDriver;\n};\nWebSocketTransport.transportName = 'websocket';\n\n// In theory, ws should require 1 round trip. But in chrome, this is\n// not very stable over SSL. Most likely a ws connection requires a\n// separate SSL connection, in which case 2 round trips are an\n// absolute minumum.\nWebSocketTransport.roundTrips = 2;\nmodule.exports = WebSocketTransport;", "map": {"version": 3, "names": ["utils", "require", "urlUtils", "inherits", "EventEmitter", "WebsocketDriver", "debug", "process", "env", "NODE_ENV", "WebSocketTransport", "transUrl", "ignore", "options", "enabled", "Error", "call", "self", "url", "addPath", "slice", "ws", "onmessage", "e", "data", "emit", "unloadRef", "unloadAdd", "close", "onclose", "code", "reason", "_cleanup", "onerror", "prototype", "send", "msg", "unloadDel", "removeAllListeners", "transportName", "roundTrips", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/transport/websocket.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils/event')\n  , urlUtils = require('../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , WebsocketDriver = require('./driver/websocket')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:websocket');\n}\n\nfunction WebSocketTransport(transUrl, ignore, options) {\n  if (!WebSocketTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  EventEmitter.call(this);\n  debug('constructor', transUrl);\n\n  var self = this;\n  var url = urlUtils.addPath(transUrl, '/websocket');\n  if (url.slice(0, 5) === 'https') {\n    url = 'wss' + url.slice(5);\n  } else {\n    url = 'ws' + url.slice(4);\n  }\n  this.url = url;\n\n  this.ws = new WebsocketDriver(this.url, [], options);\n  this.ws.onmessage = function(e) {\n    debug('message event', e.data);\n    self.emit('message', e.data);\n  };\n  // Firefox has an interesting bug. If a websocket connection is\n  // created after onunload, it stays alive even when user\n  // navigates away from the page. In such situation let's lie -\n  // let's not open the ws connection at all. See:\n  // https://github.com/sockjs/sockjs-client/issues/28\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=696085\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload');\n    self.ws.close();\n  });\n  this.ws.onclose = function(e) {\n    debug('close event', e.code, e.reason);\n    self.emit('close', e.code, e.reason);\n    self._cleanup();\n  };\n  this.ws.onerror = function(e) {\n    debug('error event', e);\n    self.emit('close', 1006, 'WebSocket connection broken');\n    self._cleanup();\n  };\n}\n\ninherits(WebSocketTransport, EventEmitter);\n\nWebSocketTransport.prototype.send = function(data) {\n  var msg = '[' + data + ']';\n  debug('send', msg);\n  this.ws.send(msg);\n};\n\nWebSocketTransport.prototype.close = function() {\n  debug('close');\n  var ws = this.ws;\n  this._cleanup();\n  if (ws) {\n    ws.close();\n  }\n};\n\nWebSocketTransport.prototype._cleanup = function() {\n  debug('_cleanup');\n  var ws = this.ws;\n  if (ws) {\n    ws.onmessage = ws.onclose = ws.onerror = null;\n  }\n  utils.unloadDel(this.unloadRef);\n  this.unloadRef = this.ws = null;\n  this.removeAllListeners();\n};\n\nWebSocketTransport.enabled = function() {\n  debug('enabled');\n  return !!WebsocketDriver;\n};\nWebSocketTransport.transportName = 'websocket';\n\n// In theory, ws should require 1 round trip. But in chrome, this is\n// not very stable over SSL. Most likely a ws connection requires a\n// separate SSL connection, in which case 2 round trips are an\n// absolute minumum.\nWebSocketTransport.roundTrips = 2;\n\nmodule.exports = WebSocketTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACjCC,QAAQ,GAAGD,OAAO,CAAC,cAAc,CAAC;EAClCE,QAAQ,GAAGF,OAAO,CAAC,UAAU,CAAC;EAC9BG,YAAY,GAAGH,OAAO,CAAC,QAAQ,CAAC,CAACG,YAAY;EAC7CC,eAAe,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AAGnD,IAAIK,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC;AACrD;AAEA,SAASS,kBAAkBA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,IAAI,CAACH,kBAAkB,CAACI,OAAO,CAAC,CAAC,EAAE;IACjC,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EAEAX,YAAY,CAACY,IAAI,CAAC,IAAI,CAAC;EACvBV,KAAK,CAAC,aAAa,EAAEK,QAAQ,CAAC;EAE9B,IAAIM,IAAI,GAAG,IAAI;EACf,IAAIC,GAAG,GAAGhB,QAAQ,CAACiB,OAAO,CAACR,QAAQ,EAAE,YAAY,CAAC;EAClD,IAAIO,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;IAC/BF,GAAG,GAAG,KAAK,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;EAC5B,CAAC,MAAM;IACLF,GAAG,GAAG,IAAI,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;EAC3B;EACA,IAAI,CAACF,GAAG,GAAGA,GAAG;EAEd,IAAI,CAACG,EAAE,GAAG,IAAIhB,eAAe,CAAC,IAAI,CAACa,GAAG,EAAE,EAAE,EAAEL,OAAO,CAAC;EACpD,IAAI,CAACQ,EAAE,CAACC,SAAS,GAAG,UAASC,CAAC,EAAE;IAC9BjB,KAAK,CAAC,eAAe,EAAEiB,CAAC,CAACC,IAAI,CAAC;IAC9BP,IAAI,CAACQ,IAAI,CAAC,SAAS,EAAEF,CAAC,CAACC,IAAI,CAAC;EAC9B,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACE,SAAS,GAAG1B,KAAK,CAAC2B,SAAS,CAAC,YAAW;IAC1CrB,KAAK,CAAC,QAAQ,CAAC;IACfW,IAAI,CAACI,EAAE,CAACO,KAAK,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,IAAI,CAACP,EAAE,CAACQ,OAAO,GAAG,UAASN,CAAC,EAAE;IAC5BjB,KAAK,CAAC,aAAa,EAAEiB,CAAC,CAACO,IAAI,EAAEP,CAAC,CAACQ,MAAM,CAAC;IACtCd,IAAI,CAACQ,IAAI,CAAC,OAAO,EAAEF,CAAC,CAACO,IAAI,EAAEP,CAAC,CAACQ,MAAM,CAAC;IACpCd,IAAI,CAACe,QAAQ,CAAC,CAAC;EACjB,CAAC;EACD,IAAI,CAACX,EAAE,CAACY,OAAO,GAAG,UAASV,CAAC,EAAE;IAC5BjB,KAAK,CAAC,aAAa,EAAEiB,CAAC,CAAC;IACvBN,IAAI,CAACQ,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,6BAA6B,CAAC;IACvDR,IAAI,CAACe,QAAQ,CAAC,CAAC;EACjB,CAAC;AACH;AAEA7B,QAAQ,CAACO,kBAAkB,EAAEN,YAAY,CAAC;AAE1CM,kBAAkB,CAACwB,SAAS,CAACC,IAAI,GAAG,UAASX,IAAI,EAAE;EACjD,IAAIY,GAAG,GAAG,GAAG,GAAGZ,IAAI,GAAG,GAAG;EAC1BlB,KAAK,CAAC,MAAM,EAAE8B,GAAG,CAAC;EAClB,IAAI,CAACf,EAAE,CAACc,IAAI,CAACC,GAAG,CAAC;AACnB,CAAC;AAED1B,kBAAkB,CAACwB,SAAS,CAACN,KAAK,GAAG,YAAW;EAC9CtB,KAAK,CAAC,OAAO,CAAC;EACd,IAAIe,EAAE,GAAG,IAAI,CAACA,EAAE;EAChB,IAAI,CAACW,QAAQ,CAAC,CAAC;EACf,IAAIX,EAAE,EAAE;IACNA,EAAE,CAACO,KAAK,CAAC,CAAC;EACZ;AACF,CAAC;AAEDlB,kBAAkB,CAACwB,SAAS,CAACF,QAAQ,GAAG,YAAW;EACjD1B,KAAK,CAAC,UAAU,CAAC;EACjB,IAAIe,EAAE,GAAG,IAAI,CAACA,EAAE;EAChB,IAAIA,EAAE,EAAE;IACNA,EAAE,CAACC,SAAS,GAAGD,EAAE,CAACQ,OAAO,GAAGR,EAAE,CAACY,OAAO,GAAG,IAAI;EAC/C;EACAjC,KAAK,CAACqC,SAAS,CAAC,IAAI,CAACX,SAAS,CAAC;EAC/B,IAAI,CAACA,SAAS,GAAG,IAAI,CAACL,EAAE,GAAG,IAAI;EAC/B,IAAI,CAACiB,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAED5B,kBAAkB,CAACI,OAAO,GAAG,YAAW;EACtCR,KAAK,CAAC,SAAS,CAAC;EAChB,OAAO,CAAC,CAACD,eAAe;AAC1B,CAAC;AACDK,kBAAkB,CAAC6B,aAAa,GAAG,WAAW;;AAE9C;AACA;AACA;AACA;AACA7B,kBAAkB,CAAC8B,UAAU,GAAG,CAAC;AAEjCC,MAAM,CAACC,OAAO,GAAGhC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}