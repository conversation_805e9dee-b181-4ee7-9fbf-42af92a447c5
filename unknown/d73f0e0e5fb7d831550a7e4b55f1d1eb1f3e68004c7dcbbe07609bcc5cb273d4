{"ast": null, "code": "module.exports = global.EventSource;", "map": {"version": 3, "names": ["module", "exports", "global", "EventSource"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/transport/browser/eventsource.js"], "sourcesContent": ["module.exports = global.EventSource;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}