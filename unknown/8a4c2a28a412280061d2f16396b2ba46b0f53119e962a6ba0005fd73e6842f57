{"ast": null, "code": "import { <PERSON>om<PERSON><PERSON>and<PERSON> } from './stomp-handler.js';\nimport { ActivationState, ReconnectionTimeMode, StompSocketState, TickerStrategy } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n  /**\n   * Underlying WebSocket instance, READONLY.\n   */\n  get webSocket() {\n    return this._stompHandler?._webSocket;\n  }\n  /**\n   * Disconnection headers.\n   */\n  get disconnectHeaders() {\n    return this._disconnectHeaders;\n  }\n  set disconnectHeaders(value) {\n    this._disconnectHeaders = value;\n    if (this._stompHandler) {\n      this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n    }\n  }\n  /**\n   * `true` if there is an active connection to STOMP Broker\n   */\n  get connected() {\n    return !!this._stompHandler && this._stompHandler.connected;\n  }\n  /**\n   * version of STOMP protocol negotiated with the server, READONLY\n   */\n  get connectedVersion() {\n    return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n  }\n  /**\n   * if the client is active (connected or going to reconnect)\n   */\n  get active() {\n    return this.state === ActivationState.ACTIVE;\n  }\n  _changeState(state) {\n    this.state = state;\n    this.onChangeState(state);\n  }\n  /**\n   * Create an instance.\n   */\n  constructor(conf = {}) {\n    /**\n     * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n     *\n     * Example:\n     * ```javascript\n     *        // Try only versions 1.1 and 1.0\n     *        client.stompVersions = new Versions(['1.1', '1.0'])\n     * ```\n     */\n    this.stompVersions = Versions.default;\n    /**\n     * Will retry if Stomp connection is not established in specified milliseconds.\n     * Default 0, which switches off automatic reconnection.\n     */\n    this.connectionTimeout = 0;\n    /**\n     *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n     */\n    this.reconnectDelay = 5000;\n    /**\n     * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n     * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n     */\n    this._nextReconnectDelay = 0;\n    /**\n     * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n     * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n     * Set to 0 for no limit on wait time.\n     */\n    this.maxReconnectDelay = 15 * 60 * 1000; // 15 minutes in ms\n    /**\n     * Reconnection wait time mode, either linear (default) or exponential.\n     * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n     *\n     * ```javascript\n     * client.configure({\n     *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n     *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n     *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n     * })\n     * ```\n     */\n    this.reconnectTimeMode = ReconnectionTimeMode.LINEAR;\n    /**\n     * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n    this.heartbeatIncoming = 10000;\n    /**\n     * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n    this.heartbeatOutgoing = 10000;\n    /**\n     * Outgoing heartbeat strategy.\n     * See https://github.com/stomp-js/stompjs/pull/579\n     *\n     * Can be worker or interval strategy, but will always use `interval`\n     * if web workers are unavailable, for example, in a non-browser environment.\n     *\n     * Using Web Workers may work better on long-running pages\n     * and mobile apps, as the browser may suspend Timers in the main page.\n     * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n     *\n     * When used in a JS environment, use 'worker' or 'interval' as valid values.\n     *\n     * Defaults to `interval` strategy.\n     */\n    this.heartbeatStrategy = TickerStrategy.Interval;\n    /**\n     * This switches on a non-standard behavior while sending WebSocket packets.\n     * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n     * Only Java Spring brokers seem to support this mode.\n     *\n     * WebSockets, by itself, split large (text) packets,\n     * so it is not needed with a truly compliant STOMP/WebSocket broker.\n     * Setting it for such a broker will cause large messages to fail.\n     *\n     * `false` by default.\n     *\n     * Binary frames are never split.\n     */\n    this.splitLargeFrames = false;\n    /**\n     * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n     * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n     */\n    this.maxWebSocketChunkSize = 8 * 1024;\n    /**\n     * Usually the\n     * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n     * is automatically decided by type of the payload.\n     * Default is `false`, which should work with all compliant brokers.\n     *\n     * Set this flag to force binary frames.\n     */\n    this.forceBinaryWSFrames = false;\n    /**\n     * A bug in ReactNative chops a string on occurrence of a NULL.\n     * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n     * This makes incoming WebSocket messages invalid STOMP packets.\n     * Setting this flag attempts to reverse the damage by appending a NULL.\n     * If the broker splits a large message into multiple WebSocket messages,\n     * this flag will cause data loss and abnormal termination of connection.\n     *\n     * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n     */\n    this.appendMissingNULLonIncoming = false;\n    /**\n     * Browsers do not immediately close WebSockets when `.close` is issued.\n     * This may cause reconnection to take a significantly long time in case\n     *  of some types of failures.\n     * In case of incoming heartbeat failure, this experimental flag instructs\n     * the library to discard the socket immediately\n     * (even before it is actually closed).\n     */\n    this.discardWebsocketOnCommFailure = false;\n    /**\n     * Activation state.\n     *\n     * It will usually be ACTIVE or INACTIVE.\n     * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n     */\n    this.state = ActivationState.INACTIVE;\n    // No op callbacks\n    const noOp = () => {};\n    this.debug = noOp;\n    this.beforeConnect = noOp;\n    this.onConnect = noOp;\n    this.onDisconnect = noOp;\n    this.onUnhandledMessage = noOp;\n    this.onUnhandledReceipt = noOp;\n    this.onUnhandledFrame = noOp;\n    this.onStompError = noOp;\n    this.onWebSocketClose = noOp;\n    this.onWebSocketError = noOp;\n    this.logRawCommunication = false;\n    this.onChangeState = noOp;\n    // These parameters would typically get proper values before connect is called\n    this.connectHeaders = {};\n    this._disconnectHeaders = {};\n    // Apply configuration\n    this.configure(conf);\n  }\n  /**\n   * Update configuration.\n   */\n  configure(conf) {\n    // bulk assign all properties to this\n    Object.assign(this, conf);\n    // Warn on incorrect maxReconnectDelay settings\n    if (this.maxReconnectDelay > 0 && this.maxReconnectDelay < this.reconnectDelay) {\n      this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`);\n      this.maxReconnectDelay = this.reconnectDelay;\n    }\n  }\n  /**\n   * Initiate the connection with the broker.\n   * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n   * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n   * is set to EXPONENTIAL it will increase the wait time exponentially\n   *\n   * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n   */\n  activate() {\n    const _activate = () => {\n      if (this.active) {\n        this.debug('Already ACTIVE, ignoring request to activate');\n        return;\n      }\n      this._changeState(ActivationState.ACTIVE);\n      this._nextReconnectDelay = this.reconnectDelay;\n      this._connect();\n    };\n    // if it is deactivating, wait for it to complete before activating.\n    if (this.state === ActivationState.DEACTIVATING) {\n      this.debug('Waiting for deactivation to finish before activating');\n      this.deactivate().then(() => {\n        _activate();\n      });\n    } else {\n      _activate();\n    }\n  }\n  async _connect() {\n    await this.beforeConnect(this);\n    if (this._stompHandler) {\n      this.debug('There is already a stompHandler, skipping the call to connect');\n      return;\n    }\n    if (!this.active) {\n      this.debug('Client has been marked inactive, will not attempt to connect');\n      return;\n    }\n    // setup connection watcher\n    if (this.connectionTimeout > 0) {\n      // clear first\n      if (this._connectionWatcher) {\n        clearTimeout(this._connectionWatcher);\n      }\n      this._connectionWatcher = setTimeout(() => {\n        if (this.connected) {\n          return;\n        }\n        // Connection not established, close the underlying socket\n        // a reconnection will be attempted\n        this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`);\n        this.forceDisconnect();\n      }, this.connectionTimeout);\n    }\n    this.debug('Opening Web Socket...');\n    // Get the actual WebSocket (or a similar object)\n    const webSocket = this._createWebSocket();\n    this._stompHandler = new StompHandler(this, webSocket, {\n      debug: this.debug,\n      stompVersions: this.stompVersions,\n      connectHeaders: this.connectHeaders,\n      disconnectHeaders: this._disconnectHeaders,\n      heartbeatIncoming: this.heartbeatIncoming,\n      heartbeatOutgoing: this.heartbeatOutgoing,\n      heartbeatStrategy: this.heartbeatStrategy,\n      splitLargeFrames: this.splitLargeFrames,\n      maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n      forceBinaryWSFrames: this.forceBinaryWSFrames,\n      logRawCommunication: this.logRawCommunication,\n      appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n      discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n      onConnect: frame => {\n        // Successfully connected, stop the connection watcher\n        if (this._connectionWatcher) {\n          clearTimeout(this._connectionWatcher);\n          this._connectionWatcher = undefined;\n        }\n        if (!this.active) {\n          this.debug('STOMP got connected while deactivate was issued, will disconnect now');\n          this._disposeStompHandler();\n          return;\n        }\n        this.onConnect(frame);\n      },\n      onDisconnect: frame => {\n        this.onDisconnect(frame);\n      },\n      onStompError: frame => {\n        this.onStompError(frame);\n      },\n      onWebSocketClose: evt => {\n        this._stompHandler = undefined; // a new one will be created in case of a reconnect\n        if (this.state === ActivationState.DEACTIVATING) {\n          // Mark deactivation complete\n          this._changeState(ActivationState.INACTIVE);\n        }\n        // The callback is called before attempting to reconnect, this would allow the client\n        // to be `deactivated` in the callback.\n        this.onWebSocketClose(evt);\n        if (this.active) {\n          this._schedule_reconnect();\n        }\n      },\n      onWebSocketError: evt => {\n        this.onWebSocketError(evt);\n      },\n      onUnhandledMessage: message => {\n        this.onUnhandledMessage(message);\n      },\n      onUnhandledReceipt: frame => {\n        this.onUnhandledReceipt(frame);\n      },\n      onUnhandledFrame: frame => {\n        this.onUnhandledFrame(frame);\n      }\n    });\n    this._stompHandler.start();\n  }\n  _createWebSocket() {\n    let webSocket;\n    if (this.webSocketFactory) {\n      webSocket = this.webSocketFactory();\n    } else if (this.brokerURL) {\n      webSocket = new WebSocket(this.brokerURL, this.stompVersions.protocolVersions());\n    } else {\n      throw new Error('Either brokerURL or webSocketFactory must be provided');\n    }\n    webSocket.binaryType = 'arraybuffer';\n    return webSocket;\n  }\n  _schedule_reconnect() {\n    if (this._nextReconnectDelay > 0) {\n      this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`);\n      this._reconnector = setTimeout(() => {\n        if (this.reconnectTimeMode === ReconnectionTimeMode.EXPONENTIAL) {\n          this._nextReconnectDelay = this._nextReconnectDelay * 2;\n          // Truncated exponential backoff with a set limit unless disabled\n          if (this.maxReconnectDelay !== 0) {\n            this._nextReconnectDelay = Math.min(this._nextReconnectDelay, this.maxReconnectDelay);\n          }\n        }\n        this._connect();\n      }, this._nextReconnectDelay);\n    }\n  }\n  /**\n   * Disconnect if connected and stop auto reconnect loop.\n   * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n   *\n   * This call is async. It will resolve immediately if there is no underlying active websocket,\n   * otherwise, it will resolve after the underlying websocket is properly disposed of.\n   *\n   * It is not an error to invoke this method more than once.\n   * Each of those would resolve on completion of deactivation.\n   *\n   * To reactivate, you can call [Client#activate]{@link Client#activate}.\n   *\n   * Experimental: pass `force: true` to immediately discard the underlying connection.\n   * This mode will skip both the STOMP and the Websocket shutdown sequences.\n   * In some cases, browsers take a long time in the Websocket shutdown\n   * if the underlying connection had gone stale.\n   * Using this mode can speed up.\n   * When this mode is used, the actual Websocket may linger for a while\n   * and the broker may not realize that the connection is no longer in use.\n   *\n   * It is possible to invoke this method initially without the `force` option\n   * and subsequently, say after a wait, with the `force` option.\n   */\n  async deactivate(options = {}) {\n    const force = options.force || false;\n    const needToDispose = this.active;\n    let retPromise;\n    if (this.state === ActivationState.INACTIVE) {\n      this.debug(`Already INACTIVE, nothing more to do`);\n      return Promise.resolve();\n    }\n    this._changeState(ActivationState.DEACTIVATING);\n    // Reset reconnection timer just to be safe\n    this._nextReconnectDelay = 0;\n    // Clear if a reconnection was scheduled\n    if (this._reconnector) {\n      clearTimeout(this._reconnector);\n      this._reconnector = undefined;\n    }\n    if (this._stompHandler &&\n    // @ts-ignore - if there is a _stompHandler, there is the webSocket\n    this.webSocket.readyState !== StompSocketState.CLOSED) {\n      const origOnWebSocketClose = this._stompHandler.onWebSocketClose;\n      // we need to wait for the underlying websocket to close\n      retPromise = new Promise((resolve, reject) => {\n        // @ts-ignore - there is a _stompHandler\n        this._stompHandler.onWebSocketClose = evt => {\n          origOnWebSocketClose(evt);\n          resolve();\n        };\n      });\n    } else {\n      // indicate that auto reconnect loop should terminate\n      this._changeState(ActivationState.INACTIVE);\n      return Promise.resolve();\n    }\n    if (force) {\n      this._stompHandler?.discardWebsocket();\n    } else if (needToDispose) {\n      this._disposeStompHandler();\n    }\n    return retPromise;\n  }\n  /**\n   * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n   * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n   * After forcing disconnect, automatic reconnect will be attempted.\n   * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n   */\n  forceDisconnect() {\n    if (this._stompHandler) {\n      this._stompHandler.forceDisconnect();\n    }\n  }\n  _disposeStompHandler() {\n    // Dispose STOMP Handler\n    if (this._stompHandler) {\n      this._stompHandler.dispose();\n    }\n  }\n  /**\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations.\n   *\n   * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n   *\n   * `body` must be String.\n   * You will need to covert the payload to string in case it is not string (e.g. JSON).\n   *\n   * To send a binary message body, use `binaryBody` parameter. It should be a\n   * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n   * Sometimes brokers may not support binary frames out of the box.\n   * Please check your broker documentation.\n   *\n   * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n   * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n   * For binary messages, `content-length` header is always added.\n   *\n   * Caution: The broker will, most likely, report an error and disconnect\n   * if the message body has NULL octet(s) and `content-length` header is missing.\n   *\n   * ```javascript\n   *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n   *\n   *        // Only destination is mandatory parameter\n   *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n   *\n   *        // Skip content-length header in the frame to the broker\n   *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n   *\n   *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n   *        // setting content-type header is not mandatory, however a good practice\n   *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n   *                         headers: {'content-type': 'application/octet-stream'}});\n   * ```\n   */\n  publish(params) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.publish(params);\n  }\n  _checkConnection() {\n    if (!this.connected) {\n      throw new TypeError('There is no underlying STOMP connection');\n    }\n  }\n  /**\n   * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n   * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n   * The value (say receipt-id) for this header needs to be unique for each use.\n   * Typically, a sequence, a UUID, a random number or a combination may be used.\n   *\n   * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n   * The operation needs to be matched based on the value of the receipt-id.\n   *\n   * This method allows watching for a receipt and invoking the callback\n   *  when the corresponding receipt has been received.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   *\n   * Example:\n   * ```javascript\n   *        // Subscribing with acknowledgement\n   *        let receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *\n   *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n   *\n   *\n   *        // Publishing with acknowledgement\n   *        receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n   * ```\n   */\n  watchForReceipt(receiptId, callback) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.watchForReceipt(receiptId, callback);\n  }\n  /**\n   * Subscribe to a STOMP Broker location. The callback will be invoked for each\n   * received message with the {@link IMessage} as argument.\n   *\n   * Note: The library will generate a unique ID if there is none provided in the headers.\n   *       To use your own ID, pass it using the `headers` argument.\n   *\n   * ```javascript\n   *        callback = function(message) {\n   *        // called when the client receives a STOMP message from the server\n   *          if (message.body) {\n   *            alert(\"got message with body \" + message.body)\n   *          } else {\n   *            alert(\"got empty message\");\n   *          }\n   *        });\n   *\n   *        var subscription = client.subscribe(\"/queue/test\", callback);\n   *\n   *        // Explicit subscription id\n   *        var mySubId = 'my-subscription-id-001';\n   *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n   * ```\n   */\n  subscribe(destination, callback, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.subscribe(destination, callback, headers);\n  }\n  /**\n   * It is preferable to unsubscribe from a subscription by calling\n   * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n   *\n   * ```javascript\n   *        var subscription = client.subscribe(destination, onmessage);\n   *        // ...\n   *        subscription.unsubscribe();\n   * ```\n   *\n   * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n   */\n  unsubscribe(id, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.unsubscribe(id, headers);\n  }\n  /**\n   * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n   * and [abort]{@link ITransaction#abort}.\n   *\n   * `transactionId` is optional, if not passed the library will generate it internally.\n   */\n  begin(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.begin(transactionId);\n  }\n  /**\n   * Commit a transaction.\n   *\n   * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.commit();\n   * ```\n   */\n  commit(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.commit(transactionId);\n  }\n  /**\n   * Abort a transaction.\n   * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.abort();\n   * ```\n   */\n  abort(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.abort(transactionId);\n  }\n  /**\n   * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // acknowledge it\n   *          message.ack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  ack(messageId, subscriptionId, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.ack(messageId, subscriptionId, headers);\n  }\n  /**\n   * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // an error occurs, nack it\n   *          message.nack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  nack(messageId, subscriptionId, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.nack(messageId, subscriptionId, headers);\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ActivationState", "ReconnectionTimeMode", "StompSocketState", "TickerStrategy", "Versions", "Client", "webSocket", "_stomp<PERSON><PERSON><PERSON>", "_webSocket", "disconnectHeaders", "_disconnectHeaders", "value", "connected", "connectedVersion", "undefined", "active", "state", "ACTIVE", "_changeState", "onChangeState", "constructor", "conf", "stompV<PERSON><PERSON>", "default", "connectionTimeout", "reconnectDelay", "_nextReconnectDelay", "maxReconnectDelay", "reconnectTimeMode", "LINEAR", "heartbeatIncoming", "heartbeatOutgoing", "heartbeatStrategy", "Interval", "splitLargeFrames", "maxWebSocketChunkSize", "forceBinaryWSFrames", "appendMissingNULLonIncoming", "discardWebsocketOnCommFailure", "INACTIVE", "noOp", "debug", "beforeConnect", "onConnect", "onDisconnect", "onUnhandledMessage", "onUnhandledReceipt", "onUnhandledFrame", "onStompError", "onWebSocketClose", "onWebSocketError", "logRawCommunication", "connectHeaders", "configure", "Object", "assign", "activate", "_activate", "_connect", "DEACTIVATING", "deactivate", "then", "_connectionWatcher", "clearTimeout", "setTimeout", "forceDisconnect", "_createWebSocket", "frame", "_dispose<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evt", "_schedule_reconnect", "message", "start", "webSocketFactory", "brokerURL", "WebSocket", "protocolVersions", "Error", "binaryType", "_reconnector", "EXPONENTIAL", "Math", "min", "options", "force", "needToDispose", "retPromise", "Promise", "resolve", "readyState", "CLOSED", "origOnWebSocketClose", "reject", "discardWeb<PERSON>cket", "dispose", "publish", "params", "_checkConnection", "TypeError", "watchForReceipt", "receiptId", "callback", "subscribe", "destination", "headers", "unsubscribe", "id", "begin", "transactionId", "commit", "abort", "ack", "messageId", "subscriptionId", "nack"], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\client.ts"], "sourcesContent": ["import { ITransaction } from './i-transaction.js';\nimport { StompConfig } from './stomp-config.js';\nimport { <PERSON>om<PERSON><PERSON>and<PERSON> } from './stomp-handler.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { StompSubscription } from './stomp-subscription.js';\nimport {\n  ActivationState,\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  IPublishParams,\n  IStompSocket,\n  messageCallbackType,\n  ReconnectionTimeMode,\n  StompSocketState,\n  TickerStrategy,\n  wsErrorCallbackType,\n} from './types.js';\nimport { Versions } from './versions.js';\n\n/**\n * @internal\n */\ndeclare const WebSocket: {\n  prototype: IStompSocket;\n  new (url: string, protocols?: string | string[]): IStompSocket;\n};\n\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n  /**\n   * The URL for the STOMP broker to connect to.\n   * Typically like `\"ws://broker.329broker.com:15674/ws\"` or `\"wss://broker.329broker.com:15674/ws\"`.\n   *\n   * Only one of this or [Client#webSocketFactory]{@link Client#webSocketFactory} need to be set.\n   * If both are set, [Client#webSocketFactory]{@link Client#webSocketFactory} will be used.\n   *\n   * If your environment does not support WebSockets natively, please refer to\n   * [Polyfills]{@link https://stomp-js.github.io/guide/stompjs/rx-stomp/ng2-stompjs/pollyfils-for-stompjs-v5.html}.\n   */\n  public brokerURL: string | undefined;\n\n  /**\n   * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n   *\n   * Example:\n   * ```javascript\n   *        // Try only versions 1.1 and 1.0\n   *        client.stompVersions = new Versions(['1.1', '1.0'])\n   * ```\n   */\n  public stompVersions = Versions.default;\n\n  /**\n   * This function should return a WebSocket or a similar (e.g. SockJS) object.\n   * If your environment does not support WebSockets natively, please refer to\n   * [Polyfills]{@link https://stomp-js.github.io/guide/stompjs/rx-stomp/ng2-stompjs/pollyfils-for-stompjs-v5.html}.\n   * If your STOMP Broker supports WebSockets, prefer setting [Client#brokerURL]{@link Client#brokerURL}.\n   *\n   * If both this and [Client#brokerURL]{@link Client#brokerURL} are set, this will be used.\n   *\n   * Example:\n   * ```javascript\n   *        // use a WebSocket\n   *        client.webSocketFactory= function () {\n   *          return new WebSocket(\"wss://broker.329broker.com:15674/ws\");\n   *        };\n   *\n   *        // Typical usage with SockJS\n   *        client.webSocketFactory= function () {\n   *          return new SockJS(\"http://broker.329broker.com/stomp\");\n   *        };\n   * ```\n   */\n  public webSocketFactory: (() => IStompSocket) | undefined;\n\n  /**\n   * Will retry if Stomp connection is not established in specified milliseconds.\n   * Default 0, which switches off automatic reconnection.\n   */\n  public connectionTimeout: number = 0;\n\n  // As per https://stackoverflow.com/questions/45802988/typescript-use-correct-version-of-settimeout-node-vs-window/56239226#56239226\n  private _connectionWatcher: ReturnType<typeof setTimeout> | undefined; // Timer\n\n  /**\n   *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n   */\n  public reconnectDelay: number = 5000;\n\n  /**\n   * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n   * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n   */\n  private _nextReconnectDelay: number = 0;\n\n  /**\n   * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n   * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n   * Set to 0 for no limit on wait time.\n   */\n  public maxReconnectDelay: number = 15 * 60 * 1000; // 15 minutes in ms\n\n  /**\n   * Reconnection wait time mode, either linear (default) or exponential.\n   * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n   *\n   * ```javascript\n   * client.configure({\n   *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n   *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n   *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n   * })\n   * ```\n   */\n  public reconnectTimeMode: ReconnectionTimeMode = ReconnectionTimeMode.LINEAR;\n\n  /**\n   * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n   */\n  public heartbeatIncoming: number = 10000;\n\n  /**\n   * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n   */\n  public heartbeatOutgoing: number = 10000;\n\n  /**\n   * Outgoing heartbeat strategy.\n   * See https://github.com/stomp-js/stompjs/pull/579\n   *\n   * Can be worker or interval strategy, but will always use `interval`\n   * if web workers are unavailable, for example, in a non-browser environment.\n   *\n   * Using Web Workers may work better on long-running pages\n   * and mobile apps, as the browser may suspend Timers in the main page.\n   * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n   *\n   * When used in a JS environment, use 'worker' or 'interval' as valid values.\n   *\n   * Defaults to `interval` strategy.\n   */\n  public heartbeatStrategy: TickerStrategy = TickerStrategy.Interval;\n\n  /**\n   * This switches on a non-standard behavior while sending WebSocket packets.\n   * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n   * Only Java Spring brokers seem to support this mode.\n   *\n   * WebSockets, by itself, split large (text) packets,\n   * so it is not needed with a truly compliant STOMP/WebSocket broker.\n   * Setting it for such a broker will cause large messages to fail.\n   *\n   * `false` by default.\n   *\n   * Binary frames are never split.\n   */\n  public splitLargeFrames: boolean = false;\n\n  /**\n   * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n   * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n   */\n  public maxWebSocketChunkSize: number = 8 * 1024;\n\n  /**\n   * Usually the\n   * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n   * is automatically decided by type of the payload.\n   * Default is `false`, which should work with all compliant brokers.\n   *\n   * Set this flag to force binary frames.\n   */\n  public forceBinaryWSFrames: boolean = false;\n\n  /**\n   * A bug in ReactNative chops a string on occurrence of a NULL.\n   * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n   * This makes incoming WebSocket messages invalid STOMP packets.\n   * Setting this flag attempts to reverse the damage by appending a NULL.\n   * If the broker splits a large message into multiple WebSocket messages,\n   * this flag will cause data loss and abnormal termination of connection.\n   *\n   * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n   */\n  public appendMissingNULLonIncoming: boolean = false;\n\n  /**\n   * Underlying WebSocket instance, READONLY.\n   */\n  get webSocket(): IStompSocket | undefined {\n    return this._stompHandler?._webSocket;\n  }\n\n  /**\n   * Connection headers, important keys - `login`, `passcode`, `host`.\n   * Though STOMP 1.2 standard marks these keys to be present, check your broker documentation for\n   * details specific to your broker.\n   */\n  public connectHeaders: StompHeaders;\n\n  /**\n   * Disconnection headers.\n   */\n  get disconnectHeaders(): StompHeaders {\n    return this._disconnectHeaders;\n  }\n\n  set disconnectHeaders(value: StompHeaders) {\n    this._disconnectHeaders = value;\n    if (this._stompHandler) {\n      this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n    }\n  }\n  private _disconnectHeaders: StompHeaders;\n\n  /**\n   * This function will be called for any unhandled messages.\n   * It is useful for receiving messages sent to RabbitMQ temporary queues.\n   *\n   * It can also get invoked with stray messages while the server is processing\n   * a request to [Client#unsubscribe]{@link Client#unsubscribe}\n   * from an endpoint.\n   *\n   * The actual {@link IMessage} will be passed as parameter to the callback.\n   */\n  public onUnhandledMessage: messageCallbackType;\n\n  /**\n   * STOMP brokers can be requested to notify when an operation is actually completed.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}. See\n   * [Client#watchForReceipt]{@link Client#watchForReceipt} for examples.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onUnhandledReceipt: frameCallbackType;\n\n  /**\n   * Will be invoked if {@link IFrame} of an unknown type is received from the STOMP broker.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onUnhandledFrame: frameCallbackType;\n\n  /**\n   * `true` if there is an active connection to STOMP Broker\n   */\n  get connected(): boolean {\n    return !!this._stompHandler && this._stompHandler.connected;\n  }\n\n  /**\n   * Callback, invoked on before a connection to the STOMP broker.\n   *\n   * You can change options on the client, which will impact the immediate connecting.\n   * It is valid to call [Client#decativate]{@link Client#deactivate} in this callback.\n   *\n   * As of version 5.1, this callback can be\n   * [async](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/async_function)\n   * (i.e., it can return a\n   * [Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)).\n   * In that case, connect will be called only after the Promise is resolved.\n   * This can be used to reliably fetch credentials, access token etc. from some other service\n   * in an asynchronous way.\n   */\n  public beforeConnect: (client: Client) => void | Promise<void>;\n\n  /**\n   * Callback, invoked on every successful connection to the STOMP broker.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   * Sometimes clients will like to use headers from this frame.\n   */\n  public onConnect: frameCallbackType;\n\n  /**\n   * Callback, invoked on every successful disconnection from the STOMP broker. It will not be invoked if\n   * the STOMP broker disconnected due to an error.\n   *\n   * The actual Receipt {@link IFrame} acknowledging the DISCONNECT will be passed as parameter to the callback.\n   *\n   * The way STOMP protocol is designed, the connection may close/terminate without the client\n   * receiving the Receipt {@link IFrame} acknowledging the DISCONNECT.\n   * You might find [Client#onWebSocketClose]{@link Client#onWebSocketClose} more appropriate to watch\n   * STOMP broker disconnects.\n   */\n  public onDisconnect: frameCallbackType;\n\n  /**\n   * Callback, invoked on an ERROR frame received from the STOMP Broker.\n   * A compliant STOMP Broker will close the connection after this type of frame.\n   * Please check broker specific documentation for exact behavior.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onStompError: frameCallbackType;\n\n  /**\n   * Callback, invoked when underlying WebSocket is closed.\n   *\n   * Actual [CloseEvent]{@link https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent}\n   * is passed as parameter to the callback.\n   */\n  public onWebSocketClose: closeEventCallbackType;\n\n  /**\n   * Callback, invoked when underlying WebSocket raises an error.\n   *\n   * Actual [Event]{@link https://developer.mozilla.org/en-US/docs/Web/API/Event}\n   * is passed as parameter to the callback.\n   */\n  public onWebSocketError: wsErrorCallbackType;\n\n  /**\n   * Set it to log the actual raw communication with the broker.\n   * When unset, it logs headers of the parsed frames.\n   *\n   * Changes effect from the next broker reconnect.\n   *\n   * **Caution: this assumes that frames only have valid UTF8 strings.**\n   */\n  public logRawCommunication: boolean;\n\n  /**\n   * By default, debug messages are discarded. To log to `console` following can be used:\n   *\n   * ```javascript\n   *        client.debug = function(str) {\n   *          console.log(str);\n   *        };\n   * ```\n   *\n   * Currently this method does not support levels of log. Be aware that the\n   * output can be quite verbose\n   * and may contain sensitive information (like passwords, tokens etc.).\n   */\n  public debug: debugFnType;\n\n  /**\n   * Browsers do not immediately close WebSockets when `.close` is issued.\n   * This may cause reconnection to take a significantly long time in case\n   *  of some types of failures.\n   * In case of incoming heartbeat failure, this experimental flag instructs\n   * the library to discard the socket immediately\n   * (even before it is actually closed).\n   */\n  public discardWebsocketOnCommFailure: boolean = false;\n\n  /**\n   * version of STOMP protocol negotiated with the server, READONLY\n   */\n  get connectedVersion(): string | undefined {\n    return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n  }\n\n  private _stompHandler: StompHandler | undefined;\n\n  /**\n   * if the client is active (connected or going to reconnect)\n   */\n  get active(): boolean {\n    return this.state === ActivationState.ACTIVE;\n  }\n\n  /**\n   * It will be called on state change.\n   *\n   * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n   */\n  public onChangeState: (state: ActivationState) => void;\n\n  private _changeState(state: ActivationState) {\n    this.state = state;\n    this.onChangeState(state);\n  }\n\n  /**\n   * Activation state.\n   *\n   * It will usually be ACTIVE or INACTIVE.\n   * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n   */\n  public state: ActivationState = ActivationState.INACTIVE;\n\n  private _reconnector: any;\n\n  /**\n   * Create an instance.\n   */\n  constructor(conf: StompConfig = {}) {\n    // No op callbacks\n    const noOp = () => {};\n    this.debug = noOp;\n    this.beforeConnect = noOp;\n    this.onConnect = noOp;\n    this.onDisconnect = noOp;\n    this.onUnhandledMessage = noOp;\n    this.onUnhandledReceipt = noOp;\n    this.onUnhandledFrame = noOp;\n    this.onStompError = noOp;\n    this.onWebSocketClose = noOp;\n    this.onWebSocketError = noOp;\n    this.logRawCommunication = false;\n    this.onChangeState = noOp;\n\n    // These parameters would typically get proper values before connect is called\n    this.connectHeaders = {};\n    this._disconnectHeaders = {};\n\n    // Apply configuration\n    this.configure(conf);\n  }\n\n  /**\n   * Update configuration.\n   */\n  public configure(conf: StompConfig): void {\n    // bulk assign all properties to this\n    (Object as any).assign(this, conf);\n\n    // Warn on incorrect maxReconnectDelay settings\n    if (\n      this.maxReconnectDelay > 0 &&\n      this.maxReconnectDelay < this.reconnectDelay\n    ) {\n      this.debug(\n        `Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`,\n      );\n      this.maxReconnectDelay = this.reconnectDelay;\n    }\n  }\n\n  /**\n   * Initiate the connection with the broker.\n   * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n   * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n   * is set to EXPONENTIAL it will increase the wait time exponentially\n   *\n   * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n   */\n  public activate(): void {\n    const _activate = () => {\n      if (this.active) {\n        this.debug('Already ACTIVE, ignoring request to activate');\n        return;\n      }\n\n      this._changeState(ActivationState.ACTIVE);\n\n      this._nextReconnectDelay = this.reconnectDelay;\n      this._connect();\n    };\n\n    // if it is deactivating, wait for it to complete before activating.\n    if (this.state === ActivationState.DEACTIVATING) {\n      this.debug('Waiting for deactivation to finish before activating');\n      this.deactivate().then(() => {\n        _activate();\n      });\n    } else {\n      _activate();\n    }\n  }\n\n  private async _connect(): Promise<void> {\n    await this.beforeConnect(this);\n\n    if (this._stompHandler) {\n      this.debug(\n        'There is already a stompHandler, skipping the call to connect',\n      );\n      return;\n    }\n\n    if (!this.active) {\n      this.debug(\n        'Client has been marked inactive, will not attempt to connect',\n      );\n      return;\n    }\n\n    // setup connection watcher\n    if (this.connectionTimeout > 0) {\n      // clear first\n      if (this._connectionWatcher) {\n        clearTimeout(this._connectionWatcher);\n      }\n      this._connectionWatcher = setTimeout(() => {\n        if (this.connected) {\n          return;\n        }\n        // Connection not established, close the underlying socket\n        // a reconnection will be attempted\n        this.debug(\n          `Connection not established in ${this.connectionTimeout}ms, closing socket`,\n        );\n        this.forceDisconnect();\n      }, this.connectionTimeout);\n    }\n\n    this.debug('Opening Web Socket...');\n\n    // Get the actual WebSocket (or a similar object)\n    const webSocket = this._createWebSocket();\n\n    this._stompHandler = new StompHandler(this, webSocket, {\n      debug: this.debug,\n      stompVersions: this.stompVersions,\n      connectHeaders: this.connectHeaders,\n      disconnectHeaders: this._disconnectHeaders,\n      heartbeatIncoming: this.heartbeatIncoming,\n      heartbeatOutgoing: this.heartbeatOutgoing,\n      heartbeatStrategy: this.heartbeatStrategy,\n      splitLargeFrames: this.splitLargeFrames,\n      maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n      forceBinaryWSFrames: this.forceBinaryWSFrames,\n      logRawCommunication: this.logRawCommunication,\n      appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n      discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n\n      onConnect: frame => {\n        // Successfully connected, stop the connection watcher\n        if (this._connectionWatcher) {\n          clearTimeout(this._connectionWatcher);\n          this._connectionWatcher = undefined;\n        }\n\n        if (!this.active) {\n          this.debug(\n            'STOMP got connected while deactivate was issued, will disconnect now',\n          );\n          this._disposeStompHandler();\n          return;\n        }\n        this.onConnect(frame);\n      },\n      onDisconnect: frame => {\n        this.onDisconnect(frame);\n      },\n      onStompError: frame => {\n        this.onStompError(frame);\n      },\n      onWebSocketClose: evt => {\n        this._stompHandler = undefined; // a new one will be created in case of a reconnect\n\n        if (this.state === ActivationState.DEACTIVATING) {\n          // Mark deactivation complete\n          this._changeState(ActivationState.INACTIVE);\n        }\n\n        // The callback is called before attempting to reconnect, this would allow the client\n        // to be `deactivated` in the callback.\n        this.onWebSocketClose(evt);\n\n        if (this.active) {\n          this._schedule_reconnect();\n        }\n      },\n      onWebSocketError: evt => {\n        this.onWebSocketError(evt);\n      },\n      onUnhandledMessage: message => {\n        this.onUnhandledMessage(message);\n      },\n      onUnhandledReceipt: frame => {\n        this.onUnhandledReceipt(frame);\n      },\n      onUnhandledFrame: frame => {\n        this.onUnhandledFrame(frame);\n      },\n    });\n\n    this._stompHandler.start();\n  }\n\n  private _createWebSocket(): IStompSocket {\n    let webSocket: IStompSocket;\n\n    if (this.webSocketFactory) {\n      webSocket = this.webSocketFactory();\n    } else if (this.brokerURL) {\n      webSocket = new WebSocket(\n        this.brokerURL,\n        this.stompVersions.protocolVersions(),\n      );\n    } else {\n      throw new Error('Either brokerURL or webSocketFactory must be provided');\n    }\n    webSocket.binaryType = 'arraybuffer';\n    return webSocket;\n  }\n\n  private _schedule_reconnect(): void {\n    if (this._nextReconnectDelay > 0) {\n      this.debug(\n        `STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`,\n      );\n\n      this._reconnector = setTimeout(() => {\n        if (this.reconnectTimeMode === ReconnectionTimeMode.EXPONENTIAL) {\n          this._nextReconnectDelay = this._nextReconnectDelay * 2;\n\n          // Truncated exponential backoff with a set limit unless disabled\n          if (this.maxReconnectDelay !== 0) {\n            this._nextReconnectDelay = Math.min(\n              this._nextReconnectDelay,\n              this.maxReconnectDelay,\n            );\n          }\n        }\n\n        this._connect();\n      }, this._nextReconnectDelay);\n    }\n  }\n\n  /**\n   * Disconnect if connected and stop auto reconnect loop.\n   * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n   *\n   * This call is async. It will resolve immediately if there is no underlying active websocket,\n   * otherwise, it will resolve after the underlying websocket is properly disposed of.\n   *\n   * It is not an error to invoke this method more than once.\n   * Each of those would resolve on completion of deactivation.\n   *\n   * To reactivate, you can call [Client#activate]{@link Client#activate}.\n   *\n   * Experimental: pass `force: true` to immediately discard the underlying connection.\n   * This mode will skip both the STOMP and the Websocket shutdown sequences.\n   * In some cases, browsers take a long time in the Websocket shutdown\n   * if the underlying connection had gone stale.\n   * Using this mode can speed up.\n   * When this mode is used, the actual Websocket may linger for a while\n   * and the broker may not realize that the connection is no longer in use.\n   *\n   * It is possible to invoke this method initially without the `force` option\n   * and subsequently, say after a wait, with the `force` option.\n   */\n  public async deactivate(options: { force?: boolean } = {}): Promise<void> {\n    const force: boolean = options.force || false;\n    const needToDispose = this.active;\n    let retPromise: Promise<void>;\n\n    if (this.state === ActivationState.INACTIVE) {\n      this.debug(`Already INACTIVE, nothing more to do`);\n      return Promise.resolve();\n    }\n\n    this._changeState(ActivationState.DEACTIVATING);\n\n    // Reset reconnection timer just to be safe\n    this._nextReconnectDelay = 0;\n\n    // Clear if a reconnection was scheduled\n    if (this._reconnector) {\n      clearTimeout(this._reconnector);\n      this._reconnector = undefined;\n    }\n\n    if (\n      this._stompHandler &&\n      // @ts-ignore - if there is a _stompHandler, there is the webSocket\n      this.webSocket.readyState !== StompSocketState.CLOSED\n    ) {\n      const origOnWebSocketClose = this._stompHandler.onWebSocketClose;\n      // we need to wait for the underlying websocket to close\n      retPromise = new Promise<void>((resolve, reject) => {\n        // @ts-ignore - there is a _stompHandler\n        this._stompHandler.onWebSocketClose = evt => {\n          origOnWebSocketClose(evt);\n          resolve();\n        };\n      });\n    } else {\n      // indicate that auto reconnect loop should terminate\n      this._changeState(ActivationState.INACTIVE);\n      return Promise.resolve();\n    }\n\n    if (force) {\n      this._stompHandler?.discardWebsocket();\n    } else if (needToDispose) {\n      this._disposeStompHandler();\n    }\n\n    return retPromise;\n  }\n\n  /**\n   * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n   * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n   * After forcing disconnect, automatic reconnect will be attempted.\n   * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n   */\n  public forceDisconnect() {\n    if (this._stompHandler) {\n      this._stompHandler.forceDisconnect();\n    }\n  }\n\n  private _disposeStompHandler() {\n    // Dispose STOMP Handler\n    if (this._stompHandler) {\n      this._stompHandler.dispose();\n    }\n  }\n\n  /**\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations.\n   *\n   * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n   *\n   * `body` must be String.\n   * You will need to covert the payload to string in case it is not string (e.g. JSON).\n   *\n   * To send a binary message body, use `binaryBody` parameter. It should be a\n   * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n   * Sometimes brokers may not support binary frames out of the box.\n   * Please check your broker documentation.\n   *\n   * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n   * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n   * For binary messages, `content-length` header is always added.\n   *\n   * Caution: The broker will, most likely, report an error and disconnect\n   * if the message body has NULL octet(s) and `content-length` header is missing.\n   *\n   * ```javascript\n   *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n   *\n   *        // Only destination is mandatory parameter\n   *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n   *\n   *        // Skip content-length header in the frame to the broker\n   *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n   *\n   *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n   *        // setting content-type header is not mandatory, however a good practice\n   *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n   *                         headers: {'content-type': 'application/octet-stream'}});\n   * ```\n   */\n  public publish(params: IPublishParams) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.publish(params);\n  }\n\n  private _checkConnection() {\n    if (!this.connected) {\n      throw new TypeError('There is no underlying STOMP connection');\n    }\n  }\n\n  /**\n   * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n   * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n   * The value (say receipt-id) for this header needs to be unique for each use.\n   * Typically, a sequence, a UUID, a random number or a combination may be used.\n   *\n   * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n   * The operation needs to be matched based on the value of the receipt-id.\n   *\n   * This method allows watching for a receipt and invoking the callback\n   *  when the corresponding receipt has been received.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   *\n   * Example:\n   * ```javascript\n   *        // Subscribing with acknowledgement\n   *        let receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *\n   *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n   *\n   *\n   *        // Publishing with acknowledgement\n   *        receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n   * ```\n   */\n  public watchForReceipt(receiptId: string, callback: frameCallbackType): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.watchForReceipt(receiptId, callback);\n  }\n\n  /**\n   * Subscribe to a STOMP Broker location. The callback will be invoked for each\n   * received message with the {@link IMessage} as argument.\n   *\n   * Note: The library will generate a unique ID if there is none provided in the headers.\n   *       To use your own ID, pass it using the `headers` argument.\n   *\n   * ```javascript\n   *        callback = function(message) {\n   *        // called when the client receives a STOMP message from the server\n   *          if (message.body) {\n   *            alert(\"got message with body \" + message.body)\n   *          } else {\n   *            alert(\"got empty message\");\n   *          }\n   *        });\n   *\n   *        var subscription = client.subscribe(\"/queue/test\", callback);\n   *\n   *        // Explicit subscription id\n   *        var mySubId = 'my-subscription-id-001';\n   *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n   * ```\n   */\n  public subscribe(\n    destination: string,\n    callback: messageCallbackType,\n    headers: StompHeaders = {},\n  ): StompSubscription {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.subscribe(destination, callback, headers);\n  }\n\n  /**\n   * It is preferable to unsubscribe from a subscription by calling\n   * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n   *\n   * ```javascript\n   *        var subscription = client.subscribe(destination, onmessage);\n   *        // ...\n   *        subscription.unsubscribe();\n   * ```\n   *\n   * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n   */\n  public unsubscribe(id: string, headers: StompHeaders = {}): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.unsubscribe(id, headers);\n  }\n\n  /**\n   * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n   * and [abort]{@link ITransaction#abort}.\n   *\n   * `transactionId` is optional, if not passed the library will generate it internally.\n   */\n  public begin(transactionId?: string): ITransaction {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.begin(transactionId);\n  }\n\n  /**\n   * Commit a transaction.\n   *\n   * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.commit();\n   * ```\n   */\n  public commit(transactionId: string): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.commit(transactionId);\n  }\n\n  /**\n   * Abort a transaction.\n   * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.abort();\n   * ```\n   */\n  public abort(transactionId: string): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.abort(transactionId);\n  }\n\n  /**\n   * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // acknowledge it\n   *          message.ack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  public ack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {},\n  ): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.ack(messageId, subscriptionId, headers);\n  }\n\n  /**\n   * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // an error occurs, nack it\n   *          message.nack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  public nack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {},\n  ): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.nack(messageId, subscriptionId, headers);\n  }\n}\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AAGjD,SACEC,eAAe,EAOfC,oBAAoB,EACpBC,gBAAgB,EAChBC,cAAc,QAET,YAAY;AACnB,SAASC,QAAQ,QAAQ,eAAe;AAUxC;;;;;AAKA,OAAM,MAAOC,MAAM;EA8JjB;;;EAGA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,aAAa,EAAEC,UAAU;EACvC;EASA;;;EAGA,IAAIC,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACC,kBAAkB;EAChC;EAEA,IAAID,iBAAiBA,CAACE,KAAmB;IACvC,IAAI,CAACD,kBAAkB,GAAGC,KAAK;IAC/B,IAAI,IAAI,CAACJ,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACE,iBAAiB,GAAG,IAAI,CAACC,kBAAkB;IAChE;EACF;EA+BA;;;EAGA,IAAIE,SAASA,CAAA;IACX,OAAO,CAAC,CAAC,IAAI,CAACL,aAAa,IAAI,IAAI,CAACA,aAAa,CAACK,SAAS;EAC7D;EAmGA;;;EAGA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACN,aAAa,GAAG,IAAI,CAACA,aAAa,CAACM,gBAAgB,GAAGC,SAAS;EAC7E;EAIA;;;EAGA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACC,KAAK,KAAKhB,eAAe,CAACiB,MAAM;EAC9C;EASQC,YAAYA,CAACF,KAAsB;IACzC,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,aAAa,CAACH,KAAK,CAAC;EAC3B;EAYA;;;EAGAI,YAAYC,IAAA,GAAoB,EAAE;IA3VlC;;;;;;;;;IASO,KAAAC,aAAa,GAAGlB,QAAQ,CAACmB,OAAO;IAyBvC;;;;IAIO,KAAAC,iBAAiB,GAAW,CAAC;IAKpC;;;IAGO,KAAAC,cAAc,GAAW,IAAI;IAEpC;;;;IAIQ,KAAAC,mBAAmB,GAAW,CAAC;IAEvC;;;;;IAKO,KAAAC,iBAAiB,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAEnD;;;;;;;;;;;;IAYO,KAAAC,iBAAiB,GAAyB3B,oBAAoB,CAAC4B,MAAM;IAE5E;;;IAGO,KAAAC,iBAAiB,GAAW,KAAK;IAExC;;;IAGO,KAAAC,iBAAiB,GAAW,KAAK;IAExC;;;;;;;;;;;;;;;IAeO,KAAAC,iBAAiB,GAAmB7B,cAAc,CAAC8B,QAAQ;IAElE;;;;;;;;;;;;;IAaO,KAAAC,gBAAgB,GAAY,KAAK;IAExC;;;;IAIO,KAAAC,qBAAqB,GAAW,CAAC,GAAG,IAAI;IAE/C;;;;;;;;IAQO,KAAAC,mBAAmB,GAAY,KAAK;IAE3C;;;;;;;;;;IAUO,KAAAC,2BAA2B,GAAY,KAAK;IAyJnD;;;;;;;;IAQO,KAAAC,6BAA6B,GAAY,KAAK;IA8BrD;;;;;;IAMO,KAAAtB,KAAK,GAAoBhB,eAAe,CAACuC,QAAQ;IAQtD;IACA,MAAMC,IAAI,GAAGA,CAAA,KAAK,CAAE,CAAC;IACrB,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACE,aAAa,GAAGF,IAAI;IACzB,IAAI,CAACG,SAAS,GAAGH,IAAI;IACrB,IAAI,CAACI,YAAY,GAAGJ,IAAI;IACxB,IAAI,CAACK,kBAAkB,GAAGL,IAAI;IAC9B,IAAI,CAACM,kBAAkB,GAAGN,IAAI;IAC9B,IAAI,CAACO,gBAAgB,GAAGP,IAAI;IAC5B,IAAI,CAACQ,YAAY,GAAGR,IAAI;IACxB,IAAI,CAACS,gBAAgB,GAAGT,IAAI;IAC5B,IAAI,CAACU,gBAAgB,GAAGV,IAAI;IAC5B,IAAI,CAACW,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAChC,aAAa,GAAGqB,IAAI;IAEzB;IACA,IAAI,CAACY,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC1C,kBAAkB,GAAG,EAAE;IAE5B;IACA,IAAI,CAAC2C,SAAS,CAAChC,IAAI,CAAC;EACtB;EAEA;;;EAGOgC,SAASA,CAAChC,IAAiB;IAChC;IACCiC,MAAc,CAACC,MAAM,CAAC,IAAI,EAAElC,IAAI,CAAC;IAElC;IACA,IACE,IAAI,CAACM,iBAAiB,GAAG,CAAC,IAC1B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACF,cAAc,EAC5C;MACA,IAAI,CAACgB,KAAK,CACR,+BAA+B,IAAI,CAACd,iBAAiB,oCAAoC,IAAI,CAACF,cAAc,2DAA2D,CACxK;MACD,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACF,cAAc;IAC9C;EACF;EAEA;;;;;;;;EAQO+B,QAAQA,CAAA;IACb,MAAMC,SAAS,GAAGA,CAAA,KAAK;MACrB,IAAI,IAAI,CAAC1C,MAAM,EAAE;QACf,IAAI,CAAC0B,KAAK,CAAC,8CAA8C,CAAC;QAC1D;MACF;MAEA,IAAI,CAACvB,YAAY,CAAClB,eAAe,CAACiB,MAAM,CAAC;MAEzC,IAAI,CAACS,mBAAmB,GAAG,IAAI,CAACD,cAAc;MAC9C,IAAI,CAACiC,QAAQ,EAAE;IACjB,CAAC;IAED;IACA,IAAI,IAAI,CAAC1C,KAAK,KAAKhB,eAAe,CAAC2D,YAAY,EAAE;MAC/C,IAAI,CAAClB,KAAK,CAAC,sDAAsD,CAAC;MAClE,IAAI,CAACmB,UAAU,EAAE,CAACC,IAAI,CAAC,MAAK;QAC1BJ,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,SAAS,EAAE;IACb;EACF;EAEQ,MAAMC,QAAQA,CAAA;IACpB,MAAM,IAAI,CAAChB,aAAa,CAAC,IAAI,CAAC;IAE9B,IAAI,IAAI,CAACnC,aAAa,EAAE;MACtB,IAAI,CAACkC,KAAK,CACR,+DAA+D,CAChE;MACD;IACF;IAEA,IAAI,CAAC,IAAI,CAAC1B,MAAM,EAAE;MAChB,IAAI,CAAC0B,KAAK,CACR,8DAA8D,CAC/D;MACD;IACF;IAEA;IACA,IAAI,IAAI,CAACjB,iBAAiB,GAAG,CAAC,EAAE;MAC9B;MACA,IAAI,IAAI,CAACsC,kBAAkB,EAAE;QAC3BC,YAAY,CAAC,IAAI,CAACD,kBAAkB,CAAC;MACvC;MACA,IAAI,CAACA,kBAAkB,GAAGE,UAAU,CAAC,MAAK;QACxC,IAAI,IAAI,CAACpD,SAAS,EAAE;UAClB;QACF;QACA;QACA;QACA,IAAI,CAAC6B,KAAK,CACR,iCAAiC,IAAI,CAACjB,iBAAiB,oBAAoB,CAC5E;QACD,IAAI,CAACyC,eAAe,EAAE;MACxB,CAAC,EAAE,IAAI,CAACzC,iBAAiB,CAAC;IAC5B;IAEA,IAAI,CAACiB,KAAK,CAAC,uBAAuB,CAAC;IAEnC;IACA,MAAMnC,SAAS,GAAG,IAAI,CAAC4D,gBAAgB,EAAE;IAEzC,IAAI,CAAC3D,aAAa,GAAG,IAAIR,YAAY,CAAC,IAAI,EAAEO,SAAS,EAAE;MACrDmC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBnB,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC8B,cAAc,EAAE,IAAI,CAACA,cAAc;MACnC3C,iBAAiB,EAAE,IAAI,CAACC,kBAAkB;MAC1CoB,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCE,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;MACjDC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7Ce,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7Cd,2BAA2B,EAAE,IAAI,CAACA,2BAA2B;MAC7DC,6BAA6B,EAAE,IAAI,CAACA,6BAA6B;MAEjEK,SAAS,EAAEwB,KAAK,IAAG;QACjB;QACA,IAAI,IAAI,CAACL,kBAAkB,EAAE;UAC3BC,YAAY,CAAC,IAAI,CAACD,kBAAkB,CAAC;UACrC,IAAI,CAACA,kBAAkB,GAAGhD,SAAS;QACrC;QAEA,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;UAChB,IAAI,CAAC0B,KAAK,CACR,sEAAsE,CACvE;UACD,IAAI,CAAC2B,oBAAoB,EAAE;UAC3B;QACF;QACA,IAAI,CAACzB,SAAS,CAACwB,KAAK,CAAC;MACvB,CAAC;MACDvB,YAAY,EAAEuB,KAAK,IAAG;QACpB,IAAI,CAACvB,YAAY,CAACuB,KAAK,CAAC;MAC1B,CAAC;MACDnB,YAAY,EAAEmB,KAAK,IAAG;QACpB,IAAI,CAACnB,YAAY,CAACmB,KAAK,CAAC;MAC1B,CAAC;MACDlB,gBAAgB,EAAEoB,GAAG,IAAG;QACtB,IAAI,CAAC9D,aAAa,GAAGO,SAAS,CAAC,CAAC;QAEhC,IAAI,IAAI,CAACE,KAAK,KAAKhB,eAAe,CAAC2D,YAAY,EAAE;UAC/C;UACA,IAAI,CAACzC,YAAY,CAAClB,eAAe,CAACuC,QAAQ,CAAC;QAC7C;QAEA;QACA;QACA,IAAI,CAACU,gBAAgB,CAACoB,GAAG,CAAC;QAE1B,IAAI,IAAI,CAACtD,MAAM,EAAE;UACf,IAAI,CAACuD,mBAAmB,EAAE;QAC5B;MACF,CAAC;MACDpB,gBAAgB,EAAEmB,GAAG,IAAG;QACtB,IAAI,CAACnB,gBAAgB,CAACmB,GAAG,CAAC;MAC5B,CAAC;MACDxB,kBAAkB,EAAE0B,OAAO,IAAG;QAC5B,IAAI,CAAC1B,kBAAkB,CAAC0B,OAAO,CAAC;MAClC,CAAC;MACDzB,kBAAkB,EAAEqB,KAAK,IAAG;QAC1B,IAAI,CAACrB,kBAAkB,CAACqB,KAAK,CAAC;MAChC,CAAC;MACDpB,gBAAgB,EAAEoB,KAAK,IAAG;QACxB,IAAI,CAACpB,gBAAgB,CAACoB,KAAK,CAAC;MAC9B;KACD,CAAC;IAEF,IAAI,CAAC5D,aAAa,CAACiE,KAAK,EAAE;EAC5B;EAEQN,gBAAgBA,CAAA;IACtB,IAAI5D,SAAuB;IAE3B,IAAI,IAAI,CAACmE,gBAAgB,EAAE;MACzBnE,SAAS,GAAG,IAAI,CAACmE,gBAAgB,EAAE;IACrC,CAAC,MAAM,IAAI,IAAI,CAACC,SAAS,EAAE;MACzBpE,SAAS,GAAG,IAAIqE,SAAS,CACvB,IAAI,CAACD,SAAS,EACd,IAAI,CAACpD,aAAa,CAACsD,gBAAgB,EAAE,CACtC;IACH,CAAC,MAAM;MACL,MAAM,IAAIC,KAAK,CAAC,uDAAuD,CAAC;IAC1E;IACAvE,SAAS,CAACwE,UAAU,GAAG,aAAa;IACpC,OAAOxE,SAAS;EAClB;EAEQgE,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAAC5C,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACe,KAAK,CACR,qCAAqC,IAAI,CAACf,mBAAmB,IAAI,CAClE;MAED,IAAI,CAACqD,YAAY,GAAGf,UAAU,CAAC,MAAK;QAClC,IAAI,IAAI,CAACpC,iBAAiB,KAAK3B,oBAAoB,CAAC+E,WAAW,EAAE;UAC/D,IAAI,CAACtD,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,GAAG,CAAC;UAEvD;UACA,IAAI,IAAI,CAACC,iBAAiB,KAAK,CAAC,EAAE;YAChC,IAAI,CAACD,mBAAmB,GAAGuD,IAAI,CAACC,GAAG,CACjC,IAAI,CAACxD,mBAAmB,EACxB,IAAI,CAACC,iBAAiB,CACvB;UACH;QACF;QAEA,IAAI,CAAC+B,QAAQ,EAAE;MACjB,CAAC,EAAE,IAAI,CAAChC,mBAAmB,CAAC;IAC9B;EACF;EAEA;;;;;;;;;;;;;;;;;;;;;;;EAuBO,MAAMkC,UAAUA,CAACuB,OAAA,GAA+B,EAAE;IACvD,MAAMC,KAAK,GAAYD,OAAO,CAACC,KAAK,IAAI,KAAK;IAC7C,MAAMC,aAAa,GAAG,IAAI,CAACtE,MAAM;IACjC,IAAIuE,UAAyB;IAE7B,IAAI,IAAI,CAACtE,KAAK,KAAKhB,eAAe,CAACuC,QAAQ,EAAE;MAC3C,IAAI,CAACE,KAAK,CAAC,sCAAsC,CAAC;MAClD,OAAO8C,OAAO,CAACC,OAAO,EAAE;IAC1B;IAEA,IAAI,CAACtE,YAAY,CAAClB,eAAe,CAAC2D,YAAY,CAAC;IAE/C;IACA,IAAI,CAACjC,mBAAmB,GAAG,CAAC;IAE5B;IACA,IAAI,IAAI,CAACqD,YAAY,EAAE;MACrBhB,YAAY,CAAC,IAAI,CAACgB,YAAY,CAAC;MAC/B,IAAI,CAACA,YAAY,GAAGjE,SAAS;IAC/B;IAEA,IACE,IAAI,CAACP,aAAa;IAClB;IACA,IAAI,CAACD,SAAS,CAACmF,UAAU,KAAKvF,gBAAgB,CAACwF,MAAM,EACrD;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAACpF,aAAa,CAAC0C,gBAAgB;MAChE;MACAqC,UAAU,GAAG,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEI,MAAM,KAAI;QACjD;QACA,IAAI,CAACrF,aAAa,CAAC0C,gBAAgB,GAAGoB,GAAG,IAAG;UAC1CsB,oBAAoB,CAACtB,GAAG,CAAC;UACzBmB,OAAO,EAAE;QACX,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACtE,YAAY,CAAClB,eAAe,CAACuC,QAAQ,CAAC;MAC3C,OAAOgD,OAAO,CAACC,OAAO,EAAE;IAC1B;IAEA,IAAIJ,KAAK,EAAE;MACT,IAAI,CAAC7E,aAAa,EAAEsF,gBAAgB,EAAE;IACxC,CAAC,MAAM,IAAIR,aAAa,EAAE;MACxB,IAAI,CAACjB,oBAAoB,EAAE;IAC7B;IAEA,OAAOkB,UAAU;EACnB;EAEA;;;;;;EAMOrB,eAAeA,CAAA;IACpB,IAAI,IAAI,CAAC1D,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAAC0D,eAAe,EAAE;IACtC;EACF;EAEQG,oBAAoBA,CAAA;IAC1B;IACA,IAAI,IAAI,CAAC7D,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACuF,OAAO,EAAE;IAC9B;EACF;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAoCOC,OAAOA,CAACC,MAAsB;IACnC,IAAI,CAACC,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAC1F,aAAa,CAACwF,OAAO,CAACC,MAAM,CAAC;EACpC;EAEQC,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACrF,SAAS,EAAE;MACnB,MAAM,IAAIsF,SAAS,CAAC,yCAAyC,CAAC;IAChE;EACF;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmCOC,eAAeA,CAACC,SAAiB,EAAEC,QAA2B;IACnE,IAAI,CAACJ,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAC1F,aAAa,CAAC4F,eAAe,CAACC,SAAS,EAAEC,QAAQ,CAAC;EACzD;EAEA;;;;;;;;;;;;;;;;;;;;;;;;EAwBOC,SAASA,CACdC,WAAmB,EACnBF,QAA6B,EAC7BG,OAAA,GAAwB,EAAE;IAE1B,IAAI,CAACP,gBAAgB,EAAE;IACvB;IACA,OAAO,IAAI,CAAC1F,aAAa,CAAC+F,SAAS,CAACC,WAAW,EAAEF,QAAQ,EAAEG,OAAO,CAAC;EACrE;EAEA;;;;;;;;;;;;EAYOC,WAAWA,CAACC,EAAU,EAAEF,OAAA,GAAwB,EAAE;IACvD,IAAI,CAACP,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAC1F,aAAa,CAACkG,WAAW,CAACC,EAAE,EAAEF,OAAO,CAAC;EAC7C;EAEA;;;;;;EAMOG,KAAKA,CAACC,aAAsB;IACjC,IAAI,CAACX,gBAAgB,EAAE;IACvB;IACA,OAAO,IAAI,CAAC1F,aAAa,CAACoG,KAAK,CAACC,aAAa,CAAC;EAChD;EAEA;;;;;;;;;;;;EAYOC,MAAMA,CAACD,aAAqB;IACjC,IAAI,CAACX,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAC1F,aAAa,CAACsG,MAAM,CAACD,aAAa,CAAC;EAC1C;EAEA;;;;;;;;;;;EAWOE,KAAKA,CAACF,aAAqB;IAChC,IAAI,CAACX,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAC1F,aAAa,CAACuG,KAAK,CAACF,aAAa,CAAC;EACzC;EAEA;;;;;;;;;;;;;EAaOG,GAAGA,CACRC,SAAiB,EACjBC,cAAsB,EACtBT,OAAA,GAAwB,EAAE;IAE1B,IAAI,CAACP,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAC1F,aAAa,CAACwG,GAAG,CAACC,SAAS,EAAEC,cAAc,EAAET,OAAO,CAAC;EAC5D;EAEA;;;;;;;;;;;;;EAaOU,IAAIA,CACTF,SAAiB,EACjBC,cAAsB,EACtBT,OAAA,GAAwB,EAAE;IAE1B,IAAI,CAACP,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAC1F,aAAa,CAAC2G,IAAI,CAACF,SAAS,EAAEC,cAAc,EAAET,OAAO,CAAC;EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}