import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { GlobalStyles } from './styles/GlobalStyles';
import { styledTheme } from './styles/theme';
import { AuthProvider } from './contexts/AuthContext';

// Layout Components
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Page Components
import SimpleLoginPage from './pages/auth/SimpleLoginPage';
import SimpleRegisterPage from './pages/auth/SimpleRegisterPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import ProfilePage from './pages/profile/ProfilePage';
import AppointmentsPage from './pages/appointments/AppointmentsPage';
import ChatPage from './pages/chat/ChatPage';
import VideoConsultationsPage from './pages/video-consultations/VideoConsultationsPage';
import PrescriptionsPage from './pages/prescriptions/PrescriptionsPage';
import AIHealthBotPage from './pages/ai-health-bot/AIHealthBotPage';
import DoctorsPage from './pages/doctors/DoctorsPage';
import NotFoundPage from './pages/NotFoundPage';

// Role-Based Components
import SimplePatientDashboard from './components/dashboard/SimplePatientDashboard';
import SimpleDoctorDashboard from './components/dashboard/SimpleDoctorDashboard';
import AppointmentBooking from './components/appointments/AppointmentBooking';
import RoleBasedRoute from './components/auth/RoleBasedRoute';

// Error Boundary
import ErrorBoundary from './components/common/ErrorBoundary';

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <ThemeProvider theme={styledTheme}>
        <GlobalStyles />
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Navigate to="/auth/login" replace />} />
              <Route path="/auth/login" element={<SimpleLoginPage />} />
              <Route path="/auth/register" element={<SimpleRegisterPage />} />

              {/* Role-Based Dashboard Routes */}
              <Route path="/patient/dashboard" element={
                <RoleBasedRoute allowedRoles={['PATIENT']}>
                  <SimplePatientDashboard />
                </RoleBasedRoute>
              } />

              <Route path="/doctor/dashboard" element={
                <RoleBasedRoute allowedRoles={['DOCTOR']}>
                  <SimpleDoctorDashboard />
                </RoleBasedRoute>
              } />

              {/* Patient-Specific Routes */}
              <Route path="/appointments/book" element={
                <RoleBasedRoute allowedRoles={['PATIENT']}>
                  <AppointmentBooking />
                </RoleBasedRoute>
              } />

              {/* Protected Routes with Layout */}
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }>
                <Route index element={<DashboardPage />} />
                <Route path="profile" element={<ProfilePage />} />
                <Route path="appointments" element={<AppointmentsPage />} />
                <Route path="chat" element={<ChatPage />} />
                <Route path="video-consultations" element={<VideoConsultationsPage />} />
                <Route path="prescriptions" element={<PrescriptionsPage />} />
                <Route path="ai-health-bot" element={<AIHealthBotPage />} />
                <Route path="doctors" element={<DoctorsPage />} />
              </Route>

              {/* 404 Page */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
