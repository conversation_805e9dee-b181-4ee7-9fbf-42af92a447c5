{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new\\\\Meditech\\\\react-frontend\\\\src\\\\components\\\\video\\\\VideoConsultationRoom.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Calendar, Clock, User, Stethoscope, MessageCircle, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';\nimport VideoCallComponent from './VideoCallComponent';\nimport { videoCallService } from '../../services/videoCallService';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoomContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n`;\n_c = RoomContainer;\nconst RoomContent = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  display: grid;\n  grid-template-columns: 1fr 350px;\n  gap: 20px;\n  height: calc(100vh - 40px);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    grid-template-rows: 1fr auto;\n  }\n`;\n_c2 = RoomContent;\nconst VideoSection = styled.div`\n  background: white;\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n`;\n_c3 = VideoSection;\nconst SidePanel = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c4 = SidePanel;\nconst InfoCard = styled.div`\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n`;\n_c5 = InfoCard;\nconst CardTitle = styled.h3`\n  margin: 0 0 16px 0;\n  color: #2d3748;\n  font-size: 18px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c6 = CardTitle;\nconst AppointmentInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n`;\n_c7 = AppointmentInfo;\nconst InfoRow = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  color: #4a5568;\n  font-size: 14px;\n`;\n_c8 = InfoRow;\nconst StatusBadge = styled.span`\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n  \n  background: ${props => {\n  switch (props.status) {\n    case 'IN_PROGRESS':\n      return '#48bb78';\n    case 'WAITING_FOR_DOCTOR':\n      return '#ed8936';\n    case 'WAITING_FOR_PATIENT':\n      return '#4299e1';\n    case 'SCHEDULED':\n      return '#9f7aea';\n    default:\n      return '#718096';\n  }\n}};\n  color: white;\n`;\n_c9 = StatusBadge;\nconst ParticipantCard = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: #f7fafc;\n  border-radius: 8px;\n  margin-bottom: 8px;\n`;\n_c0 = ParticipantCard;\nconst Avatar = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n`;\n_c1 = Avatar;\nconst ParticipantInfo = styled.div`\n  flex: 1;\n`;\n_c10 = ParticipantInfo;\nconst ParticipantName = styled.div`\n  font-weight: 500;\n  color: #2d3748;\n`;\n_c11 = ParticipantName;\nconst ParticipantRole = styled.div`\n  font-size: 12px;\n  color: #718096;\n  text-transform: uppercase;\n`;\n_c12 = ParticipantRole;\nconst ActionButton = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-bottom: 8px;\n  \n  background: ${props => {\n  switch (props.variant) {\n    case 'primary':\n      return '#4299e1';\n    case 'danger':\n      return '#e53e3e';\n    default:\n      return '#edf2f7';\n  }\n}};\n  \n  color: ${props => props.variant === 'secondary' ? '#4a5568' : 'white'};\n  \n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c13 = ActionButton;\nconst LoadingSpinner = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  color: #4a5568;\n`;\n_c14 = LoadingSpinner;\nconst ErrorMessage = styled.div`\n  background: #fed7d7;\n  color: #c53030;\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c15 = ErrorMessage;\nconst SuccessMessage = styled.div`\n  background: #c6f6d5;\n  color: #2f855a;\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c16 = SuccessMessage;\nconst VideoConsultationRoom = () => {\n  _s();\n  var _state$user2;\n  const {\n    roomId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    state\n  } = useAuth();\n  const [consultation, setConsultation] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [isCallActive, setIsCallActive] = useState(false);\n  useEffect(() => {\n    if (roomId) {\n      loadConsultation();\n    }\n  }, [roomId]);\n  const loadConsultation = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const consultationData = await videoCallService.getConsultationByRoomId(roomId);\n      setConsultation(consultationData);\n\n      // Auto-start consultation if it's scheduled\n      if (consultationData.status === 'SCHEDULED') {\n        await startConsultation();\n      } else if (consultationData.status === 'IN_PROGRESS') {\n        setIsCallActive(true);\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to load consultation');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const startConsultation = async () => {\n    if (!consultation) return;\n    try {\n      setError(null);\n      const updatedConsultation = await videoCallService.startConsultation(consultation.id);\n      setConsultation(updatedConsultation);\n      setIsCallActive(true);\n      setSuccess('Consultation started successfully!');\n\n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to start consultation');\n    }\n  };\n  const endConsultation = async () => {\n    if (!consultation) return;\n    try {\n      setError(null);\n      await videoCallService.endConsultation(consultation.id);\n      setIsCallActive(false);\n      setSuccess('Consultation ended successfully!');\n\n      // Redirect after 2 seconds\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 2000);\n    } catch (err) {\n      setError(err.message || 'Failed to end consultation');\n    }\n  };\n  const handleCallEnd = () => {\n    var _state$user;\n    setIsCallActive(false);\n    if (((_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.role) === 'DOCTOR') {\n      // Only doctors can officially end consultations\n      endConsultation();\n    } else {\n      // Patients just leave the call\n      navigate('/dashboard');\n    }\n  };\n  const handleCallError = errorMessage => {\n    setError(errorMessage);\n    setIsCallActive(false);\n  };\n  const getInitials = name => {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  };\n  const formatDateTime = dateTime => {\n    return new Date(dateTime).toLocaleString();\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'IN_PROGRESS':\n        return '#48bb78';\n      case 'WAITING_FOR_DOCTOR':\n        return '#ed8936';\n      case 'WAITING_FOR_PATIENT':\n        return '#4299e1';\n      case 'SCHEDULED':\n        return '#9f7aea';\n      default:\n        return '#718096';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(RoomContainer, {\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        children: \"Loading consultation room...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this);\n  }\n  if (!consultation) {\n    return /*#__PURE__*/_jsxDEV(RoomContainer, {\n      children: /*#__PURE__*/_jsxDEV(RoomContent, {\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), \"Consultation not found or you don't have access to this room.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(RoomContainer, {\n    children: /*#__PURE__*/_jsxDEV(RoomContent, {\n      children: [/*#__PURE__*/_jsxDEV(VideoSection, {\n        children: isCallActive ? /*#__PURE__*/_jsxDEV(VideoCallComponent, {\n          consultation: consultation,\n          onCallEnd: handleCallEnd,\n          onError: handleCallError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '40px',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Video Consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Click \\\"Join Call\\\" to start the video consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), !isCallActive && consultation.status === 'SCHEDULED' && /*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"primary\",\n            onClick: startConsultation,\n            children: \"Join Call\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SidePanel, {\n        children: [error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), error, /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setError(null),\n            style: {\n              marginLeft: 'auto',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(X, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), \"Appointment Details\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AppointmentInfo, {\n            children: [/*#__PURE__*/_jsxDEV(InfoRow, {\n              children: [/*#__PURE__*/_jsxDEV(Clock, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this), formatDateTime(consultation.scheduledStartTime)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoRow, {\n              children: [/*#__PURE__*/_jsxDEV(FileText, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this), \"Type: \", consultation.type.replace('_', ' ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoRow, {\n              children: [\"Status: \", /*#__PURE__*/_jsxDEV(StatusBadge, {\n                status: consultation.status,\n                children: consultation.status.replace('_', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), \"Participants\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ParticipantCard, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              children: getInitials(consultation.doctor.fullName)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ParticipantInfo, {\n              children: [/*#__PURE__*/_jsxDEV(ParticipantName, {\n                children: consultation.doctor.fullName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ParticipantRole, {\n                children: [\"Doctor \\u2022 \", consultation.doctor.specialization]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stethoscope, {\n              size: 16,\n              color: \"#4299e1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ParticipantCard, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              children: getInitials(consultation.patient.fullName)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ParticipantInfo, {\n              children: [/*#__PURE__*/_jsxDEV(ParticipantName, {\n                children: consultation.patient.fullName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ParticipantRole, {\n                children: \"Patient\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(User, {\n              size: 16,\n              color: \"#48bb78\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), \"Actions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), !isCallActive && consultation.status === 'SCHEDULED' && /*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"primary\",\n            onClick: startConsultation,\n            children: \"Join Video Call\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this), isCallActive && ((_state$user2 = state.user) === null || _state$user2 === void 0 ? void 0 : _state$user2.role) === 'DOCTOR' && /*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"danger\",\n            onClick: endConsultation,\n            children: \"End Consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"secondary\",\n            onClick: () => navigate('/dashboard'),\n            children: \"Back to Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoConsultationRoom, \"SYMRna4i5f2YNQyedx4+eFgMuMM=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c17 = VideoConsultationRoom;\nexport default VideoConsultationRoom;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"RoomContainer\");\n$RefreshReg$(_c2, \"RoomContent\");\n$RefreshReg$(_c3, \"VideoSection\");\n$RefreshReg$(_c4, \"SidePanel\");\n$RefreshReg$(_c5, \"InfoCard\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"AppointmentInfo\");\n$RefreshReg$(_c8, \"InfoRow\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"ParticipantCard\");\n$RefreshReg$(_c1, \"Avatar\");\n$RefreshReg$(_c10, \"ParticipantInfo\");\n$RefreshReg$(_c11, \"ParticipantName\");\n$RefreshReg$(_c12, \"ParticipantRole\");\n$RefreshReg$(_c13, \"ActionButton\");\n$RefreshReg$(_c14, \"LoadingSpinner\");\n$RefreshReg$(_c15, \"ErrorMessage\");\n$RefreshReg$(_c16, \"SuccessMessage\");\n$RefreshReg$(_c17, \"VideoConsultationRoom\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "useParams", "useNavigate", "Calendar", "Clock", "User", "Stethoscope", "MessageCircle", "FileText", "AlertCircle", "CheckCircle", "X", "VideoCallComponent", "videoCallService", "useAuth", "jsxDEV", "_jsxDEV", "RoomContainer", "div", "_c", "Room<PERSON><PERSON>nt", "_c2", "VideoSection", "_c3", "SidePanel", "_c4", "InfoCard", "_c5", "CardTitle", "h3", "_c6", "AppointmentInfo", "_c7", "InfoRow", "_c8", "StatusBadge", "span", "props", "status", "_c9", "ParticipantCard", "_c0", "Avatar", "_c1", "ParticipantInfo", "_c10", "ParticipantName", "_c11", "ParticipantRole", "_c12", "ActionButton", "button", "variant", "_c13", "LoadingSpinner", "_c14", "ErrorMessage", "_c15", "SuccessMessage", "_c16", "VideoConsultationRoom", "_s", "_state$user2", "roomId", "navigate", "state", "consultation", "setConsultation", "loading", "setLoading", "error", "setError", "success", "setSuccess", "isCallActive", "setIsCallActive", "loadConsultation", "consultationData", "getConsultationByRoomId", "startConsultation", "err", "message", "updatedConsultation", "id", "setTimeout", "endConsultation", "handleCallEnd", "_state$user", "user", "role", "handleCallError", "errorMessage", "getInitials", "name", "split", "map", "n", "join", "toUpperCase", "formatDateTime", "dateTime", "Date", "toLocaleString", "getStatusColor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onCallEnd", "onError", "style", "padding", "textAlign", "onClick", "marginLeft", "background", "border", "cursor", "scheduledStartTime", "type", "replace", "doctor", "fullName", "specialization", "color", "patient", "_c17", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/src/components/video/VideoConsultationRoom.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { \n  Calendar, \n  Clock, \n  User, \n  Stethoscope, \n  MessageCircle, \n  FileText,\n  Star,\n  AlertCircle,\n  CheckCircle,\n  X\n} from 'lucide-react';\nimport VideoCallComponent from './VideoCallComponent';\nimport { videoCallService, VideoConsultation, ConsultationType } from '../../services/videoCallService';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst RoomContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n`;\n\nconst RoomContent = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  display: grid;\n  grid-template-columns: 1fr 350px;\n  gap: 20px;\n  height: calc(100vh - 40px);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    grid-template-rows: 1fr auto;\n  }\n`;\n\nconst VideoSection = styled.div`\n  background: white;\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n`;\n\nconst SidePanel = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst InfoCard = styled.div`\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n`;\n\nconst CardTitle = styled.h3`\n  margin: 0 0 16px 0;\n  color: #2d3748;\n  font-size: 18px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst AppointmentInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n`;\n\nconst InfoRow = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  color: #4a5568;\n  font-size: 14px;\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n  \n  background: ${props => {\n    switch (props.status) {\n      case 'IN_PROGRESS': return '#48bb78';\n      case 'WAITING_FOR_DOCTOR': return '#ed8936';\n      case 'WAITING_FOR_PATIENT': return '#4299e1';\n      case 'SCHEDULED': return '#9f7aea';\n      default: return '#718096';\n    }\n  }};\n  color: white;\n`;\n\nconst ParticipantCard = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: #f7fafc;\n  border-radius: 8px;\n  margin-bottom: 8px;\n`;\n\nconst Avatar = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n`;\n\nconst ParticipantInfo = styled.div`\n  flex: 1;\n`;\n\nconst ParticipantName = styled.div`\n  font-weight: 500;\n  color: #2d3748;\n`;\n\nconst ParticipantRole = styled.div`\n  font-size: 12px;\n  color: #718096;\n  text-transform: uppercase;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`\n  width: 100%;\n  padding: 12px 16px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-bottom: 8px;\n  \n  background: ${props => {\n    switch (props.variant) {\n      case 'primary': return '#4299e1';\n      case 'danger': return '#e53e3e';\n      default: return '#edf2f7';\n    }\n  }};\n  \n  color: ${props => props.variant === 'secondary' ? '#4a5568' : 'white'};\n  \n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  color: #4a5568;\n`;\n\nconst ErrorMessage = styled.div`\n  background: #fed7d7;\n  color: #c53030;\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst SuccessMessage = styled.div`\n  background: #c6f6d5;\n  color: #2f855a;\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\ninterface VideoConsultationRoomProps {}\n\nconst VideoConsultationRoom: React.FC<VideoConsultationRoomProps> = () => {\n  const { roomId } = useParams<{ roomId: string }>();\n  const navigate = useNavigate();\n  const { state } = useAuth();\n  \n  const [consultation, setConsultation] = useState<VideoConsultation | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [isCallActive, setIsCallActive] = useState(false);\n\n  useEffect(() => {\n    if (roomId) {\n      loadConsultation();\n    }\n  }, [roomId]);\n\n  const loadConsultation = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const consultationData = await videoCallService.getConsultationByRoomId(roomId!);\n      setConsultation(consultationData);\n      \n      // Auto-start consultation if it's scheduled\n      if (consultationData.status === 'SCHEDULED') {\n        await startConsultation();\n      } else if (consultationData.status === 'IN_PROGRESS') {\n        setIsCallActive(true);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to load consultation');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startConsultation = async () => {\n    if (!consultation) return;\n    \n    try {\n      setError(null);\n      const updatedConsultation = await videoCallService.startConsultation(consultation.id);\n      setConsultation(updatedConsultation);\n      setIsCallActive(true);\n      setSuccess('Consultation started successfully!');\n      \n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to start consultation');\n    }\n  };\n\n  const endConsultation = async () => {\n    if (!consultation) return;\n    \n    try {\n      setError(null);\n      await videoCallService.endConsultation(consultation.id);\n      setIsCallActive(false);\n      setSuccess('Consultation ended successfully!');\n      \n      // Redirect after 2 seconds\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 2000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to end consultation');\n    }\n  };\n\n  const handleCallEnd = () => {\n    setIsCallActive(false);\n    if (state.user?.role === 'DOCTOR') {\n      // Only doctors can officially end consultations\n      endConsultation();\n    } else {\n      // Patients just leave the call\n      navigate('/dashboard');\n    }\n  };\n\n  const handleCallError = (errorMessage: string) => {\n    setError(errorMessage);\n    setIsCallActive(false);\n  };\n\n  const getInitials = (name: string): string => {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  };\n\n  const formatDateTime = (dateTime: string): string => {\n    return new Date(dateTime).toLocaleString();\n  };\n\n  const getStatusColor = (status: string): string => {\n    switch (status) {\n      case 'IN_PROGRESS': return '#48bb78';\n      case 'WAITING_FOR_DOCTOR': return '#ed8936';\n      case 'WAITING_FOR_PATIENT': return '#4299e1';\n      case 'SCHEDULED': return '#9f7aea';\n      default: return '#718096';\n    }\n  };\n\n  if (loading) {\n    return (\n      <RoomContainer>\n        <LoadingSpinner>\n          Loading consultation room...\n        </LoadingSpinner>\n      </RoomContainer>\n    );\n  }\n\n  if (!consultation) {\n    return (\n      <RoomContainer>\n        <RoomContent>\n          <ErrorMessage>\n            <AlertCircle size={20} />\n            Consultation not found or you don't have access to this room.\n          </ErrorMessage>\n        </RoomContent>\n      </RoomContainer>\n    );\n  }\n\n  return (\n    <RoomContainer>\n      <RoomContent>\n        <VideoSection>\n          {isCallActive ? (\n            <VideoCallComponent\n              consultation={consultation}\n              onCallEnd={handleCallEnd}\n              onError={handleCallError}\n            />\n          ) : (\n            <div style={{ padding: '40px', textAlign: 'center' }}>\n              <h2>Video Consultation</h2>\n              <p>Click \"Join Call\" to start the video consultation</p>\n              {!isCallActive && consultation.status === 'SCHEDULED' && (\n                <ActionButton variant=\"primary\" onClick={startConsultation}>\n                  Join Call\n                </ActionButton>\n              )}\n            </div>\n          )}\n        </VideoSection>\n\n        <SidePanel>\n          {error && (\n            <ErrorMessage>\n              <AlertCircle size={20} />\n              {error}\n              <button \n                onClick={() => setError(null)}\n                style={{ marginLeft: 'auto', background: 'none', border: 'none', cursor: 'pointer' }}\n              >\n                <X size={16} />\n              </button>\n            </ErrorMessage>\n          )}\n\n          {success && (\n            <SuccessMessage>\n              <CheckCircle size={20} />\n              {success}\n            </SuccessMessage>\n          )}\n\n          <InfoCard>\n            <CardTitle>\n              <Calendar size={20} />\n              Appointment Details\n            </CardTitle>\n            <AppointmentInfo>\n              <InfoRow>\n                <Clock size={16} />\n                {formatDateTime(consultation.scheduledStartTime)}\n              </InfoRow>\n              <InfoRow>\n                <FileText size={16} />\n                Type: {consultation.type.replace('_', ' ')}\n              </InfoRow>\n              <InfoRow>\n                Status: <StatusBadge status={consultation.status}>{consultation.status.replace('_', ' ')}</StatusBadge>\n              </InfoRow>\n            </AppointmentInfo>\n          </InfoCard>\n\n          <InfoCard>\n            <CardTitle>\n              <User size={20} />\n              Participants\n            </CardTitle>\n            \n            <ParticipantCard>\n              <Avatar>{getInitials(consultation.doctor.fullName)}</Avatar>\n              <ParticipantInfo>\n                <ParticipantName>{consultation.doctor.fullName}</ParticipantName>\n                <ParticipantRole>Doctor • {consultation.doctor.specialization}</ParticipantRole>\n              </ParticipantInfo>\n              <Stethoscope size={16} color=\"#4299e1\" />\n            </ParticipantCard>\n\n            <ParticipantCard>\n              <Avatar>{getInitials(consultation.patient.fullName)}</Avatar>\n              <ParticipantInfo>\n                <ParticipantName>{consultation.patient.fullName}</ParticipantName>\n                <ParticipantRole>Patient</ParticipantRole>\n              </ParticipantInfo>\n              <User size={16} color=\"#48bb78\" />\n            </ParticipantCard>\n          </InfoCard>\n\n          <InfoCard>\n            <CardTitle>\n              <MessageCircle size={20} />\n              Actions\n            </CardTitle>\n            \n            {!isCallActive && consultation.status === 'SCHEDULED' && (\n              <ActionButton variant=\"primary\" onClick={startConsultation}>\n                Join Video Call\n              </ActionButton>\n            )}\n            \n            {isCallActive && state.user?.role === 'DOCTOR' && (\n              <ActionButton variant=\"danger\" onClick={endConsultation}>\n                End Consultation\n              </ActionButton>\n            )}\n            \n            <ActionButton variant=\"secondary\" onClick={() => navigate('/dashboard')}>\n              Back to Dashboard\n            </ActionButton>\n          </InfoCard>\n        </SidePanel>\n      </RoomContent>\n    </RoomContainer>\n  );\n};\n\nexport default VideoConsultationRoom;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,aAAa,EACbC,QAAQ,EAERC,WAAW,EACXC,WAAW,EACXC,CAAC,QACI,cAAc;AACrB,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,gBAAgB,QAA6C,iCAAiC;AACvG,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,aAAa,GAAGjB,MAAM,CAACkB,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,aAAa;AAMnB,MAAMG,WAAW,GAAGpB,MAAM,CAACkB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAZID,WAAW;AAcjB,MAAME,YAAY,GAAGtB,MAAM,CAACkB,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GALID,YAAY;AAOlB,MAAME,SAAS,GAAGxB,MAAM,CAACkB,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,SAAS;AAMf,MAAME,QAAQ,GAAG1B,MAAM,CAACkB,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GALID,QAAQ;AAOd,MAAME,SAAS,GAAG5B,MAAM,CAAC6B,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARIF,SAAS;AAUf,MAAMG,eAAe,GAAG/B,MAAM,CAACkB,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,eAAe;AAMrB,MAAME,OAAO,GAAGjC,MAAM,CAACkB,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,OAAO;AAQb,MAAME,WAAW,GAAGnC,MAAM,CAACoC,IAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAI;EACrB,QAAQA,KAAK,CAACC,MAAM;IAClB,KAAK,aAAa;MAAE,OAAO,SAAS;IACpC,KAAK,oBAAoB;MAAE,OAAO,SAAS;IAC3C,KAAK,qBAAqB;MAAE,OAAO,SAAS;IAC5C,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AACH;AACA,CAAC;AAACC,GAAA,GAjBIJ,WAAW;AAmBjB,MAAMK,eAAe,GAAGxC,MAAM,CAACkB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GARID,eAAe;AAUrB,MAAME,MAAM,GAAG1C,MAAM,CAACkB,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GAVID,MAAM;AAYZ,MAAME,eAAe,GAAG5C,MAAM,CAACkB,GAAG;AAClC;AACA,CAAC;AAAC2B,IAAA,GAFID,eAAe;AAIrB,MAAME,eAAe,GAAG9C,MAAM,CAACkB,GAAG;AAClC;AACA;AACA,CAAC;AAAC6B,IAAA,GAHID,eAAe;AAKrB,MAAME,eAAe,GAAGhD,MAAM,CAACkB,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GAJID,eAAe;AAMrB,MAAME,YAAY,GAAGlD,MAAM,CAACmD,MAAwD;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBd,KAAK,IAAI;EACrB,QAAQA,KAAK,CAACe,OAAO;IACnB,KAAK,SAAS;MAAE,OAAO,SAAS;IAChC,KAAK,QAAQ;MAAE,OAAO,SAAS;IAC/B;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AACH;AACA,WAAWf,KAAK,IAAIA,KAAK,CAACe,OAAO,KAAK,WAAW,GAAG,SAAS,GAAG,OAAO;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA9BIH,YAAY;AAgClB,MAAMI,cAAc,GAAGtD,MAAM,CAACkB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GANID,cAAc;AAQpB,MAAME,YAAY,GAAGxD,MAAM,CAACkB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GATID,YAAY;AAWlB,MAAME,cAAc,GAAG1D,MAAM,CAACkB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GATID,cAAc;AAapB,MAAME,qBAA2D,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA;EACxE,MAAM;IAAEC;EAAO,CAAC,GAAG9D,SAAS,CAAqB,CAAC;EAClD,MAAM+D,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+D;EAAM,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAE3B,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAA2B,IAAI,CAAC;EAChF,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0E,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAIgE,MAAM,EAAE;MACVa,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACb,MAAM,CAAC,CAAC;EAEZ,MAAMa,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMM,gBAAgB,GAAG,MAAMhE,gBAAgB,CAACiE,uBAAuB,CAACf,MAAO,CAAC;MAChFI,eAAe,CAACU,gBAAgB,CAAC;;MAEjC;MACA,IAAIA,gBAAgB,CAACvC,MAAM,KAAK,WAAW,EAAE;QAC3C,MAAMyC,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM,IAAIF,gBAAgB,CAACvC,MAAM,KAAK,aAAa,EAAE;QACpDqC,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOK,GAAQ,EAAE;MACjBT,QAAQ,CAACS,GAAG,CAACC,OAAO,IAAI,6BAA6B,CAAC;IACxD,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACb,YAAY,EAAE;IAEnB,IAAI;MACFK,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMW,mBAAmB,GAAG,MAAMrE,gBAAgB,CAACkE,iBAAiB,CAACb,YAAY,CAACiB,EAAE,CAAC;MACrFhB,eAAe,CAACe,mBAAmB,CAAC;MACpCP,eAAe,CAAC,IAAI,CAAC;MACrBF,UAAU,CAAC,oCAAoC,CAAC;;MAEhD;MACAW,UAAU,CAAC,MAAMX,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOO,GAAQ,EAAE;MACjBT,QAAQ,CAACS,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;IACzD;EACF,CAAC;EAED,MAAMI,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACnB,YAAY,EAAE;IAEnB,IAAI;MACFK,QAAQ,CAAC,IAAI,CAAC;MACd,MAAM1D,gBAAgB,CAACwE,eAAe,CAACnB,YAAY,CAACiB,EAAE,CAAC;MACvDR,eAAe,CAAC,KAAK,CAAC;MACtBF,UAAU,CAAC,kCAAkC,CAAC;;MAE9C;MACAW,UAAU,CAAC,MAAM;QACfpB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOgB,GAAQ,EAAE;MACjBT,QAAQ,CAACS,GAAG,CAACC,OAAO,IAAI,4BAA4B,CAAC;IACvD;EACF,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA;IAC1BZ,eAAe,CAAC,KAAK,CAAC;IACtB,IAAI,EAAAY,WAAA,GAAAtB,KAAK,CAACuB,IAAI,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,IAAI,MAAK,QAAQ,EAAE;MACjC;MACAJ,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM;MACL;MACArB,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAM0B,eAAe,GAAIC,YAAoB,IAAK;IAChDpB,QAAQ,CAACoB,YAAY,CAAC;IACtBhB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMiB,WAAW,GAAIC,IAAY,IAAa;IAC5C,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAC9D,CAAC;EAED,MAAMC,cAAc,GAAIC,QAAgB,IAAa;IACnD,OAAO,IAAIC,IAAI,CAACD,QAAQ,CAAC,CAACE,cAAc,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,cAAc,GAAIjE,MAAc,IAAa;IACjD,QAAQA,MAAM;MACZ,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC,KAAK,oBAAoB;QAAE,OAAO,SAAS;MAC3C,KAAK,qBAAqB;QAAE,OAAO,SAAS;MAC5C,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAI8B,OAAO,EAAE;IACX,oBACEpD,OAAA,CAACC,aAAa;MAAAuF,QAAA,eACZxF,OAAA,CAACsC,cAAc;QAAAkD,QAAA,EAAC;MAEhB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEpB;EAEA,IAAI,CAAC1C,YAAY,EAAE;IACjB,oBACElD,OAAA,CAACC,aAAa;MAAAuF,QAAA,eACZxF,OAAA,CAACI,WAAW;QAAAoF,QAAA,eACVxF,OAAA,CAACwC,YAAY;UAAAgD,QAAA,gBACXxF,OAAA,CAACP,WAAW;YAACoG,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iEAE3B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEpB;EAEA,oBACE5F,OAAA,CAACC,aAAa;IAAAuF,QAAA,eACZxF,OAAA,CAACI,WAAW;MAAAoF,QAAA,gBACVxF,OAAA,CAACM,YAAY;QAAAkF,QAAA,EACV9B,YAAY,gBACX1D,OAAA,CAACJ,kBAAkB;UACjBsD,YAAY,EAAEA,YAAa;UAC3B4C,SAAS,EAAExB,aAAc;UACzByB,OAAO,EAAErB;QAAgB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,gBAEF5F,OAAA;UAAKgG,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAV,QAAA,gBACnDxF,OAAA;YAAAwF,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B5F,OAAA;YAAAwF,QAAA,EAAG;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EACvD,CAAClC,YAAY,IAAIR,YAAY,CAAC5B,MAAM,KAAK,WAAW,iBACnDtB,OAAA,CAACkC,YAAY;YAACE,OAAO,EAAC,SAAS;YAAC+D,OAAO,EAAEpC,iBAAkB;YAAAyB,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CACf;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEf5F,OAAA,CAACQ,SAAS;QAAAgF,QAAA,GACPlC,KAAK,iBACJtD,OAAA,CAACwC,YAAY;UAAAgD,QAAA,gBACXxF,OAAA,CAACP,WAAW;YAACoG,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxBtC,KAAK,eACNtD,OAAA;YACEmG,OAAO,EAAEA,CAAA,KAAM5C,QAAQ,CAAC,IAAI,CAAE;YAC9ByC,KAAK,EAAE;cAAEI,UAAU,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,MAAM,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAf,QAAA,eAErFxF,OAAA,CAACL,CAAC;cAACkG,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACf,EAEApC,OAAO,iBACNxD,OAAA,CAAC0C,cAAc;UAAA8C,QAAA,gBACbxF,OAAA,CAACN,WAAW;YAACmG,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxBpC,OAAO;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB,eAED5F,OAAA,CAACU,QAAQ;UAAA8E,QAAA,gBACPxF,OAAA,CAACY,SAAS;YAAA4E,QAAA,gBACRxF,OAAA,CAACb,QAAQ;cAAC0G,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uBAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZ5F,OAAA,CAACe,eAAe;YAAAyE,QAAA,gBACdxF,OAAA,CAACiB,OAAO;cAAAuE,QAAA,gBACNxF,OAAA,CAACZ,KAAK;gBAACyG,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAClBT,cAAc,CAACjC,YAAY,CAACsD,kBAAkB,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACV5F,OAAA,CAACiB,OAAO;cAAAuE,QAAA,gBACNxF,OAAA,CAACR,QAAQ;gBAACqG,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAChB,EAAC1C,YAAY,CAACuD,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACV5F,OAAA,CAACiB,OAAO;cAAAuE,QAAA,GAAC,UACC,eAAAxF,OAAA,CAACmB,WAAW;gBAACG,MAAM,EAAE4B,YAAY,CAAC5B,MAAO;gBAAAkE,QAAA,EAAEtC,YAAY,CAAC5B,MAAM,CAACoF,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEX5F,OAAA,CAACU,QAAQ;UAAA8E,QAAA,gBACPxF,OAAA,CAACY,SAAS;YAAA4E,QAAA,gBACRxF,OAAA,CAACX,IAAI;cAACwG,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAEZ5F,OAAA,CAACwB,eAAe;YAAAgE,QAAA,gBACdxF,OAAA,CAAC0B,MAAM;cAAA8D,QAAA,EAAEZ,WAAW,CAAC1B,YAAY,CAACyD,MAAM,CAACC,QAAQ;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC5D5F,OAAA,CAAC4B,eAAe;cAAA4D,QAAA,gBACdxF,OAAA,CAAC8B,eAAe;gBAAA0D,QAAA,EAAEtC,YAAY,CAACyD,MAAM,CAACC;cAAQ;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACjE5F,OAAA,CAACgC,eAAe;gBAAAwD,QAAA,GAAC,gBAAS,EAACtC,YAAY,CAACyD,MAAM,CAACE,cAAc;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eAClB5F,OAAA,CAACV,WAAW;cAACuG,IAAI,EAAE,EAAG;cAACiB,KAAK,EAAC;YAAS;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAElB5F,OAAA,CAACwB,eAAe;YAAAgE,QAAA,gBACdxF,OAAA,CAAC0B,MAAM;cAAA8D,QAAA,EAAEZ,WAAW,CAAC1B,YAAY,CAAC6D,OAAO,CAACH,QAAQ;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC7D5F,OAAA,CAAC4B,eAAe;cAAA4D,QAAA,gBACdxF,OAAA,CAAC8B,eAAe;gBAAA0D,QAAA,EAAEtC,YAAY,CAAC6D,OAAO,CAACH;cAAQ;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eAClE5F,OAAA,CAACgC,eAAe;gBAAAwD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eAClB5F,OAAA,CAACX,IAAI;cAACwG,IAAI,EAAE,EAAG;cAACiB,KAAK,EAAC;YAAS;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEX5F,OAAA,CAACU,QAAQ;UAAA8E,QAAA,gBACPxF,OAAA,CAACY,SAAS;YAAA4E,QAAA,gBACRxF,OAAA,CAACT,aAAa;cAACsG,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,EAEX,CAAClC,YAAY,IAAIR,YAAY,CAAC5B,MAAM,KAAK,WAAW,iBACnDtB,OAAA,CAACkC,YAAY;YAACE,OAAO,EAAC,SAAS;YAAC+D,OAAO,EAAEpC,iBAAkB;YAAAyB,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CACf,EAEAlC,YAAY,IAAI,EAAAZ,YAAA,GAAAG,KAAK,CAACuB,IAAI,cAAA1B,YAAA,uBAAVA,YAAA,CAAY2B,IAAI,MAAK,QAAQ,iBAC5CzE,OAAA,CAACkC,YAAY;YAACE,OAAO,EAAC,QAAQ;YAAC+D,OAAO,EAAE9B,eAAgB;YAAAmB,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CACf,eAED5F,OAAA,CAACkC,YAAY;YAACE,OAAO,EAAC,WAAW;YAAC+D,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,YAAY,CAAE;YAAAwC,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB,CAAC;AAAC/C,EAAA,CArPID,qBAA2D;EAAA,QAC5C3D,SAAS,EACXC,WAAW,EACVY,OAAO;AAAA;AAAAkH,IAAA,GAHrBpE,qBAA2D;AAuPjE,eAAeA,qBAAqB;AAAC,IAAAzC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAqE,IAAA;AAAAC,YAAA,CAAA9G,EAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}