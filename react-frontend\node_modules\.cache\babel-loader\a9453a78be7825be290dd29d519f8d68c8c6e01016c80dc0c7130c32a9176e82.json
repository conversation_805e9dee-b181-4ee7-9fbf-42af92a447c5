{"ast": null, "code": "/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport const BYTE = {\n  // LINEFEED byte (octet 10)\n  LF: '\\x0A',\n  // NULL byte (octet 0)\n  NULL: '\\x00'\n};", "map": {"version": 3, "names": ["BYTE", "LF", "NULL"], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\byte.ts"], "sourcesContent": ["/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport const BYTE = {\n  // LINEFEED byte (octet 10)\n  LF: '\\x0A',\n  // NULL byte (octet 0)\n  NULL: '\\x00',\n};\n"], "mappings": "AAAA;;;;;;;AAOA,OAAO,MAAMA,IAAI,GAAG;EAClB;EACAC,EAAE,EAAE,MAAM;EACV;EACAC,IAAI,EAAE;CACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}