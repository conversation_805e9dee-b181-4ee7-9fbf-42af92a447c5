{"ast": null, "code": "import { apiService } from './apiService';\nimport { Client } from '@stomp/stompjs';\nimport SockJS from 'sockjs-client';\nclass VideoCallService {\n  constructor() {\n    this.stompClient = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    this.currentRoomId = null;\n    this.currentPeerId = null;\n    this.isConnected = false;\n    this.mediaRecorder = null;\n    this.recordedChunks = [];\n    // Event callbacks\n    this.onRemoteStreamCallback = void 0;\n    this.onParticipantJoinedCallback = void 0;\n    this.onParticipantLeftCallback = void 0;\n    this.onConnectionStateChangeCallback = void 0;\n    this.onCallEndedCallback = void 0;\n    // ICE servers configuration\n    this.iceServers = [{\n      urls: 'stun:stun.l.google.com:19302'\n    }, {\n      urls: 'stun:stun1.l.google.com:19302'\n    }];\n  }\n  // API Methods\n  async createConsultation(appointmentId, type) {\n    return apiService.post('/video-consultation/create', {\n      appointmentId,\n      type\n    });\n  }\n  async startConsultation(consultationId) {\n    return apiService.post(`/video-consultation/${consultationId}/start`);\n  }\n  async endConsultation(consultationId, notes, diagnosis, recommendations) {\n    return apiService.post(`/video-consultation/${consultationId}/end`, {\n      notes,\n      diagnosis,\n      recommendations\n    });\n  }\n  async getConsultation(consultationId) {\n    return apiService.get(`/video-consultation/${consultationId}`);\n  }\n  async getConsultationByRoomId(roomId) {\n    return apiService.get(`/video-consultation/room/${roomId}`);\n  }\n  async getUserConsultations() {\n    return apiService.get('/video-consultation/user/consultations');\n  }\n  async getUpcomingConsultations() {\n    return apiService.get('/video-consultation/user/upcoming');\n  }\n  async updateConsultationSettings(consultationId, settings) {\n    return apiService.put(`/video-consultation/${consultationId}/settings`, settings);\n  }\n  async submitFeedback(consultationId, feedback) {\n    return apiService.post(`/video-consultation/${consultationId}/feedback`, feedback);\n  }\n\n  // WebSocket Connection\n  connectWebSocket() {\n    return new Promise((resolve, reject) => {\n      try {\n        const token = localStorage.getItem('authToken');\n        if (!token) {\n          reject(new Error('No authentication token found'));\n          return;\n        }\n        const socket = new SockJS('http://localhost:8080/ws');\n        this.stompClient = new Client({\n          webSocketFactory: () => socket,\n          connectHeaders: {\n            Authorization: `Bearer ${token}`\n          },\n          debug: str => console.log('STOMP Debug:', str),\n          reconnectDelay: 5000,\n          heartbeatIncoming: 4000,\n          heartbeatOutgoing: 4000\n        });\n        this.stompClient.onConnect = () => {\n          console.log('WebSocket connected for video calls');\n          this.isConnected = true;\n          resolve();\n        };\n        this.stompClient.onStompError = frame => {\n          console.error('WebSocket STOMP error:', frame);\n          reject(new Error('WebSocket connection failed'));\n        };\n        this.stompClient.onWebSocketError = error => {\n          console.error('WebSocket error:', error);\n          reject(error);\n        };\n        this.stompClient.activate();\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n  disconnectWebSocket() {\n    if (this.stompClient) {\n      this.stompClient.deactivate();\n      this.isConnected = false;\n    }\n  }\n\n  // Media Access\n  async getUserMedia(constraints = {\n    video: true,\n    audio: true\n  }) {\n    try {\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);\n      return this.localStream;\n    } catch (error) {\n      console.error('Error accessing user media:', error);\n      throw error;\n    }\n  }\n  async getDisplayMedia() {\n    try {\n      return await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n    } catch (error) {\n      console.error('Error accessing display media:', error);\n      throw error;\n    }\n  }\n\n  // Event Handlers\n  setOnRemoteStream(callback) {\n    this.onRemoteStreamCallback = callback;\n  }\n  setOnParticipantJoined(callback) {\n    this.onParticipantJoinedCallback = callback;\n  }\n  setOnParticipantLeft(callback) {\n    this.onParticipantLeftCallback = callback;\n  }\n  setOnConnectionStateChange(callback) {\n    this.onConnectionStateChangeCallback = callback;\n  }\n  setOnCallEnded(callback) {\n    this.onCallEndedCallback = callback;\n  }\n\n  // Getters\n  getLocalStream() {\n    return this.localStream;\n  }\n  getRemoteStream() {\n    return this.remoteStream;\n  }\n  getCurrentRoomId() {\n    return this.currentRoomId;\n  }\n  isWebSocketConnected() {\n    return this.isConnected;\n  }\n\n  // WebRTC Room Management\n  async joinRoom(roomId, userRole) {\n    if (!this.isConnected || !this.stompClient) {\n      throw new Error('WebSocket not connected');\n    }\n    this.currentRoomId = roomId;\n    this.currentPeerId = this.generatePeerId();\n\n    // Subscribe to room messages\n    this.stompClient.subscribe(`/topic/webrtc/${roomId}/${this.getCurrentUserId()}`, message => {\n      const webrtcMessage = JSON.parse(message.body);\n      this.handleWebRTCMessage(webrtcMessage);\n    });\n\n    // Send join message\n    this.stompClient.publish({\n      destination: `/app/webrtc/${roomId}/join`,\n      body: JSON.stringify({\n        userRole\n      }),\n      headers: {\n        Authorization: `Bearer ${localStorage.getItem('authToken')}`\n      }\n    });\n\n    // Initialize peer connection\n    this.initializePeerConnection();\n  }\n  async leaveRoom() {\n    if (!this.currentRoomId || !this.stompClient) return;\n    this.stompClient.publish({\n      destination: `/app/webrtc/${this.currentRoomId}/leave`,\n      headers: {\n        Authorization: `Bearer ${localStorage.getItem('authToken')}`\n      }\n    });\n    this.cleanup();\n  }\n\n  // WebRTC Peer Connection\n  initializePeerConnection() {\n    this.peerConnection = new RTCPeerConnection({\n      iceServers: this.iceServers\n    });\n\n    // Add local stream tracks\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, this.localStream);\n      });\n    }\n\n    // Handle remote stream\n    this.peerConnection.ontrack = event => {\n      console.log('Received remote stream');\n      this.remoteStream = event.streams[0];\n      if (this.onRemoteStreamCallback) {\n        this.onRemoteStreamCallback(this.remoteStream);\n      }\n    };\n\n    // Handle ICE candidates\n    this.peerConnection.onicecandidate = event => {\n      if (event.candidate && this.currentRoomId) {\n        this.sendSignal({\n          type: 'ICE_CANDIDATE',\n          data: event.candidate\n        });\n      }\n    };\n\n    // Handle connection state changes\n    this.peerConnection.onconnectionstatechange = () => {\n      const state = this.peerConnection.connectionState;\n      console.log('Connection state changed:', state);\n      if (this.onConnectionStateChangeCallback) {\n        this.onConnectionStateChangeCallback(state);\n      }\n    };\n  }\n  async handleWebRTCMessage(message) {\n    switch (message.type) {\n      case 'OFFER':\n        await this.handleOffer(message);\n        break;\n      case 'ANSWER':\n        await this.handleAnswer(message);\n        break;\n      case 'ICE_CANDIDATE':\n        await this.handleIceCandidate(message);\n        break;\n      case 'USER_JOINED':\n        this.handleUserJoined(message);\n        break;\n      case 'USER_LEFT':\n        this.handleUserLeft(message);\n        break;\n      case 'EXISTING_PEER':\n        await this.handleExistingPeer(message);\n        break;\n      case 'SCREEN_SHARE_START':\n        this.handleScreenShareStart(message);\n        break;\n      case 'SCREEN_SHARE_STOP':\n        this.handleScreenShareStop(message);\n        break;\n      case 'SESSION_END':\n        this.handleSessionEnd();\n        break;\n    }\n  }\n  async handleOffer(message) {\n    if (!this.peerConnection) return;\n    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(message.data));\n    const answer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(answer);\n    this.sendSignal({\n      type: 'ANSWER',\n      targetPeerId: message.fromPeerId,\n      data: answer\n    });\n  }\n  async handleAnswer(message) {\n    if (!this.peerConnection) return;\n    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(message.data));\n  }\n  async handleIceCandidate(message) {\n    if (!this.peerConnection) return;\n    await this.peerConnection.addIceCandidate(new RTCIceCandidate(message.data));\n  }\n  handleUserJoined(message) {\n    console.log('User joined:', message.fromPeerId);\n    if (this.onParticipantJoinedCallback && message.data) {\n      // Create offer for new user\n      this.createOffer(message.fromPeerId);\n    }\n  }\n  handleUserLeft(message) {\n    console.log('User left:', message.fromPeerId);\n    if (this.onParticipantLeftCallback) {\n      this.onParticipantLeftCallback(message.fromPeerId);\n    }\n  }\n  async handleExistingPeer(message) {\n    console.log('Existing peer:', message.fromPeerId);\n    // Wait for the existing peer to create an offer\n  }\n  handleScreenShareStart(message) {\n    console.log('Screen share started by:', message.fromPeerId);\n  }\n  handleScreenShareStop(message) {\n    console.log('Screen share stopped by:', message.fromPeerId);\n  }\n  handleSessionEnd() {\n    console.log('Session ended');\n    if (this.onCallEndedCallback) {\n      this.onCallEndedCallback();\n    }\n    this.cleanup();\n  }\n  async createOffer(targetPeerId) {\n    if (!this.peerConnection) return;\n    const offer = await this.peerConnection.createOffer();\n    await this.peerConnection.setLocalDescription(offer);\n    this.sendSignal({\n      type: 'OFFER',\n      targetPeerId,\n      data: offer\n    });\n  }\n  sendSignal(signal) {\n    if (!this.currentRoomId || !this.stompClient) return;\n    this.stompClient.publish({\n      destination: `/app/webrtc/${this.currentRoomId}/signal`,\n      body: JSON.stringify(signal),\n      headers: {\n        Authorization: `Bearer ${localStorage.getItem('authToken')}`\n      }\n    });\n  }\n\n  // Media Controls\n  toggleAudio() {\n    if (!this.localStream) return false;\n    const audioTrack = this.localStream.getAudioTracks()[0];\n    if (audioTrack) {\n      audioTrack.enabled = !audioTrack.enabled;\n      return !audioTrack.enabled; // Return muted state\n    }\n    return false;\n  }\n  toggleVideo() {\n    if (!this.localStream) return false;\n    const videoTrack = this.localStream.getVideoTracks()[0];\n    if (videoTrack) {\n      videoTrack.enabled = !videoTrack.enabled;\n      return !videoTrack.enabled; // Return muted state\n    }\n    return false;\n  }\n  async startScreenShare() {\n    try {\n      const screenStream = await this.getDisplayMedia();\n\n      // Replace video track with screen share\n      if (this.peerConnection && this.localStream) {\n        const videoTrack = this.localStream.getVideoTracks()[0];\n        const screenTrack = screenStream.getVideoTracks()[0];\n        const sender = this.peerConnection.getSenders().find(s => s.track && s.track.kind === 'video');\n        if (sender) {\n          await sender.replaceTrack(screenTrack);\n        }\n\n        // Update local stream\n        this.localStream.removeTrack(videoTrack);\n        this.localStream.addTrack(screenTrack);\n\n        // Notify other participants\n        this.sendSignal({\n          type: 'SCREEN_SHARE_START'\n        });\n\n        // Handle screen share end\n        screenTrack.onended = () => {\n          this.stopScreenShare();\n        };\n      }\n      return screenStream;\n    } catch (error) {\n      console.error('Error starting screen share:', error);\n      throw error;\n    }\n  }\n  async stopScreenShare() {\n    try {\n      // Get camera stream again\n      const cameraStream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: false\n      });\n      const cameraTrack = cameraStream.getVideoTracks()[0];\n      if (this.peerConnection && this.localStream) {\n        const screenTrack = this.localStream.getVideoTracks()[0];\n        const sender = this.peerConnection.getSenders().find(s => s.track && s.track.kind === 'video');\n        if (sender) {\n          await sender.replaceTrack(cameraTrack);\n        }\n\n        // Update local stream\n        this.localStream.removeTrack(screenTrack);\n        this.localStream.addTrack(cameraTrack);\n\n        // Stop screen track\n        screenTrack.stop();\n\n        // Notify other participants\n        this.sendSignal({\n          type: 'SCREEN_SHARE_STOP'\n        });\n      }\n    } catch (error) {\n      console.error('Error stopping screen share:', error);\n      throw error;\n    }\n  }\n\n  // Recording\n  startRecording() {\n    if (!this.localStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new MediaRecorder(this.localStream);\n    this.mediaRecorder.ondataavailable = event => {\n      if (event.data.size > 0) {\n        this.recordedChunks.push(event.data);\n      }\n    };\n    this.mediaRecorder.start();\n  }\n  stopRecording() {\n    if (!this.mediaRecorder) return null;\n    this.mediaRecorder.stop();\n    if (this.recordedChunks.length > 0) {\n      return new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n    }\n    return null;\n  }\n\n  // Utility methods\n  generatePeerId() {\n    return 'peer_' + Math.random().toString(36).substr(2, 9);\n  }\n  getCurrentUserId() {\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    return user.id || 0;\n  }\n\n  // Cleanup\n  cleanup() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n      this.mediaRecorder.stop();\n    }\n    this.currentRoomId = null;\n    this.currentPeerId = null;\n    this.remoteStream = null;\n    this.recordedChunks = [];\n  }\n}\nexport const videoCallService = new VideoCallService();", "map": {"version": 3, "names": ["apiService", "Client", "SockJS", "VideoCallService", "constructor", "stompClient", "localStream", "remoteStream", "peerConnection", "currentRoomId", "currentPeerId", "isConnected", "mediaRecorder", "recordedChunks", "onRemoteStreamCallback", "onParticipantJoinedCallback", "onParticipantLeftCallback", "onConnectionStateChangeCallback", "onCallEndedCallback", "iceServers", "urls", "createConsultation", "appointmentId", "type", "post", "startConsultation", "consultationId", "endConsultation", "notes", "diagnosis", "recommendations", "getConsultation", "get", "getConsultationByRoomId", "roomId", "getUserConsultations", "getUpcomingConsultations", "updateConsultationSettings", "settings", "put", "submitFeedback", "feedback", "connectWebSocket", "Promise", "resolve", "reject", "token", "localStorage", "getItem", "Error", "socket", "webSocketFactory", "connectHeaders", "Authorization", "debug", "str", "console", "log", "reconnectDelay", "heartbeatIncoming", "heartbeatOutgoing", "onConnect", "onStompError", "frame", "error", "onWebSocketError", "activate", "disconnectWebSocket", "deactivate", "getUserMedia", "constraints", "video", "audio", "navigator", "mediaDevices", "getDisplayMedia", "setOnRemoteStream", "callback", "setOnParticipantJoined", "setOnParticipantLeft", "setOnConnectionStateChange", "setOnCallEnded", "getLocalStream", "getRemoteStream", "getCurrentRoomId", "isWebSocketConnected", "joinRoom", "userRole", "generatePeerId", "subscribe", "getCurrentUserId", "message", "webrtcMessage", "JSON", "parse", "body", "handleWebRTCMessage", "publish", "destination", "stringify", "headers", "initializePeerConnection", "leaveRoom", "cleanup", "RTCPeerConnection", "getTracks", "for<PERSON>ach", "track", "addTrack", "ontrack", "event", "streams", "onicecandidate", "candidate", "sendSignal", "data", "onconnectionstatechange", "state", "connectionState", "handleOffer", "handleAnswer", "handleIceCandidate", "handleUserJoined", "handleUserLeft", "handleExistingPeer", "handleScreenShareStart", "handleScreenShareStop", "handleSessionEnd", "setRemoteDescription", "RTCSessionDescription", "answer", "createAnswer", "setLocalDescription", "targetPeerId", "fromPeerId", "addIceCandidate", "RTCIceCandidate", "createOffer", "offer", "signal", "toggleAudio", "audioTrack", "getAudioTracks", "enabled", "toggleVideo", "videoTrack", "getVideoTracks", "startScreenShare", "screenStream", "screenTrack", "sender", "getSenders", "find", "s", "kind", "replaceTrack", "removeTrack", "onended", "stopScreenShare", "cameraStream", "cameraTrack", "stop", "startRecording", "MediaRecorder", "ondataavailable", "size", "push", "start", "stopRecording", "length", "Blob", "Math", "random", "toString", "substr", "user", "id", "close", "videoCallService"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/src/services/videoCallService.ts"], "sourcesContent": ["import { apiService } from './apiService';\nimport { Client } from '@stomp/stompjs';\nimport SockJS from 'sockjs-client';\n\nexport interface VideoConsultation {\n  id: number;\n  roomId: string;\n  sessionId: string;\n  status: ConsultationStatus;\n  type: ConsultationType;\n  scheduledStartTime: string;\n  actualStartTime?: string;\n  endTime?: string;\n  durationMinutes?: number;\n  doctorJoinTime?: string;\n  patientJoinTime?: string;\n  recordingEnabled: boolean;\n  recordingUrl?: string;\n  screenSharingEnabled: boolean;\n  chatEnabled: boolean;\n  appointment: {\n    id: number;\n    date: string;\n    startTime: string;\n    endTime: string;\n  };\n  doctor: {\n    id: number;\n    fullName: string;\n    specialization?: string;\n  };\n  patient: {\n    id: number;\n    fullName: string;\n  };\n}\n\nexport type ConsultationStatus = \n  | 'SCHEDULED' \n  | 'WAITING_FOR_DOCTOR' \n  | 'WAITING_FOR_PATIENT' \n  | 'IN_PROGRESS' \n  | 'COMPLETED' \n  | 'CANCELLED' \n  | 'NO_SHOW' \n  | 'TECHNICAL_ISSUES';\n\nexport type ConsultationType = \n  | 'ROUTINE_CHECKUP' \n  | 'FOLLOW_UP' \n  | 'URGENT_CARE' \n  | 'SPECIALIST_CONSULTATION' \n  | 'MENTAL_HEALTH' \n  | 'PRESCRIPTION_REVIEW' \n  | 'SECOND_OPINION' \n  | 'EMERGENCY_CONSULTATION';\n\nexport interface WebRTCSignal {\n  type: 'OFFER' | 'ANSWER' | 'ICE_CANDIDATE' | 'SCREEN_SHARE_START' | 'SCREEN_SHARE_STOP';\n  targetPeerId?: string;\n  data?: any;\n}\n\nexport interface WebRTCMessage {\n  type: 'OFFER' | 'ANSWER' | 'ICE_CANDIDATE' | 'USER_JOINED' | 'USER_LEFT' | 'EXISTING_PEER' | 'SCREEN_SHARE_START' | 'SCREEN_SHARE_STOP' | 'SESSION_END';\n  fromPeerId?: string;\n  toPeerId?: string;\n  data?: any;\n}\n\nexport interface CallParticipant {\n  userId: number;\n  peerId: string;\n  userRole: 'DOCTOR' | 'PATIENT';\n  fullName: string;\n  isAudioMuted: boolean;\n  isVideoMuted: boolean;\n  isScreenSharing: boolean;\n}\n\nclass VideoCallService {\n  private stompClient: Client | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n  private currentRoomId: string | null = null;\n  private currentPeerId: string | null = null;\n  private isConnected = false;\n  private mediaRecorder: MediaRecorder | null = null;\n  private recordedChunks: Blob[] = [];\n\n  // Event callbacks\n  private onRemoteStreamCallback?: (stream: MediaStream) => void;\n  private onParticipantJoinedCallback?: (participant: CallParticipant) => void;\n  private onParticipantLeftCallback?: (peerId: string) => void;\n  private onConnectionStateChangeCallback?: (state: RTCPeerConnectionState) => void;\n  private onCallEndedCallback?: () => void;\n\n  // ICE servers configuration\n  private iceServers = [\n    { urls: 'stun:stun.l.google.com:19302' },\n    { urls: 'stun:stun1.l.google.com:19302' }\n  ];\n\n  // API Methods\n  async createConsultation(appointmentId: number, type: ConsultationType): Promise<VideoConsultation> {\n    return apiService.post<VideoConsultation>('/video-consultation/create', {\n      appointmentId,\n      type\n    });\n  }\n\n  async startConsultation(consultationId: number): Promise<VideoConsultation> {\n    return apiService.post<VideoConsultation>(`/video-consultation/${consultationId}/start`);\n  }\n\n  async endConsultation(consultationId: number, notes?: string, diagnosis?: string, recommendations?: string): Promise<VideoConsultation> {\n    return apiService.post<VideoConsultation>(`/video-consultation/${consultationId}/end`, {\n      notes,\n      diagnosis,\n      recommendations\n    });\n  }\n\n  async getConsultation(consultationId: number): Promise<VideoConsultation> {\n    return apiService.get<VideoConsultation>(`/video-consultation/${consultationId}`);\n  }\n\n  async getConsultationByRoomId(roomId: string): Promise<VideoConsultation> {\n    return apiService.get<VideoConsultation>(`/video-consultation/room/${roomId}`);\n  }\n\n  async getUserConsultations(): Promise<VideoConsultation[]> {\n    return apiService.get<VideoConsultation[]>('/video-consultation/user/consultations');\n  }\n\n  async getUpcomingConsultations(): Promise<VideoConsultation[]> {\n    return apiService.get<VideoConsultation[]>('/video-consultation/user/upcoming');\n  }\n\n  async updateConsultationSettings(consultationId: number, settings: {\n    recordingEnabled: boolean;\n    screenSharingEnabled: boolean;\n    chatEnabled: boolean;\n  }): Promise<VideoConsultation> {\n    return apiService.put<VideoConsultation>(`/video-consultation/${consultationId}/settings`, settings);\n  }\n\n  async submitFeedback(consultationId: number, feedback: {\n    qualityRating: number;\n    satisfaction: number;\n    feedback: string;\n  }): Promise<VideoConsultation> {\n    return apiService.post<VideoConsultation>(`/video-consultation/${consultationId}/feedback`, feedback);\n  }\n\n  // WebSocket Connection\n  connectWebSocket(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        const token = localStorage.getItem('authToken');\n        if (!token) {\n          reject(new Error('No authentication token found'));\n          return;\n        }\n\n        const socket = new SockJS('http://localhost:8080/ws');\n        this.stompClient = new Client({\n          webSocketFactory: () => socket,\n          connectHeaders: {\n            Authorization: `Bearer ${token}`\n          },\n          debug: (str) => console.log('STOMP Debug:', str),\n          reconnectDelay: 5000,\n          heartbeatIncoming: 4000,\n          heartbeatOutgoing: 4000,\n        });\n\n        this.stompClient.onConnect = () => {\n          console.log('WebSocket connected for video calls');\n          this.isConnected = true;\n          resolve();\n        };\n\n        this.stompClient.onStompError = (frame) => {\n          console.error('WebSocket STOMP error:', frame);\n          reject(new Error('WebSocket connection failed'));\n        };\n\n        this.stompClient.onWebSocketError = (error) => {\n          console.error('WebSocket error:', error);\n          reject(error);\n        };\n\n        this.stompClient.activate();\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  disconnectWebSocket(): void {\n    if (this.stompClient) {\n      this.stompClient.deactivate();\n      this.isConnected = false;\n    }\n  }\n\n  // Media Access\n  async getUserMedia(constraints: MediaStreamConstraints = { video: true, audio: true }): Promise<MediaStream> {\n    try {\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);\n      return this.localStream;\n    } catch (error) {\n      console.error('Error accessing user media:', error);\n      throw error;\n    }\n  }\n\n  async getDisplayMedia(): Promise<MediaStream> {\n    try {\n      return await navigator.mediaDevices.getDisplayMedia({ video: true, audio: true });\n    } catch (error) {\n      console.error('Error accessing display media:', error);\n      throw error;\n    }\n  }\n\n  // Event Handlers\n  setOnRemoteStream(callback: (stream: MediaStream) => void): void {\n    this.onRemoteStreamCallback = callback;\n  }\n\n  setOnParticipantJoined(callback: (participant: CallParticipant) => void): void {\n    this.onParticipantJoinedCallback = callback;\n  }\n\n  setOnParticipantLeft(callback: (peerId: string) => void): void {\n    this.onParticipantLeftCallback = callback;\n  }\n\n  setOnConnectionStateChange(callback: (state: RTCPeerConnectionState) => void): void {\n    this.onConnectionStateChangeCallback = callback;\n  }\n\n  setOnCallEnded(callback: () => void): void {\n    this.onCallEndedCallback = callback;\n  }\n\n  // Getters\n  getLocalStream(): MediaStream | null {\n    return this.localStream;\n  }\n\n  getRemoteStream(): MediaStream | null {\n    return this.remoteStream;\n  }\n\n  getCurrentRoomId(): string | null {\n    return this.currentRoomId;\n  }\n\n  isWebSocketConnected(): boolean {\n    return this.isConnected;\n  }\n\n  // WebRTC Room Management\n  async joinRoom(roomId: string, userRole: 'DOCTOR' | 'PATIENT'): Promise<void> {\n    if (!this.isConnected || !this.stompClient) {\n      throw new Error('WebSocket not connected');\n    }\n\n    this.currentRoomId = roomId;\n    this.currentPeerId = this.generatePeerId();\n\n    // Subscribe to room messages\n    this.stompClient.subscribe(`/topic/webrtc/${roomId}/${this.getCurrentUserId()}`, (message) => {\n      const webrtcMessage: WebRTCMessage = JSON.parse(message.body);\n      this.handleWebRTCMessage(webrtcMessage);\n    });\n\n    // Send join message\n    this.stompClient.publish({\n      destination: `/app/webrtc/${roomId}/join`,\n      body: JSON.stringify({ userRole }),\n      headers: { Authorization: `Bearer ${localStorage.getItem('authToken')}` }\n    });\n\n    // Initialize peer connection\n    this.initializePeerConnection();\n  }\n\n  async leaveRoom(): Promise<void> {\n    if (!this.currentRoomId || !this.stompClient) return;\n\n    this.stompClient.publish({\n      destination: `/app/webrtc/${this.currentRoomId}/leave`,\n      headers: { Authorization: `Bearer ${localStorage.getItem('authToken')}` }\n    });\n\n    this.cleanup();\n  }\n\n  // WebRTC Peer Connection\n  private initializePeerConnection(): void {\n    this.peerConnection = new RTCPeerConnection({ iceServers: this.iceServers });\n\n    // Add local stream tracks\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        this.peerConnection!.addTrack(track, this.localStream!);\n      });\n    }\n\n    // Handle remote stream\n    this.peerConnection.ontrack = (event) => {\n      console.log('Received remote stream');\n      this.remoteStream = event.streams[0];\n      if (this.onRemoteStreamCallback) {\n        this.onRemoteStreamCallback(this.remoteStream);\n      }\n    };\n\n    // Handle ICE candidates\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate && this.currentRoomId) {\n        this.sendSignal({\n          type: 'ICE_CANDIDATE',\n          data: event.candidate\n        });\n      }\n    };\n\n    // Handle connection state changes\n    this.peerConnection.onconnectionstatechange = () => {\n      const state = this.peerConnection!.connectionState;\n      console.log('Connection state changed:', state);\n      if (this.onConnectionStateChangeCallback) {\n        this.onConnectionStateChangeCallback(state);\n      }\n    };\n  }\n\n  private async handleWebRTCMessage(message: WebRTCMessage): Promise<void> {\n    switch (message.type) {\n      case 'OFFER':\n        await this.handleOffer(message);\n        break;\n      case 'ANSWER':\n        await this.handleAnswer(message);\n        break;\n      case 'ICE_CANDIDATE':\n        await this.handleIceCandidate(message);\n        break;\n      case 'USER_JOINED':\n        this.handleUserJoined(message);\n        break;\n      case 'USER_LEFT':\n        this.handleUserLeft(message);\n        break;\n      case 'EXISTING_PEER':\n        await this.handleExistingPeer(message);\n        break;\n      case 'SCREEN_SHARE_START':\n        this.handleScreenShareStart(message);\n        break;\n      case 'SCREEN_SHARE_STOP':\n        this.handleScreenShareStop(message);\n        break;\n      case 'SESSION_END':\n        this.handleSessionEnd();\n        break;\n    }\n  }\n\n  private async handleOffer(message: WebRTCMessage): Promise<void> {\n    if (!this.peerConnection) return;\n\n    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(message.data));\n    const answer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(answer);\n\n    this.sendSignal({\n      type: 'ANSWER',\n      targetPeerId: message.fromPeerId,\n      data: answer\n    });\n  }\n\n  private async handleAnswer(message: WebRTCMessage): Promise<void> {\n    if (!this.peerConnection) return;\n    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(message.data));\n  }\n\n  private async handleIceCandidate(message: WebRTCMessage): Promise<void> {\n    if (!this.peerConnection) return;\n    await this.peerConnection.addIceCandidate(new RTCIceCandidate(message.data));\n  }\n\n  private handleUserJoined(message: WebRTCMessage): void {\n    console.log('User joined:', message.fromPeerId);\n    if (this.onParticipantJoinedCallback && message.data) {\n      // Create offer for new user\n      this.createOffer(message.fromPeerId!);\n    }\n  }\n\n  private handleUserLeft(message: WebRTCMessage): void {\n    console.log('User left:', message.fromPeerId);\n    if (this.onParticipantLeftCallback) {\n      this.onParticipantLeftCallback(message.fromPeerId!);\n    }\n  }\n\n  private async handleExistingPeer(message: WebRTCMessage): Promise<void> {\n    console.log('Existing peer:', message.fromPeerId);\n    // Wait for the existing peer to create an offer\n  }\n\n  private handleScreenShareStart(message: WebRTCMessage): void {\n    console.log('Screen share started by:', message.fromPeerId);\n  }\n\n  private handleScreenShareStop(message: WebRTCMessage): void {\n    console.log('Screen share stopped by:', message.fromPeerId);\n  }\n\n  private handleSessionEnd(): void {\n    console.log('Session ended');\n    if (this.onCallEndedCallback) {\n      this.onCallEndedCallback();\n    }\n    this.cleanup();\n  }\n\n  private async createOffer(targetPeerId: string): Promise<void> {\n    if (!this.peerConnection) return;\n\n    const offer = await this.peerConnection.createOffer();\n    await this.peerConnection.setLocalDescription(offer);\n\n    this.sendSignal({\n      type: 'OFFER',\n      targetPeerId,\n      data: offer\n    });\n  }\n\n  private sendSignal(signal: WebRTCSignal): void {\n    if (!this.currentRoomId || !this.stompClient) return;\n\n    this.stompClient.publish({\n      destination: `/app/webrtc/${this.currentRoomId}/signal`,\n      body: JSON.stringify(signal),\n      headers: { Authorization: `Bearer ${localStorage.getItem('authToken')}` }\n    });\n  }\n\n  // Media Controls\n  toggleAudio(): boolean {\n    if (!this.localStream) return false;\n\n    const audioTrack = this.localStream.getAudioTracks()[0];\n    if (audioTrack) {\n      audioTrack.enabled = !audioTrack.enabled;\n      return !audioTrack.enabled; // Return muted state\n    }\n    return false;\n  }\n\n  toggleVideo(): boolean {\n    if (!this.localStream) return false;\n\n    const videoTrack = this.localStream.getVideoTracks()[0];\n    if (videoTrack) {\n      videoTrack.enabled = !videoTrack.enabled;\n      return !videoTrack.enabled; // Return muted state\n    }\n    return false;\n  }\n\n  async startScreenShare(): Promise<MediaStream> {\n    try {\n      const screenStream = await this.getDisplayMedia();\n\n      // Replace video track with screen share\n      if (this.peerConnection && this.localStream) {\n        const videoTrack = this.localStream.getVideoTracks()[0];\n        const screenTrack = screenStream.getVideoTracks()[0];\n\n        const sender = this.peerConnection.getSenders().find(s =>\n          s.track && s.track.kind === 'video'\n        );\n\n        if (sender) {\n          await sender.replaceTrack(screenTrack);\n        }\n\n        // Update local stream\n        this.localStream.removeTrack(videoTrack);\n        this.localStream.addTrack(screenTrack);\n\n        // Notify other participants\n        this.sendSignal({ type: 'SCREEN_SHARE_START' });\n\n        // Handle screen share end\n        screenTrack.onended = () => {\n          this.stopScreenShare();\n        };\n      }\n\n      return screenStream;\n    } catch (error) {\n      console.error('Error starting screen share:', error);\n      throw error;\n    }\n  }\n\n  async stopScreenShare(): Promise<void> {\n    try {\n      // Get camera stream again\n      const cameraStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });\n      const cameraTrack = cameraStream.getVideoTracks()[0];\n\n      if (this.peerConnection && this.localStream) {\n        const screenTrack = this.localStream.getVideoTracks()[0];\n\n        const sender = this.peerConnection.getSenders().find(s =>\n          s.track && s.track.kind === 'video'\n        );\n\n        if (sender) {\n          await sender.replaceTrack(cameraTrack);\n        }\n\n        // Update local stream\n        this.localStream.removeTrack(screenTrack);\n        this.localStream.addTrack(cameraTrack);\n\n        // Stop screen track\n        screenTrack.stop();\n\n        // Notify other participants\n        this.sendSignal({ type: 'SCREEN_SHARE_STOP' });\n      }\n    } catch (error) {\n      console.error('Error stopping screen share:', error);\n      throw error;\n    }\n  }\n\n  // Recording\n  startRecording(): void {\n    if (!this.localStream) return;\n\n    this.recordedChunks = [];\n    this.mediaRecorder = new MediaRecorder(this.localStream);\n\n    this.mediaRecorder.ondataavailable = (event) => {\n      if (event.data.size > 0) {\n        this.recordedChunks.push(event.data);\n      }\n    };\n\n    this.mediaRecorder.start();\n  }\n\n  stopRecording(): Blob | null {\n    if (!this.mediaRecorder) return null;\n\n    this.mediaRecorder.stop();\n\n    if (this.recordedChunks.length > 0) {\n      return new Blob(this.recordedChunks, { type: 'video/webm' });\n    }\n\n    return null;\n  }\n\n  // Utility methods\n  private generatePeerId(): string {\n    return 'peer_' + Math.random().toString(36).substr(2, 9);\n  }\n\n  private getCurrentUserId(): number {\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    return user.id || 0;\n  }\n\n  // Cleanup\n  cleanup(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n      this.mediaRecorder.stop();\n    }\n\n    this.currentRoomId = null;\n    this.currentPeerId = null;\n    this.remoteStream = null;\n    this.recordedChunks = [];\n  }\n}\n\nexport const videoCallService = new VideoCallService();\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,MAAM,QAAQ,gBAAgB;AACvC,OAAOC,MAAM,MAAM,eAAe;AA8ElC,MAAMC,gBAAgB,CAAC;EAAAC,YAAA;IAAA,KACbC,WAAW,GAAkB,IAAI;IAAA,KACjCC,WAAW,GAAuB,IAAI;IAAA,KACtCC,YAAY,GAAuB,IAAI;IAAA,KACvCC,cAAc,GAA6B,IAAI;IAAA,KAC/CC,aAAa,GAAkB,IAAI;IAAA,KACnCC,aAAa,GAAkB,IAAI;IAAA,KACnCC,WAAW,GAAG,KAAK;IAAA,KACnBC,aAAa,GAAyB,IAAI;IAAA,KAC1CC,cAAc,GAAW,EAAE;IAEnC;IAAA,KACQC,sBAAsB;IAAA,KACtBC,2BAA2B;IAAA,KAC3BC,yBAAyB;IAAA,KACzBC,+BAA+B;IAAA,KAC/BC,mBAAmB;IAE3B;IAAA,KACQC,UAAU,GAAG,CACnB;MAAEC,IAAI,EAAE;IAA+B,CAAC,EACxC;MAAEA,IAAI,EAAE;IAAgC,CAAC,CAC1C;EAAA;EAED;EACA,MAAMC,kBAAkBA,CAACC,aAAqB,EAAEC,IAAsB,EAA8B;IAClG,OAAOvB,UAAU,CAACwB,IAAI,CAAoB,4BAA4B,EAAE;MACtEF,aAAa;MACbC;IACF,CAAC,CAAC;EACJ;EAEA,MAAME,iBAAiBA,CAACC,cAAsB,EAA8B;IAC1E,OAAO1B,UAAU,CAACwB,IAAI,CAAoB,uBAAuBE,cAAc,QAAQ,CAAC;EAC1F;EAEA,MAAMC,eAAeA,CAACD,cAAsB,EAAEE,KAAc,EAAEC,SAAkB,EAAEC,eAAwB,EAA8B;IACtI,OAAO9B,UAAU,CAACwB,IAAI,CAAoB,uBAAuBE,cAAc,MAAM,EAAE;MACrFE,KAAK;MACLC,SAAS;MACTC;IACF,CAAC,CAAC;EACJ;EAEA,MAAMC,eAAeA,CAACL,cAAsB,EAA8B;IACxE,OAAO1B,UAAU,CAACgC,GAAG,CAAoB,uBAAuBN,cAAc,EAAE,CAAC;EACnF;EAEA,MAAMO,uBAAuBA,CAACC,MAAc,EAA8B;IACxE,OAAOlC,UAAU,CAACgC,GAAG,CAAoB,4BAA4BE,MAAM,EAAE,CAAC;EAChF;EAEA,MAAMC,oBAAoBA,CAAA,EAAiC;IACzD,OAAOnC,UAAU,CAACgC,GAAG,CAAsB,wCAAwC,CAAC;EACtF;EAEA,MAAMI,wBAAwBA,CAAA,EAAiC;IAC7D,OAAOpC,UAAU,CAACgC,GAAG,CAAsB,mCAAmC,CAAC;EACjF;EAEA,MAAMK,0BAA0BA,CAACX,cAAsB,EAAEY,QAIxD,EAA8B;IAC7B,OAAOtC,UAAU,CAACuC,GAAG,CAAoB,uBAAuBb,cAAc,WAAW,EAAEY,QAAQ,CAAC;EACtG;EAEA,MAAME,cAAcA,CAACd,cAAsB,EAAEe,QAI5C,EAA8B;IAC7B,OAAOzC,UAAU,CAACwB,IAAI,CAAoB,uBAAuBE,cAAc,WAAW,EAAEe,QAAQ,CAAC;EACvG;;EAEA;EACAC,gBAAgBA,CAAA,EAAkB;IAChC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAC/C,IAAI,CAACF,KAAK,EAAE;UACVD,MAAM,CAAC,IAAII,KAAK,CAAC,+BAA+B,CAAC,CAAC;UAClD;QACF;QAEA,MAAMC,MAAM,GAAG,IAAIhD,MAAM,CAAC,0BAA0B,CAAC;QACrD,IAAI,CAACG,WAAW,GAAG,IAAIJ,MAAM,CAAC;UAC5BkD,gBAAgB,EAAEA,CAAA,KAAMD,MAAM;UAC9BE,cAAc,EAAE;YACdC,aAAa,EAAE,UAAUP,KAAK;UAChC,CAAC;UACDQ,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,GAAG,CAAC;UAChDG,cAAc,EAAE,IAAI;UACpBC,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;QACrB,CAAC,CAAC;QAEF,IAAI,CAACvD,WAAW,CAACwD,SAAS,GAAG,MAAM;UACjCL,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;UAClD,IAAI,CAAC9C,WAAW,GAAG,IAAI;UACvBiC,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAACvC,WAAW,CAACyD,YAAY,GAAIC,KAAK,IAAK;UACzCP,OAAO,CAACQ,KAAK,CAAC,wBAAwB,EAAED,KAAK,CAAC;UAC9ClB,MAAM,CAAC,IAAII,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC5C,WAAW,CAAC4D,gBAAgB,GAAID,KAAK,IAAK;UAC7CR,OAAO,CAACQ,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;UACxCnB,MAAM,CAACmB,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC3D,WAAW,CAAC6D,QAAQ,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdnB,MAAM,CAACmB,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEAG,mBAAmBA,CAAA,EAAS;IAC1B,IAAI,IAAI,CAAC9D,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC+D,UAAU,CAAC,CAAC;MAC7B,IAAI,CAACzD,WAAW,GAAG,KAAK;IAC1B;EACF;;EAEA;EACA,MAAM0D,YAAYA,CAACC,WAAmC,GAAG;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAwB;IAC3G,IAAI;MACF,IAAI,CAAClE,WAAW,GAAG,MAAMmE,SAAS,CAACC,YAAY,CAACL,YAAY,CAACC,WAAW,CAAC;MACzE,OAAO,IAAI,CAAChE,WAAW;IACzB,CAAC,CAAC,OAAO0D,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMW,eAAeA,CAAA,EAAyB;IAC5C,IAAI;MACF,OAAO,MAAMF,SAAS,CAACC,YAAY,CAACC,eAAe,CAAC;QAAEJ,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;IACnF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;EACAY,iBAAiBA,CAACC,QAAuC,EAAQ;IAC/D,IAAI,CAAC/D,sBAAsB,GAAG+D,QAAQ;EACxC;EAEAC,sBAAsBA,CAACD,QAAgD,EAAQ;IAC7E,IAAI,CAAC9D,2BAA2B,GAAG8D,QAAQ;EAC7C;EAEAE,oBAAoBA,CAACF,QAAkC,EAAQ;IAC7D,IAAI,CAAC7D,yBAAyB,GAAG6D,QAAQ;EAC3C;EAEAG,0BAA0BA,CAACH,QAAiD,EAAQ;IAClF,IAAI,CAAC5D,+BAA+B,GAAG4D,QAAQ;EACjD;EAEAI,cAAcA,CAACJ,QAAoB,EAAQ;IACzC,IAAI,CAAC3D,mBAAmB,GAAG2D,QAAQ;EACrC;;EAEA;EACAK,cAAcA,CAAA,EAAuB;IACnC,OAAO,IAAI,CAAC5E,WAAW;EACzB;EAEA6E,eAAeA,CAAA,EAAuB;IACpC,OAAO,IAAI,CAAC5E,YAAY;EAC1B;EAEA6E,gBAAgBA,CAAA,EAAkB;IAChC,OAAO,IAAI,CAAC3E,aAAa;EAC3B;EAEA4E,oBAAoBA,CAAA,EAAY;IAC9B,OAAO,IAAI,CAAC1E,WAAW;EACzB;;EAEA;EACA,MAAM2E,QAAQA,CAACpD,MAAc,EAAEqD,QAA8B,EAAiB;IAC5E,IAAI,CAAC,IAAI,CAAC5E,WAAW,IAAI,CAAC,IAAI,CAACN,WAAW,EAAE;MAC1C,MAAM,IAAI4C,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,IAAI,CAACxC,aAAa,GAAGyB,MAAM;IAC3B,IAAI,CAACxB,aAAa,GAAG,IAAI,CAAC8E,cAAc,CAAC,CAAC;;IAE1C;IACA,IAAI,CAACnF,WAAW,CAACoF,SAAS,CAAC,iBAAiBvD,MAAM,IAAI,IAAI,CAACwD,gBAAgB,CAAC,CAAC,EAAE,EAAGC,OAAO,IAAK;MAC5F,MAAMC,aAA4B,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAACI,IAAI,CAAC;MAC7D,IAAI,CAACC,mBAAmB,CAACJ,aAAa,CAAC;IACzC,CAAC,CAAC;;IAEF;IACA,IAAI,CAACvF,WAAW,CAAC4F,OAAO,CAAC;MACvBC,WAAW,EAAE,eAAehE,MAAM,OAAO;MACzC6D,IAAI,EAAEF,IAAI,CAACM,SAAS,CAAC;QAAEZ;MAAS,CAAC,CAAC;MAClCa,OAAO,EAAE;QAAE/C,aAAa,EAAE,UAAUN,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAAG;IAC1E,CAAC,CAAC;;IAEF;IACA,IAAI,CAACqD,wBAAwB,CAAC,CAAC;EACjC;EAEA,MAAMC,SAASA,CAAA,EAAkB;IAC/B,IAAI,CAAC,IAAI,CAAC7F,aAAa,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;IAE9C,IAAI,CAACA,WAAW,CAAC4F,OAAO,CAAC;MACvBC,WAAW,EAAE,eAAe,IAAI,CAACzF,aAAa,QAAQ;MACtD2F,OAAO,EAAE;QAAE/C,aAAa,EAAE,UAAUN,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAAG;IAC1E,CAAC,CAAC;IAEF,IAAI,CAACuD,OAAO,CAAC,CAAC;EAChB;;EAEA;EACQF,wBAAwBA,CAAA,EAAS;IACvC,IAAI,CAAC7F,cAAc,GAAG,IAAIgG,iBAAiB,CAAC;MAAErF,UAAU,EAAE,IAAI,CAACA;IAAW,CAAC,CAAC;;IAE5E;IACA,IAAI,IAAI,CAACb,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACmG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAC5C,IAAI,CAACnG,cAAc,CAAEoG,QAAQ,CAACD,KAAK,EAAE,IAAI,CAACrG,WAAY,CAAC;MACzD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACE,cAAc,CAACqG,OAAO,GAAIC,KAAK,IAAK;MACvCtD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAAClD,YAAY,GAAGuG,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MACpC,IAAI,IAAI,CAACjG,sBAAsB,EAAE;QAC/B,IAAI,CAACA,sBAAsB,CAAC,IAAI,CAACP,YAAY,CAAC;MAChD;IACF,CAAC;;IAED;IACA,IAAI,CAACC,cAAc,CAACwG,cAAc,GAAIF,KAAK,IAAK;MAC9C,IAAIA,KAAK,CAACG,SAAS,IAAI,IAAI,CAACxG,aAAa,EAAE;QACzC,IAAI,CAACyG,UAAU,CAAC;UACd3F,IAAI,EAAE,eAAe;UACrB4F,IAAI,EAAEL,KAAK,CAACG;QACd,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACA,IAAI,CAACzG,cAAc,CAAC4G,uBAAuB,GAAG,MAAM;MAClD,MAAMC,KAAK,GAAG,IAAI,CAAC7G,cAAc,CAAE8G,eAAe;MAClD9D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4D,KAAK,CAAC;MAC/C,IAAI,IAAI,CAACpG,+BAA+B,EAAE;QACxC,IAAI,CAACA,+BAA+B,CAACoG,KAAK,CAAC;MAC7C;IACF,CAAC;EACH;EAEA,MAAcrB,mBAAmBA,CAACL,OAAsB,EAAiB;IACvE,QAAQA,OAAO,CAACpE,IAAI;MAClB,KAAK,OAAO;QACV,MAAM,IAAI,CAACgG,WAAW,CAAC5B,OAAO,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,MAAM,IAAI,CAAC6B,YAAY,CAAC7B,OAAO,CAAC;QAChC;MACF,KAAK,eAAe;QAClB,MAAM,IAAI,CAAC8B,kBAAkB,CAAC9B,OAAO,CAAC;QACtC;MACF,KAAK,aAAa;QAChB,IAAI,CAAC+B,gBAAgB,CAAC/B,OAAO,CAAC;QAC9B;MACF,KAAK,WAAW;QACd,IAAI,CAACgC,cAAc,CAAChC,OAAO,CAAC;QAC5B;MACF,KAAK,eAAe;QAClB,MAAM,IAAI,CAACiC,kBAAkB,CAACjC,OAAO,CAAC;QACtC;MACF,KAAK,oBAAoB;QACvB,IAAI,CAACkC,sBAAsB,CAAClC,OAAO,CAAC;QACpC;MACF,KAAK,mBAAmB;QACtB,IAAI,CAACmC,qBAAqB,CAACnC,OAAO,CAAC;QACnC;MACF,KAAK,aAAa;QAChB,IAAI,CAACoC,gBAAgB,CAAC,CAAC;QACvB;IACJ;EACF;EAEA,MAAcR,WAAWA,CAAC5B,OAAsB,EAAiB;IAC/D,IAAI,CAAC,IAAI,CAACnF,cAAc,EAAE;IAE1B,MAAM,IAAI,CAACA,cAAc,CAACwH,oBAAoB,CAAC,IAAIC,qBAAqB,CAACtC,OAAO,CAACwB,IAAI,CAAC,CAAC;IACvF,MAAMe,MAAM,GAAG,MAAM,IAAI,CAAC1H,cAAc,CAAC2H,YAAY,CAAC,CAAC;IACvD,MAAM,IAAI,CAAC3H,cAAc,CAAC4H,mBAAmB,CAACF,MAAM,CAAC;IAErD,IAAI,CAAChB,UAAU,CAAC;MACd3F,IAAI,EAAE,QAAQ;MACd8G,YAAY,EAAE1C,OAAO,CAAC2C,UAAU;MAChCnB,IAAI,EAAEe;IACR,CAAC,CAAC;EACJ;EAEA,MAAcV,YAAYA,CAAC7B,OAAsB,EAAiB;IAChE,IAAI,CAAC,IAAI,CAACnF,cAAc,EAAE;IAC1B,MAAM,IAAI,CAACA,cAAc,CAACwH,oBAAoB,CAAC,IAAIC,qBAAqB,CAACtC,OAAO,CAACwB,IAAI,CAAC,CAAC;EACzF;EAEA,MAAcM,kBAAkBA,CAAC9B,OAAsB,EAAiB;IACtE,IAAI,CAAC,IAAI,CAACnF,cAAc,EAAE;IAC1B,MAAM,IAAI,CAACA,cAAc,CAAC+H,eAAe,CAAC,IAAIC,eAAe,CAAC7C,OAAO,CAACwB,IAAI,CAAC,CAAC;EAC9E;EAEQO,gBAAgBA,CAAC/B,OAAsB,EAAQ;IACrDnC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkC,OAAO,CAAC2C,UAAU,CAAC;IAC/C,IAAI,IAAI,CAACvH,2BAA2B,IAAI4E,OAAO,CAACwB,IAAI,EAAE;MACpD;MACA,IAAI,CAACsB,WAAW,CAAC9C,OAAO,CAAC2C,UAAW,CAAC;IACvC;EACF;EAEQX,cAAcA,CAAChC,OAAsB,EAAQ;IACnDnC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkC,OAAO,CAAC2C,UAAU,CAAC;IAC7C,IAAI,IAAI,CAACtH,yBAAyB,EAAE;MAClC,IAAI,CAACA,yBAAyB,CAAC2E,OAAO,CAAC2C,UAAW,CAAC;IACrD;EACF;EAEA,MAAcV,kBAAkBA,CAACjC,OAAsB,EAAiB;IACtEnC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkC,OAAO,CAAC2C,UAAU,CAAC;IACjD;EACF;EAEQT,sBAAsBA,CAAClC,OAAsB,EAAQ;IAC3DnC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkC,OAAO,CAAC2C,UAAU,CAAC;EAC7D;EAEQR,qBAAqBA,CAACnC,OAAsB,EAAQ;IAC1DnC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkC,OAAO,CAAC2C,UAAU,CAAC;EAC7D;EAEQP,gBAAgBA,CAAA,EAAS;IAC/BvE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B,IAAI,IAAI,CAACvC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACqF,OAAO,CAAC,CAAC;EAChB;EAEA,MAAckC,WAAWA,CAACJ,YAAoB,EAAiB;IAC7D,IAAI,CAAC,IAAI,CAAC7H,cAAc,EAAE;IAE1B,MAAMkI,KAAK,GAAG,MAAM,IAAI,CAAClI,cAAc,CAACiI,WAAW,CAAC,CAAC;IACrD,MAAM,IAAI,CAACjI,cAAc,CAAC4H,mBAAmB,CAACM,KAAK,CAAC;IAEpD,IAAI,CAACxB,UAAU,CAAC;MACd3F,IAAI,EAAE,OAAO;MACb8G,YAAY;MACZlB,IAAI,EAAEuB;IACR,CAAC,CAAC;EACJ;EAEQxB,UAAUA,CAACyB,MAAoB,EAAQ;IAC7C,IAAI,CAAC,IAAI,CAAClI,aAAa,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;IAE9C,IAAI,CAACA,WAAW,CAAC4F,OAAO,CAAC;MACvBC,WAAW,EAAE,eAAe,IAAI,CAACzF,aAAa,SAAS;MACvDsF,IAAI,EAAEF,IAAI,CAACM,SAAS,CAACwC,MAAM,CAAC;MAC5BvC,OAAO,EAAE;QAAE/C,aAAa,EAAE,UAAUN,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAAG;IAC1E,CAAC,CAAC;EACJ;;EAEA;EACA4F,WAAWA,CAAA,EAAY;IACrB,IAAI,CAAC,IAAI,CAACtI,WAAW,EAAE,OAAO,KAAK;IAEnC,MAAMuI,UAAU,GAAG,IAAI,CAACvI,WAAW,CAACwI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,IAAID,UAAU,EAAE;MACdA,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO;MACxC,OAAO,CAACF,UAAU,CAACE,OAAO,CAAC,CAAC;IAC9B;IACA,OAAO,KAAK;EACd;EAEAC,WAAWA,CAAA,EAAY;IACrB,IAAI,CAAC,IAAI,CAAC1I,WAAW,EAAE,OAAO,KAAK;IAEnC,MAAM2I,UAAU,GAAG,IAAI,CAAC3I,WAAW,CAAC4I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,IAAID,UAAU,EAAE;MACdA,UAAU,CAACF,OAAO,GAAG,CAACE,UAAU,CAACF,OAAO;MACxC,OAAO,CAACE,UAAU,CAACF,OAAO,CAAC,CAAC;IAC9B;IACA,OAAO,KAAK;EACd;EAEA,MAAMI,gBAAgBA,CAAA,EAAyB;IAC7C,IAAI;MACF,MAAMC,YAAY,GAAG,MAAM,IAAI,CAACzE,eAAe,CAAC,CAAC;;MAEjD;MACA,IAAI,IAAI,CAACnE,cAAc,IAAI,IAAI,CAACF,WAAW,EAAE;QAC3C,MAAM2I,UAAU,GAAG,IAAI,CAAC3I,WAAW,CAAC4I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,MAAMG,WAAW,GAAGD,YAAY,CAACF,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,MAAMI,MAAM,GAAG,IAAI,CAAC9I,cAAc,CAAC+I,UAAU,CAAC,CAAC,CAACC,IAAI,CAACC,CAAC,IACpDA,CAAC,CAAC9C,KAAK,IAAI8C,CAAC,CAAC9C,KAAK,CAAC+C,IAAI,KAAK,OAC9B,CAAC;QAED,IAAIJ,MAAM,EAAE;UACV,MAAMA,MAAM,CAACK,YAAY,CAACN,WAAW,CAAC;QACxC;;QAEA;QACA,IAAI,CAAC/I,WAAW,CAACsJ,WAAW,CAACX,UAAU,CAAC;QACxC,IAAI,CAAC3I,WAAW,CAACsG,QAAQ,CAACyC,WAAW,CAAC;;QAEtC;QACA,IAAI,CAACnC,UAAU,CAAC;UAAE3F,IAAI,EAAE;QAAqB,CAAC,CAAC;;QAE/C;QACA8H,WAAW,CAACQ,OAAO,GAAG,MAAM;UAC1B,IAAI,CAACC,eAAe,CAAC,CAAC;QACxB,CAAC;MACH;MAEA,OAAOV,YAAY;IACrB,CAAC,CAAC,OAAOpF,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAM8F,eAAeA,CAAA,EAAkB;IACrC,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,MAAMtF,SAAS,CAACC,YAAY,CAACL,YAAY,CAAC;QAAEE,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAM,CAAC,CAAC;MAC7F,MAAMwF,WAAW,GAAGD,YAAY,CAACb,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAEpD,IAAI,IAAI,CAAC1I,cAAc,IAAI,IAAI,CAACF,WAAW,EAAE;QAC3C,MAAM+I,WAAW,GAAG,IAAI,CAAC/I,WAAW,CAAC4I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,MAAMI,MAAM,GAAG,IAAI,CAAC9I,cAAc,CAAC+I,UAAU,CAAC,CAAC,CAACC,IAAI,CAACC,CAAC,IACpDA,CAAC,CAAC9C,KAAK,IAAI8C,CAAC,CAAC9C,KAAK,CAAC+C,IAAI,KAAK,OAC9B,CAAC;QAED,IAAIJ,MAAM,EAAE;UACV,MAAMA,MAAM,CAACK,YAAY,CAACK,WAAW,CAAC;QACxC;;QAEA;QACA,IAAI,CAAC1J,WAAW,CAACsJ,WAAW,CAACP,WAAW,CAAC;QACzC,IAAI,CAAC/I,WAAW,CAACsG,QAAQ,CAACoD,WAAW,CAAC;;QAEtC;QACAX,WAAW,CAACY,IAAI,CAAC,CAAC;;QAElB;QACA,IAAI,CAAC/C,UAAU,CAAC;UAAE3F,IAAI,EAAE;QAAoB,CAAC,CAAC;MAChD;IACF,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;EACAkG,cAAcA,CAAA,EAAS;IACrB,IAAI,CAAC,IAAI,CAAC5J,WAAW,EAAE;IAEvB,IAAI,CAACO,cAAc,GAAG,EAAE;IACxB,IAAI,CAACD,aAAa,GAAG,IAAIuJ,aAAa,CAAC,IAAI,CAAC7J,WAAW,CAAC;IAExD,IAAI,CAACM,aAAa,CAACwJ,eAAe,GAAItD,KAAK,IAAK;MAC9C,IAAIA,KAAK,CAACK,IAAI,CAACkD,IAAI,GAAG,CAAC,EAAE;QACvB,IAAI,CAACxJ,cAAc,CAACyJ,IAAI,CAACxD,KAAK,CAACK,IAAI,CAAC;MACtC;IACF,CAAC;IAED,IAAI,CAACvG,aAAa,CAAC2J,KAAK,CAAC,CAAC;EAC5B;EAEAC,aAAaA,CAAA,EAAgB;IAC3B,IAAI,CAAC,IAAI,CAAC5J,aAAa,EAAE,OAAO,IAAI;IAEpC,IAAI,CAACA,aAAa,CAACqJ,IAAI,CAAC,CAAC;IAEzB,IAAI,IAAI,CAACpJ,cAAc,CAAC4J,MAAM,GAAG,CAAC,EAAE;MAClC,OAAO,IAAIC,IAAI,CAAC,IAAI,CAAC7J,cAAc,EAAE;QAAEU,IAAI,EAAE;MAAa,CAAC,CAAC;IAC9D;IAEA,OAAO,IAAI;EACb;;EAEA;EACQiE,cAAcA,CAAA,EAAW;IAC/B,OAAO,OAAO,GAAGmF,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D;EAEQpF,gBAAgBA,CAAA,EAAW;IACjC,MAAMqF,IAAI,GAAGlF,IAAI,CAACC,KAAK,CAAC/C,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D,OAAO+H,IAAI,CAACC,EAAE,IAAI,CAAC;EACrB;;EAEA;EACAzE,OAAOA,CAAA,EAAS;IACd,IAAI,IAAI,CAACjG,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACmG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACsD,IAAI,CAAC,CAAC,CAAC;MAC3D,IAAI,CAAC3J,WAAW,GAAG,IAAI;IACzB;IAEA,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACyK,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACzK,cAAc,GAAG,IAAI;IAC5B;IAEA,IAAI,IAAI,CAACI,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyG,KAAK,KAAK,UAAU,EAAE;MACjE,IAAI,CAACzG,aAAa,CAACqJ,IAAI,CAAC,CAAC;IAC3B;IAEA,IAAI,CAACxJ,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACH,YAAY,GAAG,IAAI;IACxB,IAAI,CAACM,cAAc,GAAG,EAAE;EAC1B;AACF;AAEA,OAAO,MAAMqK,gBAAgB,GAAG,IAAI/K,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}