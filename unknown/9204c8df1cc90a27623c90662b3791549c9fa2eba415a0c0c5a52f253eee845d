{"ast": null, "code": "/* eslint-disable */\n/* jscs: disable */\n'use strict';\n\n// pulled specific shims from https://github.com/es-shims/es5-shim\nvar ArrayPrototype = Array.prototype;\nvar ObjectPrototype = Object.prototype;\nvar FunctionPrototype = Function.prototype;\nvar StringPrototype = String.prototype;\nvar array_slice = ArrayPrototype.slice;\nvar _toString = ObjectPrototype.toString;\nvar isFunction = function (val) {\n  return ObjectPrototype.toString.call(val) === '[object Function]';\n};\nvar isArray = function isArray(obj) {\n  return _toString.call(obj) === '[object Array]';\n};\nvar isString = function isString(obj) {\n  return _toString.call(obj) === '[object String]';\n};\nvar supportsDescriptors = Object.defineProperty && function () {\n  try {\n    Object.defineProperty({}, 'x', {});\n    return true;\n  } catch (e) {\n    /* this is ES3 */\n    return false;\n  }\n}();\n\n// Define configurable, writable and non-enumerable props\n// if they don't exist.\nvar defineProperty;\nif (supportsDescriptors) {\n  defineProperty = function (object, name, method, forceAssign) {\n    if (!forceAssign && name in object) {\n      return;\n    }\n    Object.defineProperty(object, name, {\n      configurable: true,\n      enumerable: false,\n      writable: true,\n      value: method\n    });\n  };\n} else {\n  defineProperty = function (object, name, method, forceAssign) {\n    if (!forceAssign && name in object) {\n      return;\n    }\n    object[name] = method;\n  };\n}\nvar defineProperties = function (object, map, forceAssign) {\n  for (var name in map) {\n    if (ObjectPrototype.hasOwnProperty.call(map, name)) {\n      defineProperty(object, name, map[name], forceAssign);\n    }\n  }\n};\nvar toObject = function (o) {\n  if (o == null) {\n    // this matches both null and undefined\n    throw new TypeError(\"can't convert \" + o + ' to object');\n  }\n  return Object(o);\n};\n\n//\n// Util\n// ======\n//\n\n// ES5 9.4\n// http://es5.github.com/#x9.4\n// http://jsperf.com/to-integer\n\nfunction toInteger(num) {\n  var n = +num;\n  if (n !== n) {\n    // isNaN\n    n = 0;\n  } else if (n !== 0 && n !== 1 / 0 && n !== -(1 / 0)) {\n    n = (n > 0 || -1) * Math.floor(Math.abs(n));\n  }\n  return n;\n}\nfunction ToUint32(x) {\n  return x >>> 0;\n}\n\n//\n// Function\n// ========\n//\n\n// ES-5 ********\n// http://es5.github.com/#x********\n\nfunction Empty() {}\ndefineProperties(FunctionPrototype, {\n  bind: function bind(that) {\n    // .length is 1\n    // 1. Let Target be the this value.\n    var target = this;\n    // 2. If IsCallable(Target) is false, throw a TypeError exception.\n    if (!isFunction(target)) {\n      throw new TypeError('Function.prototype.bind called on incompatible ' + target);\n    }\n    // 3. Let A be a new (possibly empty) internal list of all of the\n    //   argument values provided after thisArg (arg1, arg2 etc), in order.\n    // XXX slicedArgs will stand in for \"A\" if used\n    var args = array_slice.call(arguments, 1); // for normal call\n    // 4. Let F be a new native ECMAScript object.\n    // 11. Set the [[Prototype]] internal property of F to the standard\n    //   built-in Function prototype object as specified in ********.\n    // 12. Set the [[Call]] internal property of F as described in\n    //   ********.1.\n    // 13. Set the [[Construct]] internal property of F as described in\n    //   ********.2.\n    // 14. Set the [[HasInstance]] internal property of F as described in\n    //   ********.3.\n    var binder = function () {\n      if (this instanceof bound) {\n        // ********.2 [[Construct]]\n        // When the [[Construct]] internal method of a function object,\n        // F that was created using the bind function is called with a\n        // list of arguments ExtraArgs, the following steps are taken:\n        // 1. Let target be the value of F's [[TargetFunction]]\n        //   internal property.\n        // 2. If target has no [[Construct]] internal method, a\n        //   TypeError exception is thrown.\n        // 3. Let boundArgs be the value of F's [[BoundArgs]] internal\n        //   property.\n        // 4. Let args be a new list containing the same values as the\n        //   list boundArgs in the same order followed by the same\n        //   values as the list ExtraArgs in the same order.\n        // 5. Return the result of calling the [[Construct]] internal\n        //   method of target providing args as the arguments.\n\n        var result = target.apply(this, args.concat(array_slice.call(arguments)));\n        if (Object(result) === result) {\n          return result;\n        }\n        return this;\n      } else {\n        // ********.1 [[Call]]\n        // When the [[Call]] internal method of a function object, F,\n        // which was created using the bind function is called with a\n        // this value and a list of arguments ExtraArgs, the following\n        // steps are taken:\n        // 1. Let boundArgs be the value of F's [[BoundArgs]] internal\n        //   property.\n        // 2. Let boundThis be the value of F's [[BoundThis]] internal\n        //   property.\n        // 3. Let target be the value of F's [[TargetFunction]] internal\n        //   property.\n        // 4. Let args be a new list containing the same values as the\n        //   list boundArgs in the same order followed by the same\n        //   values as the list ExtraArgs in the same order.\n        // 5. Return the result of calling the [[Call]] internal method\n        //   of target providing boundThis as the this value and\n        //   providing args as the arguments.\n\n        // equiv: target.call(this, ...boundArgs, ...args)\n        return target.apply(that, args.concat(array_slice.call(arguments)));\n      }\n    };\n\n    // 15. If the [[Class]] internal property of Target is \"Function\", then\n    //     a. Let L be the length property of Target minus the length of A.\n    //     b. Set the length own property of F to either 0 or L, whichever is\n    //       larger.\n    // 16. Else set the length own property of F to 0.\n\n    var boundLength = Math.max(0, target.length - args.length);\n\n    // 17. Set the attributes of the length own property of F to the values\n    //   specified in 15.3.5.1.\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n      boundArgs.push('$' + i);\n    }\n\n    // XXX Build a dynamic function with desired amount of arguments is the only\n    // way to set the length property of a function.\n    // In environments where Content Security Policies enabled (Chrome extensions,\n    // for ex.) all use of eval or Function costructor throws an exception.\n    // However in all of these environments Function.prototype.bind exists\n    // and so this code will never be executed.\n    var bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this, arguments); }')(binder);\n    if (target.prototype) {\n      Empty.prototype = target.prototype;\n      bound.prototype = new Empty();\n      // Clean up dangling references.\n      Empty.prototype = null;\n    }\n\n    // TODO\n    // 18. Set the [[Extensible]] internal property of F to true.\n\n    // TODO\n    // 19. Let thrower be the [[ThrowTypeError]] function Object (13.2.3).\n    // 20. Call the [[DefineOwnProperty]] internal method of F with\n    //   arguments \"caller\", PropertyDescriptor {[[Get]]: thrower, [[Set]]:\n    //   thrower, [[Enumerable]]: false, [[Configurable]]: false}, and\n    //   false.\n    // 21. Call the [[DefineOwnProperty]] internal method of F with\n    //   arguments \"arguments\", PropertyDescriptor {[[Get]]: thrower,\n    //   [[Set]]: thrower, [[Enumerable]]: false, [[Configurable]]: false},\n    //   and false.\n\n    // TODO\n    // NOTE Function objects created using Function.prototype.bind do not\n    // have a prototype property or the [[Code]], [[FormalParameters]], and\n    // [[Scope]] internal properties.\n    // XXX can't delete prototype in pure-js.\n\n    // 22. Return F.\n    return bound;\n  }\n});\n\n//\n// Array\n// =====\n//\n\n// ES5 ********\n// http://es5.github.com/#x********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/isArray\ndefineProperties(Array, {\n  isArray: isArray\n});\nvar boxedString = Object('a');\nvar splitString = boxedString[0] !== 'a' || !(0 in boxedString);\nvar properlyBoxesContext = function properlyBoxed(method) {\n  // Check node 0.6.21 bug where third parameter is not boxed\n  var properlyBoxesNonStrict = true;\n  var properlyBoxesStrict = true;\n  if (method) {\n    method.call('foo', function (_, __, context) {\n      if (typeof context !== 'object') {\n        properlyBoxesNonStrict = false;\n      }\n    });\n    method.call([1], function () {\n      'use strict';\n\n      properlyBoxesStrict = typeof this === 'string';\n    }, 'x');\n  }\n  return !!method && properlyBoxesNonStrict && properlyBoxesStrict;\n};\ndefineProperties(ArrayPrototype, {\n  forEach: function forEach(fun /*, thisp*/) {\n    var object = toObject(this),\n      self = splitString && isString(this) ? this.split('') : object,\n      thisp = arguments[1],\n      i = -1,\n      length = self.length >>> 0;\n\n    // If no callback function or if callback is not a callable function\n    if (!isFunction(fun)) {\n      throw new TypeError(); // TODO message\n    }\n    while (++i < length) {\n      if (i in self) {\n        // Invoke the callback function with call, passing arguments:\n        // context, property value, property key, thisArg object\n        // context\n        fun.call(thisp, self[i], i, object);\n      }\n    }\n  }\n}, !properlyBoxesContext(ArrayPrototype.forEach));\n\n// ES5 *********\n// http://es5.github.com/#x*********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/indexOf\nvar hasFirefox2IndexOfBug = Array.prototype.indexOf && [0, 1].indexOf(1, 2) !== -1;\ndefineProperties(ArrayPrototype, {\n  indexOf: function indexOf(sought /*, fromIndex */) {\n    var self = splitString && isString(this) ? this.split('') : toObject(this),\n      length = self.length >>> 0;\n    if (!length) {\n      return -1;\n    }\n    var i = 0;\n    if (arguments.length > 1) {\n      i = toInteger(arguments[1]);\n    }\n\n    // handle negative indices\n    i = i >= 0 ? i : Math.max(0, length + i);\n    for (; i < length; i++) {\n      if (i in self && self[i] === sought) {\n        return i;\n      }\n    }\n    return -1;\n  }\n}, hasFirefox2IndexOfBug);\n\n//\n// String\n// ======\n//\n\n// ES5 *********\n// http://es5.github.com/#x*********\n\n// [bugfix, IE lt 9, firefox 4, Konqueror, Opera, obscure browsers]\n// Many browsers do not split properly with regular expressions or they\n// do not perform the split correctly under obscure conditions.\n// See http://blog.stevenlevithan.com/archives/cross-browser-split\n// I've tested in many browsers and this seems to cover the deviant ones:\n//    'ab'.split(/(?:ab)*/) should be [\"\", \"\"], not [\"\"]\n//    '.'.split(/(.?)(.?)/) should be [\"\", \".\", \"\", \"\"], not [\"\", \"\"]\n//    'tesst'.split(/(s)*/) should be [\"t\", undefined, \"e\", \"s\", \"t\"], not\n//       [undefined, \"t\", undefined, \"e\", ...]\n//    ''.split(/.?/) should be [], not [\"\"]\n//    '.'.split(/()()/) should be [\".\"], not [\"\", \"\", \".\"]\n\nvar string_split = StringPrototype.split;\nif ('ab'.split(/(?:ab)*/).length !== 2 || '.'.split(/(.?)(.?)/).length !== 4 || 'tesst'.split(/(s)*/)[1] === 't' || 'test'.split(/(?:)/, -1).length !== 4 || ''.split(/.?/).length || '.'.split(/()()/).length > 1) {\n  (function () {\n    var compliantExecNpcg = /()??/.exec('')[1] === void 0; // NPCG: nonparticipating capturing group\n\n    StringPrototype.split = function (separator, limit) {\n      var string = this;\n      if (separator === void 0 && limit === 0) {\n        return [];\n      }\n\n      // If `separator` is not a regex, use native split\n      if (_toString.call(separator) !== '[object RegExp]') {\n        return string_split.call(this, separator, limit);\n      }\n      var output = [],\n        flags = (separator.ignoreCase ? 'i' : '') + (separator.multiline ? 'm' : '') + (separator.extended ? 'x' : '') + (\n        // Proposed for ES6\n        separator.sticky ? 'y' : ''),\n        // Firefox 3+\n        lastLastIndex = 0,\n        // Make `global` and avoid `lastIndex` issues by working with a copy\n        separator2,\n        match,\n        lastIndex,\n        lastLength;\n      separator = new RegExp(separator.source, flags + 'g');\n      string += ''; // Type-convert\n      if (!compliantExecNpcg) {\n        // Doesn't need flags gy, but they don't hurt\n        separator2 = new RegExp('^' + separator.source + '$(?!\\\\s)', flags);\n      }\n      /* Values for `limit`, per the spec:\n       * If undefined: 4294967295 // Math.pow(2, 32) - 1\n       * If 0, Infinity, or NaN: 0\n       * If positive number: limit = Math.floor(limit); if (limit > 4294967295) limit -= 4294967296;\n       * If negative number: 4294967296 - Math.floor(Math.abs(limit))\n       * If other: Type-convert, then use the above rules\n       */\n      limit = limit === void 0 ? -1 >>> 0 :\n      // Math.pow(2, 32) - 1\n      ToUint32(limit);\n      while (match = separator.exec(string)) {\n        // `separator.lastIndex` is not reliable cross-browser\n        lastIndex = match.index + match[0].length;\n        if (lastIndex > lastLastIndex) {\n          output.push(string.slice(lastLastIndex, match.index));\n          // Fix browsers whose `exec` methods don't consistently return `undefined` for\n          // nonparticipating capturing groups\n          if (!compliantExecNpcg && match.length > 1) {\n            match[0].replace(separator2, function () {\n              for (var i = 1; i < arguments.length - 2; i++) {\n                if (arguments[i] === void 0) {\n                  match[i] = void 0;\n                }\n              }\n            });\n          }\n          if (match.length > 1 && match.index < string.length) {\n            ArrayPrototype.push.apply(output, match.slice(1));\n          }\n          lastLength = match[0].length;\n          lastLastIndex = lastIndex;\n          if (output.length >= limit) {\n            break;\n          }\n        }\n        if (separator.lastIndex === match.index) {\n          separator.lastIndex++; // Avoid an infinite loop\n        }\n      }\n      if (lastLastIndex === string.length) {\n        if (lastLength || !separator.test('')) {\n          output.push('');\n        }\n      } else {\n        output.push(string.slice(lastLastIndex));\n      }\n      return output.length > limit ? output.slice(0, limit) : output;\n    };\n  })();\n\n  // [bugfix, chrome]\n  // If separator is undefined, then the result array contains just one String,\n  // which is the this value (converted to a String). If limit is not undefined,\n  // then the output array is truncated so that it contains no more than limit\n  // elements.\n  // \"0\".split(undefined, 0) -> []\n} else if ('0'.split(void 0, 0).length) {\n  StringPrototype.split = function split(separator, limit) {\n    if (separator === void 0 && limit === 0) {\n      return [];\n    }\n    return string_split.call(this, separator, limit);\n  };\n}\n\n// ECMA-262, 3rd B.2.3\n// Not an ECMAScript standard, although ECMAScript 3rd Edition has a\n// non-normative section suggesting uniform semantics and it should be\n// normalized across all browsers\n// [bugfix, IE lt 9] IE < 9 substr() with negative value not working in IE\nvar string_substr = StringPrototype.substr;\nvar hasNegativeSubstrBug = ''.substr && '0b'.substr(-1) !== 'b';\ndefineProperties(StringPrototype, {\n  substr: function substr(start, length) {\n    return string_substr.call(this, start < 0 ? (start = this.length + start) < 0 ? 0 : start : start, length);\n  }\n}, hasNegativeSubstrBug);", "map": {"version": 3, "names": ["ArrayPrototype", "Array", "prototype", "ObjectPrototype", "Object", "FunctionPrototype", "Function", "StringPrototype", "String", "array_slice", "slice", "_toString", "toString", "isFunction", "val", "call", "isArray", "obj", "isString", "supportsDescriptors", "defineProperty", "e", "object", "name", "method", "forceAssign", "configurable", "enumerable", "writable", "value", "defineProperties", "map", "hasOwnProperty", "toObject", "o", "TypeError", "toInteger", "num", "n", "Math", "floor", "abs", "ToUint32", "x", "Empty", "bind", "that", "target", "args", "arguments", "binder", "bound", "result", "apply", "concat", "<PERSON><PERSON><PERSON><PERSON>", "max", "length", "boundArgs", "i", "push", "join", "boxedString", "splitString", "properlyBoxesContext", "properlyBoxed", "properlyBoxesNonStrict", "properlyBoxesStrict", "_", "__", "context", "for<PERSON>ach", "fun", "self", "split", "thisp", "hasFirefox2IndexOfBug", "indexOf", "sought", "string_split", "compliantExecNpcg", "exec", "separator", "limit", "string", "output", "flags", "ignoreCase", "multiline", "extended", "sticky", "lastLastIndex", "separator2", "match", "lastIndex", "last<PERSON><PERSON><PERSON>", "RegExp", "source", "index", "replace", "test", "string_substr", "substr", "hasNegativeSubstrBug", "start"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/shims.js"], "sourcesContent": ["/* eslint-disable */\n/* jscs: disable */\n'use strict';\n\n// pulled specific shims from https://github.com/es-shims/es5-shim\n\nvar ArrayPrototype = Array.prototype;\nvar ObjectPrototype = Object.prototype;\nvar FunctionPrototype = Function.prototype;\nvar StringPrototype = String.prototype;\nvar array_slice = ArrayPrototype.slice;\n\nvar _toString = ObjectPrototype.toString;\nvar isFunction = function (val) {\n    return ObjectPrototype.toString.call(val) === '[object Function]';\n};\nvar isArray = function isArray(obj) {\n    return _toString.call(obj) === '[object Array]';\n};\nvar isString = function isString(obj) {\n    return _toString.call(obj) === '[object String]';\n};\n\nvar supportsDescriptors = Object.defineProperty && (function () {\n    try {\n        Object.defineProperty({}, 'x', {});\n        return true;\n    } catch (e) { /* this is ES3 */\n        return false;\n    }\n}());\n\n// Define configurable, writable and non-enumerable props\n// if they don't exist.\nvar defineProperty;\nif (supportsDescriptors) {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        Object.defineProperty(object, name, {\n            configurable: true,\n            enumerable: false,\n            writable: true,\n            value: method\n        });\n    };\n} else {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        object[name] = method;\n    };\n}\nvar defineProperties = function (object, map, forceAssign) {\n    for (var name in map) {\n        if (ObjectPrototype.hasOwnProperty.call(map, name)) {\n          defineProperty(object, name, map[name], forceAssign);\n        }\n    }\n};\n\nvar toObject = function (o) {\n    if (o == null) { // this matches both null and undefined\n        throw new TypeError(\"can't convert \" + o + ' to object');\n    }\n    return Object(o);\n};\n\n//\n// Util\n// ======\n//\n\n// ES5 9.4\n// http://es5.github.com/#x9.4\n// http://jsperf.com/to-integer\n\nfunction toInteger(num) {\n    var n = +num;\n    if (n !== n) { // isNaN\n        n = 0;\n    } else if (n !== 0 && n !== (1 / 0) && n !== -(1 / 0)) {\n        n = (n > 0 || -1) * Math.floor(Math.abs(n));\n    }\n    return n;\n}\n\nfunction ToUint32(x) {\n    return x >>> 0;\n}\n\n//\n// Function\n// ========\n//\n\n// ES-5 ********\n// http://es5.github.com/#x********\n\nfunction Empty() {}\n\ndefineProperties(FunctionPrototype, {\n    bind: function bind(that) { // .length is 1\n        // 1. Let Target be the this value.\n        var target = this;\n        // 2. If IsCallable(Target) is false, throw a TypeError exception.\n        if (!isFunction(target)) {\n            throw new TypeError('Function.prototype.bind called on incompatible ' + target);\n        }\n        // 3. Let A be a new (possibly empty) internal list of all of the\n        //   argument values provided after thisArg (arg1, arg2 etc), in order.\n        // XXX slicedArgs will stand in for \"A\" if used\n        var args = array_slice.call(arguments, 1); // for normal call\n        // 4. Let F be a new native ECMAScript object.\n        // 11. Set the [[Prototype]] internal property of F to the standard\n        //   built-in Function prototype object as specified in ********.\n        // 12. Set the [[Call]] internal property of F as described in\n        //   ********.1.\n        // 13. Set the [[Construct]] internal property of F as described in\n        //   ********.2.\n        // 14. Set the [[HasInstance]] internal property of F as described in\n        //   ********.3.\n        var binder = function () {\n\n            if (this instanceof bound) {\n                // ********.2 [[Construct]]\n                // When the [[Construct]] internal method of a function object,\n                // F that was created using the bind function is called with a\n                // list of arguments ExtraArgs, the following steps are taken:\n                // 1. Let target be the value of F's [[TargetFunction]]\n                //   internal property.\n                // 2. If target has no [[Construct]] internal method, a\n                //   TypeError exception is thrown.\n                // 3. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Construct]] internal\n                //   method of target providing args as the arguments.\n\n                var result = target.apply(\n                    this,\n                    args.concat(array_slice.call(arguments))\n                );\n                if (Object(result) === result) {\n                    return result;\n                }\n                return this;\n\n            } else {\n                // ********.1 [[Call]]\n                // When the [[Call]] internal method of a function object, F,\n                // which was created using the bind function is called with a\n                // this value and a list of arguments ExtraArgs, the following\n                // steps are taken:\n                // 1. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 2. Let boundThis be the value of F's [[BoundThis]] internal\n                //   property.\n                // 3. Let target be the value of F's [[TargetFunction]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Call]] internal method\n                //   of target providing boundThis as the this value and\n                //   providing args as the arguments.\n\n                // equiv: target.call(this, ...boundArgs, ...args)\n                return target.apply(\n                    that,\n                    args.concat(array_slice.call(arguments))\n                );\n\n            }\n\n        };\n\n        // 15. If the [[Class]] internal property of Target is \"Function\", then\n        //     a. Let L be the length property of Target minus the length of A.\n        //     b. Set the length own property of F to either 0 or L, whichever is\n        //       larger.\n        // 16. Else set the length own property of F to 0.\n\n        var boundLength = Math.max(0, target.length - args.length);\n\n        // 17. Set the attributes of the length own property of F to the values\n        //   specified in 15.3.5.1.\n        var boundArgs = [];\n        for (var i = 0; i < boundLength; i++) {\n            boundArgs.push('$' + i);\n        }\n\n        // XXX Build a dynamic function with desired amount of arguments is the only\n        // way to set the length property of a function.\n        // In environments where Content Security Policies enabled (Chrome extensions,\n        // for ex.) all use of eval or Function costructor throws an exception.\n        // However in all of these environments Function.prototype.bind exists\n        // and so this code will never be executed.\n        var bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this, arguments); }')(binder);\n\n        if (target.prototype) {\n            Empty.prototype = target.prototype;\n            bound.prototype = new Empty();\n            // Clean up dangling references.\n            Empty.prototype = null;\n        }\n\n        // TODO\n        // 18. Set the [[Extensible]] internal property of F to true.\n\n        // TODO\n        // 19. Let thrower be the [[ThrowTypeError]] function Object (13.2.3).\n        // 20. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"caller\", PropertyDescriptor {[[Get]]: thrower, [[Set]]:\n        //   thrower, [[Enumerable]]: false, [[Configurable]]: false}, and\n        //   false.\n        // 21. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"arguments\", PropertyDescriptor {[[Get]]: thrower,\n        //   [[Set]]: thrower, [[Enumerable]]: false, [[Configurable]]: false},\n        //   and false.\n\n        // TODO\n        // NOTE Function objects created using Function.prototype.bind do not\n        // have a prototype property or the [[Code]], [[FormalParameters]], and\n        // [[Scope]] internal properties.\n        // XXX can't delete prototype in pure-js.\n\n        // 22. Return F.\n        return bound;\n    }\n});\n\n//\n// Array\n// =====\n//\n\n// ES5 ********\n// http://es5.github.com/#x********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/isArray\ndefineProperties(Array, { isArray: isArray });\n\n\nvar boxedString = Object('a');\nvar splitString = boxedString[0] !== 'a' || !(0 in boxedString);\n\nvar properlyBoxesContext = function properlyBoxed(method) {\n    // Check node 0.6.21 bug where third parameter is not boxed\n    var properlyBoxesNonStrict = true;\n    var properlyBoxesStrict = true;\n    if (method) {\n        method.call('foo', function (_, __, context) {\n            if (typeof context !== 'object') { properlyBoxesNonStrict = false; }\n        });\n\n        method.call([1], function () {\n            'use strict';\n            properlyBoxesStrict = typeof this === 'string';\n        }, 'x');\n    }\n    return !!method && properlyBoxesNonStrict && properlyBoxesStrict;\n};\n\ndefineProperties(ArrayPrototype, {\n    forEach: function forEach(fun /*, thisp*/) {\n        var object = toObject(this),\n            self = splitString && isString(this) ? this.split('') : object,\n            thisp = arguments[1],\n            i = -1,\n            length = self.length >>> 0;\n\n        // If no callback function or if callback is not a callable function\n        if (!isFunction(fun)) {\n            throw new TypeError(); // TODO message\n        }\n\n        while (++i < length) {\n            if (i in self) {\n                // Invoke the callback function with call, passing arguments:\n                // context, property value, property key, thisArg object\n                // context\n                fun.call(thisp, self[i], i, object);\n            }\n        }\n    }\n}, !properlyBoxesContext(ArrayPrototype.forEach));\n\n// ES5 *********\n// http://es5.github.com/#x*********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/indexOf\nvar hasFirefox2IndexOfBug = Array.prototype.indexOf && [0, 1].indexOf(1, 2) !== -1;\ndefineProperties(ArrayPrototype, {\n    indexOf: function indexOf(sought /*, fromIndex */ ) {\n        var self = splitString && isString(this) ? this.split('') : toObject(this),\n            length = self.length >>> 0;\n\n        if (!length) {\n            return -1;\n        }\n\n        var i = 0;\n        if (arguments.length > 1) {\n            i = toInteger(arguments[1]);\n        }\n\n        // handle negative indices\n        i = i >= 0 ? i : Math.max(0, length + i);\n        for (; i < length; i++) {\n            if (i in self && self[i] === sought) {\n                return i;\n            }\n        }\n        return -1;\n    }\n}, hasFirefox2IndexOfBug);\n\n//\n// String\n// ======\n//\n\n// ES5 *********\n// http://es5.github.com/#x*********\n\n// [bugfix, IE lt 9, firefox 4, Konqueror, Opera, obscure browsers]\n// Many browsers do not split properly with regular expressions or they\n// do not perform the split correctly under obscure conditions.\n// See http://blog.stevenlevithan.com/archives/cross-browser-split\n// I've tested in many browsers and this seems to cover the deviant ones:\n//    'ab'.split(/(?:ab)*/) should be [\"\", \"\"], not [\"\"]\n//    '.'.split(/(.?)(.?)/) should be [\"\", \".\", \"\", \"\"], not [\"\", \"\"]\n//    'tesst'.split(/(s)*/) should be [\"t\", undefined, \"e\", \"s\", \"t\"], not\n//       [undefined, \"t\", undefined, \"e\", ...]\n//    ''.split(/.?/) should be [], not [\"\"]\n//    '.'.split(/()()/) should be [\".\"], not [\"\", \"\", \".\"]\n\nvar string_split = StringPrototype.split;\nif (\n    'ab'.split(/(?:ab)*/).length !== 2 ||\n    '.'.split(/(.?)(.?)/).length !== 4 ||\n    'tesst'.split(/(s)*/)[1] === 't' ||\n    'test'.split(/(?:)/, -1).length !== 4 ||\n    ''.split(/.?/).length ||\n    '.'.split(/()()/).length > 1\n) {\n    (function () {\n        var compliantExecNpcg = /()??/.exec('')[1] === void 0; // NPCG: nonparticipating capturing group\n\n        StringPrototype.split = function (separator, limit) {\n            var string = this;\n            if (separator === void 0 && limit === 0) {\n                return [];\n            }\n\n            // If `separator` is not a regex, use native split\n            if (_toString.call(separator) !== '[object RegExp]') {\n                return string_split.call(this, separator, limit);\n            }\n\n            var output = [],\n                flags = (separator.ignoreCase ? 'i' : '') +\n                        (separator.multiline  ? 'm' : '') +\n                        (separator.extended   ? 'x' : '') + // Proposed for ES6\n                        (separator.sticky     ? 'y' : ''), // Firefox 3+\n                lastLastIndex = 0,\n                // Make `global` and avoid `lastIndex` issues by working with a copy\n                separator2, match, lastIndex, lastLength;\n            separator = new RegExp(separator.source, flags + 'g');\n            string += ''; // Type-convert\n            if (!compliantExecNpcg) {\n                // Doesn't need flags gy, but they don't hurt\n                separator2 = new RegExp('^' + separator.source + '$(?!\\\\s)', flags);\n            }\n            /* Values for `limit`, per the spec:\n             * If undefined: 4294967295 // Math.pow(2, 32) - 1\n             * If 0, Infinity, or NaN: 0\n             * If positive number: limit = Math.floor(limit); if (limit > 4294967295) limit -= 4294967296;\n             * If negative number: 4294967296 - Math.floor(Math.abs(limit))\n             * If other: Type-convert, then use the above rules\n             */\n            limit = limit === void 0 ?\n                -1 >>> 0 : // Math.pow(2, 32) - 1\n                ToUint32(limit);\n            while (match = separator.exec(string)) {\n                // `separator.lastIndex` is not reliable cross-browser\n                lastIndex = match.index + match[0].length;\n                if (lastIndex > lastLastIndex) {\n                    output.push(string.slice(lastLastIndex, match.index));\n                    // Fix browsers whose `exec` methods don't consistently return `undefined` for\n                    // nonparticipating capturing groups\n                    if (!compliantExecNpcg && match.length > 1) {\n                        match[0].replace(separator2, function () {\n                            for (var i = 1; i < arguments.length - 2; i++) {\n                                if (arguments[i] === void 0) {\n                                    match[i] = void 0;\n                                }\n                            }\n                        });\n                    }\n                    if (match.length > 1 && match.index < string.length) {\n                        ArrayPrototype.push.apply(output, match.slice(1));\n                    }\n                    lastLength = match[0].length;\n                    lastLastIndex = lastIndex;\n                    if (output.length >= limit) {\n                        break;\n                    }\n                }\n                if (separator.lastIndex === match.index) {\n                    separator.lastIndex++; // Avoid an infinite loop\n                }\n            }\n            if (lastLastIndex === string.length) {\n                if (lastLength || !separator.test('')) {\n                    output.push('');\n                }\n            } else {\n                output.push(string.slice(lastLastIndex));\n            }\n            return output.length > limit ? output.slice(0, limit) : output;\n        };\n    }());\n\n// [bugfix, chrome]\n// If separator is undefined, then the result array contains just one String,\n// which is the this value (converted to a String). If limit is not undefined,\n// then the output array is truncated so that it contains no more than limit\n// elements.\n// \"0\".split(undefined, 0) -> []\n} else if ('0'.split(void 0, 0).length) {\n    StringPrototype.split = function split(separator, limit) {\n        if (separator === void 0 && limit === 0) { return []; }\n        return string_split.call(this, separator, limit);\n    };\n}\n\n// ECMA-262, 3rd B.2.3\n// Not an ECMAScript standard, although ECMAScript 3rd Edition has a\n// non-normative section suggesting uniform semantics and it should be\n// normalized across all browsers\n// [bugfix, IE lt 9] IE < 9 substr() with negative value not working in IE\nvar string_substr = StringPrototype.substr;\nvar hasNegativeSubstrBug = ''.substr && '0b'.substr(-1) !== 'b';\ndefineProperties(StringPrototype, {\n    substr: function substr(start, length) {\n        return string_substr.call(\n            this,\n            start < 0 ? ((start = this.length + start) < 0 ? 0 : start) : start,\n            length\n        );\n    }\n}, hasNegativeSubstrBug);\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAEZ;AAEA,IAAIA,cAAc,GAAGC,KAAK,CAACC,SAAS;AACpC,IAAIC,eAAe,GAAGC,MAAM,CAACF,SAAS;AACtC,IAAIG,iBAAiB,GAAGC,QAAQ,CAACJ,SAAS;AAC1C,IAAIK,eAAe,GAAGC,MAAM,CAACN,SAAS;AACtC,IAAIO,WAAW,GAAGT,cAAc,CAACU,KAAK;AAEtC,IAAIC,SAAS,GAAGR,eAAe,CAACS,QAAQ;AACxC,IAAIC,UAAU,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAC5B,OAAOX,eAAe,CAACS,QAAQ,CAACG,IAAI,CAACD,GAAG,CAAC,KAAK,mBAAmB;AACrE,CAAC;AACD,IAAIE,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;EAChC,OAAON,SAAS,CAACI,IAAI,CAACE,GAAG,CAAC,KAAK,gBAAgB;AACnD,CAAC;AACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACD,GAAG,EAAE;EAClC,OAAON,SAAS,CAACI,IAAI,CAACE,GAAG,CAAC,KAAK,iBAAiB;AACpD,CAAC;AAED,IAAIE,mBAAmB,GAAGf,MAAM,CAACgB,cAAc,IAAK,YAAY;EAC5D,IAAI;IACAhB,MAAM,CAACgB,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACf,CAAC,CAAC,OAAOC,CAAC,EAAE;IAAE;IACV,OAAO,KAAK;EAChB;AACJ,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA,IAAID,cAAc;AAClB,IAAID,mBAAmB,EAAE;EACrBC,cAAc,GAAG,SAAAA,CAAUE,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAE;IAC1D,IAAI,CAACA,WAAW,IAAKF,IAAI,IAAID,MAAO,EAAE;MAAE;IAAQ;IAChDlB,MAAM,CAACgB,cAAc,CAACE,MAAM,EAAEC,IAAI,EAAE;MAChCG,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAEL;IACX,CAAC,CAAC;EACN,CAAC;AACL,CAAC,MAAM;EACHJ,cAAc,GAAG,SAAAA,CAAUE,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAE;IAC1D,IAAI,CAACA,WAAW,IAAKF,IAAI,IAAID,MAAO,EAAE;MAAE;IAAQ;IAChDA,MAAM,CAACC,IAAI,CAAC,GAAGC,MAAM;EACzB,CAAC;AACL;AACA,IAAIM,gBAAgB,GAAG,SAAAA,CAAUR,MAAM,EAAES,GAAG,EAAEN,WAAW,EAAE;EACvD,KAAK,IAAIF,IAAI,IAAIQ,GAAG,EAAE;IAClB,IAAI5B,eAAe,CAAC6B,cAAc,CAACjB,IAAI,CAACgB,GAAG,EAAER,IAAI,CAAC,EAAE;MAClDH,cAAc,CAACE,MAAM,EAAEC,IAAI,EAAEQ,GAAG,CAACR,IAAI,CAAC,EAAEE,WAAW,CAAC;IACtD;EACJ;AACJ,CAAC;AAED,IAAIQ,QAAQ,GAAG,SAAAA,CAAUC,CAAC,EAAE;EACxB,IAAIA,CAAC,IAAI,IAAI,EAAE;IAAE;IACb,MAAM,IAAIC,SAAS,CAAC,gBAAgB,GAAGD,CAAC,GAAG,YAAY,CAAC;EAC5D;EACA,OAAO9B,MAAM,CAAC8B,CAAC,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASE,SAASA,CAACC,GAAG,EAAE;EACpB,IAAIC,CAAC,GAAG,CAACD,GAAG;EACZ,IAAIC,CAAC,KAAKA,CAAC,EAAE;IAAE;IACXA,CAAC,GAAG,CAAC;EACT,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAM,CAAC,GAAG,CAAE,IAAIA,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;IACnDA,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACH,CAAC,CAAC,CAAC;EAC/C;EACA,OAAOA,CAAC;AACZ;AAEA,SAASI,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAOA,CAAC,KAAK,CAAC;AAClB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,SAASC,KAAKA,CAAA,EAAG,CAAC;AAElBd,gBAAgB,CAACzB,iBAAiB,EAAE;EAChCwC,IAAI,EAAE,SAASA,IAAIA,CAACC,IAAI,EAAE;IAAE;IACxB;IACA,IAAIC,MAAM,GAAG,IAAI;IACjB;IACA,IAAI,CAAClC,UAAU,CAACkC,MAAM,CAAC,EAAE;MACrB,MAAM,IAAIZ,SAAS,CAAC,iDAAiD,GAAGY,MAAM,CAAC;IACnF;IACA;IACA;IACA;IACA,IAAIC,IAAI,GAAGvC,WAAW,CAACM,IAAI,CAACkC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,MAAM,GAAG,SAAAA,CAAA,EAAY;MAErB,IAAI,IAAI,YAAYC,KAAK,EAAE;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,IAAIC,MAAM,GAAGL,MAAM,CAACM,KAAK,CACrB,IAAI,EACJL,IAAI,CAACM,MAAM,CAAC7C,WAAW,CAACM,IAAI,CAACkC,SAAS,CAAC,CAC3C,CAAC;QACD,IAAI7C,MAAM,CAACgD,MAAM,CAAC,KAAKA,MAAM,EAAE;UAC3B,OAAOA,MAAM;QACjB;QACA,OAAO,IAAI;MAEf,CAAC,MAAM;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA,OAAOL,MAAM,CAACM,KAAK,CACfP,IAAI,EACJE,IAAI,CAACM,MAAM,CAAC7C,WAAW,CAACM,IAAI,CAACkC,SAAS,CAAC,CAC3C,CAAC;MAEL;IAEJ,CAAC;;IAED;IACA;IACA;IACA;IACA;;IAEA,IAAIM,WAAW,GAAGhB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAET,MAAM,CAACU,MAAM,GAAGT,IAAI,CAACS,MAAM,CAAC;;IAE1D;IACA;IACA,IAAIC,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAEI,CAAC,EAAE,EAAE;MAClCD,SAAS,CAACE,IAAI,CAAC,GAAG,GAAGD,CAAC,CAAC;IAC3B;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIR,KAAK,GAAG7C,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,GAAGoD,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,4CAA4C,CAAC,CAACX,MAAM,CAAC;IAEhI,IAAIH,MAAM,CAAC7C,SAAS,EAAE;MAClB0C,KAAK,CAAC1C,SAAS,GAAG6C,MAAM,CAAC7C,SAAS;MAClCiD,KAAK,CAACjD,SAAS,GAAG,IAAI0C,KAAK,CAAC,CAAC;MAC7B;MACAA,KAAK,CAAC1C,SAAS,GAAG,IAAI;IAC1B;;IAEA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA,OAAOiD,KAAK;EAChB;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;;AAEA;AACA;AACA;AACArB,gBAAgB,CAAC7B,KAAK,EAAE;EAAEe,OAAO,EAAEA;AAAQ,CAAC,CAAC;AAG7C,IAAI8C,WAAW,GAAG1D,MAAM,CAAC,GAAG,CAAC;AAC7B,IAAI2D,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,IAAIA,WAAW,CAAC;AAE/D,IAAIE,oBAAoB,GAAG,SAASC,aAAaA,CAACzC,MAAM,EAAE;EACtD;EACA,IAAI0C,sBAAsB,GAAG,IAAI;EACjC,IAAIC,mBAAmB,GAAG,IAAI;EAC9B,IAAI3C,MAAM,EAAE;IACRA,MAAM,CAACT,IAAI,CAAC,KAAK,EAAE,UAAUqD,CAAC,EAAEC,EAAE,EAAEC,OAAO,EAAE;MACzC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAAEJ,sBAAsB,GAAG,KAAK;MAAE;IACvE,CAAC,CAAC;IAEF1C,MAAM,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY;MACzB,YAAY;;MACZoD,mBAAmB,GAAG,OAAO,IAAI,KAAK,QAAQ;IAClD,CAAC,EAAE,GAAG,CAAC;EACX;EACA,OAAO,CAAC,CAAC3C,MAAM,IAAI0C,sBAAsB,IAAIC,mBAAmB;AACpE,CAAC;AAEDrC,gBAAgB,CAAC9B,cAAc,EAAE;EAC7BuE,OAAO,EAAE,SAASA,OAAOA,CAACC,GAAG,CAAC,aAAa;IACvC,IAAIlD,MAAM,GAAGW,QAAQ,CAAC,IAAI,CAAC;MACvBwC,IAAI,GAAGV,WAAW,IAAI7C,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAACwD,KAAK,CAAC,EAAE,CAAC,GAAGpD,MAAM;MAC9DqD,KAAK,GAAG1B,SAAS,CAAC,CAAC,CAAC;MACpBU,CAAC,GAAG,CAAC,CAAC;MACNF,MAAM,GAAGgB,IAAI,CAAChB,MAAM,KAAK,CAAC;;IAE9B;IACA,IAAI,CAAC5C,UAAU,CAAC2D,GAAG,CAAC,EAAE;MAClB,MAAM,IAAIrC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3B;IAEA,OAAO,EAAEwB,CAAC,GAAGF,MAAM,EAAE;MACjB,IAAIE,CAAC,IAAIc,IAAI,EAAE;QACX;QACA;QACA;QACAD,GAAG,CAACzD,IAAI,CAAC4D,KAAK,EAAEF,IAAI,CAACd,CAAC,CAAC,EAAEA,CAAC,EAAErC,MAAM,CAAC;MACvC;IACJ;EACJ;AACJ,CAAC,EAAE,CAAC0C,oBAAoB,CAAChE,cAAc,CAACuE,OAAO,CAAC,CAAC;;AAEjD;AACA;AACA;AACA,IAAIK,qBAAqB,GAAG3E,KAAK,CAACC,SAAS,CAAC2E,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAClF/C,gBAAgB,CAAC9B,cAAc,EAAE;EAC7B6E,OAAO,EAAE,SAASA,OAAOA,CAACC,MAAM,CAAC,kBAAmB;IAChD,IAAIL,IAAI,GAAGV,WAAW,IAAI7C,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAACwD,KAAK,CAAC,EAAE,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;MACtEwB,MAAM,GAAGgB,IAAI,CAAChB,MAAM,KAAK,CAAC;IAE9B,IAAI,CAACA,MAAM,EAAE;MACT,OAAO,CAAC,CAAC;IACb;IAEA,IAAIE,CAAC,GAAG,CAAC;IACT,IAAIV,SAAS,CAACQ,MAAM,GAAG,CAAC,EAAE;MACtBE,CAAC,GAAGvB,SAAS,CAACa,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B;;IAEA;IACAU,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAGpB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAGE,CAAC,CAAC;IACxC,OAAOA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;MACpB,IAAIA,CAAC,IAAIc,IAAI,IAAIA,IAAI,CAACd,CAAC,CAAC,KAAKmB,MAAM,EAAE;QACjC,OAAOnB,CAAC;MACZ;IACJ;IACA,OAAO,CAAC,CAAC;EACb;AACJ,CAAC,EAAEiB,qBAAqB,CAAC;;AAEzB;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIG,YAAY,GAAGxE,eAAe,CAACmE,KAAK;AACxC,IACI,IAAI,CAACA,KAAK,CAAC,SAAS,CAAC,CAACjB,MAAM,KAAK,CAAC,IAClC,GAAG,CAACiB,KAAK,CAAC,UAAU,CAAC,CAACjB,MAAM,KAAK,CAAC,IAClC,OAAO,CAACiB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAChC,MAAM,CAACA,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAACjB,MAAM,KAAK,CAAC,IACrC,EAAE,CAACiB,KAAK,CAAC,IAAI,CAAC,CAACjB,MAAM,IACrB,GAAG,CAACiB,KAAK,CAAC,MAAM,CAAC,CAACjB,MAAM,GAAG,CAAC,EAC9B;EACG,aAAY;IACT,IAAIuB,iBAAiB,GAAG,MAAM,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;;IAEvD1E,eAAe,CAACmE,KAAK,GAAG,UAAUQ,SAAS,EAAEC,KAAK,EAAE;MAChD,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIF,SAAS,KAAK,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,EAAE;QACrC,OAAO,EAAE;MACb;;MAEA;MACA,IAAIxE,SAAS,CAACI,IAAI,CAACmE,SAAS,CAAC,KAAK,iBAAiB,EAAE;QACjD,OAAOH,YAAY,CAAChE,IAAI,CAAC,IAAI,EAAEmE,SAAS,EAAEC,KAAK,CAAC;MACpD;MAEA,IAAIE,MAAM,GAAG,EAAE;QACXC,KAAK,GAAG,CAACJ,SAAS,CAACK,UAAU,GAAG,GAAG,GAAG,EAAE,KAC/BL,SAAS,CAACM,SAAS,GAAI,GAAG,GAAG,EAAE,CAAC,IAChCN,SAAS,CAACO,QAAQ,GAAK,GAAG,GAAG,EAAE,CAAC;QAAG;QACnCP,SAAS,CAACQ,MAAM,GAAO,GAAG,GAAG,EAAE,CAAC;QAAE;QAC3CC,aAAa,GAAG,CAAC;QACjB;QACAC,UAAU;QAAEC,KAAK;QAAEC,SAAS;QAAEC,UAAU;MAC5Cb,SAAS,GAAG,IAAIc,MAAM,CAACd,SAAS,CAACe,MAAM,EAAEX,KAAK,GAAG,GAAG,CAAC;MACrDF,MAAM,IAAI,EAAE,CAAC,CAAC;MACd,IAAI,CAACJ,iBAAiB,EAAE;QACpB;QACAY,UAAU,GAAG,IAAII,MAAM,CAAC,GAAG,GAAGd,SAAS,CAACe,MAAM,GAAG,UAAU,EAAEX,KAAK,CAAC;MACvE;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACYH,KAAK,GAAGA,KAAK,KAAK,KAAK,CAAC,GACpB,CAAC,CAAC,KAAK,CAAC;MAAG;MACXzC,QAAQ,CAACyC,KAAK,CAAC;MACnB,OAAOU,KAAK,GAAGX,SAAS,CAACD,IAAI,CAACG,MAAM,CAAC,EAAE;QACnC;QACAU,SAAS,GAAGD,KAAK,CAACK,KAAK,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACpC,MAAM;QACzC,IAAIqC,SAAS,GAAGH,aAAa,EAAE;UAC3BN,MAAM,CAACzB,IAAI,CAACwB,MAAM,CAAC1E,KAAK,CAACiF,aAAa,EAAEE,KAAK,CAACK,KAAK,CAAC,CAAC;UACrD;UACA;UACA,IAAI,CAAClB,iBAAiB,IAAIa,KAAK,CAACpC,MAAM,GAAG,CAAC,EAAE;YACxCoC,KAAK,CAAC,CAAC,CAAC,CAACM,OAAO,CAACP,UAAU,EAAE,YAAY;cACrC,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,SAAS,CAACQ,MAAM,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;gBAC3C,IAAIV,SAAS,CAACU,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE;kBACzBkC,KAAK,CAAClC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACrB;cACJ;YACJ,CAAC,CAAC;UACN;UACA,IAAIkC,KAAK,CAACpC,MAAM,GAAG,CAAC,IAAIoC,KAAK,CAACK,KAAK,GAAGd,MAAM,CAAC3B,MAAM,EAAE;YACjDzD,cAAc,CAAC4D,IAAI,CAACP,KAAK,CAACgC,MAAM,EAAEQ,KAAK,CAACnF,KAAK,CAAC,CAAC,CAAC,CAAC;UACrD;UACAqF,UAAU,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACpC,MAAM;UAC5BkC,aAAa,GAAGG,SAAS;UACzB,IAAIT,MAAM,CAAC5B,MAAM,IAAI0B,KAAK,EAAE;YACxB;UACJ;QACJ;QACA,IAAID,SAAS,CAACY,SAAS,KAAKD,KAAK,CAACK,KAAK,EAAE;UACrChB,SAAS,CAACY,SAAS,EAAE,CAAC,CAAC;QAC3B;MACJ;MACA,IAAIH,aAAa,KAAKP,MAAM,CAAC3B,MAAM,EAAE;QACjC,IAAIsC,UAAU,IAAI,CAACb,SAAS,CAACkB,IAAI,CAAC,EAAE,CAAC,EAAE;UACnCf,MAAM,CAACzB,IAAI,CAAC,EAAE,CAAC;QACnB;MACJ,CAAC,MAAM;QACHyB,MAAM,CAACzB,IAAI,CAACwB,MAAM,CAAC1E,KAAK,CAACiF,aAAa,CAAC,CAAC;MAC5C;MACA,OAAON,MAAM,CAAC5B,MAAM,GAAG0B,KAAK,GAAGE,MAAM,CAAC3E,KAAK,CAAC,CAAC,EAAEyE,KAAK,CAAC,GAAGE,MAAM;IAClE,CAAC;EACL,CAAC,EAAC,CAAC;;EAEP;EACA;EACA;EACA;EACA;EACA;AACA,CAAC,MAAM,IAAI,GAAG,CAACX,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACjB,MAAM,EAAE;EACpClD,eAAe,CAACmE,KAAK,GAAG,SAASA,KAAKA,CAACQ,SAAS,EAAEC,KAAK,EAAE;IACrD,IAAID,SAAS,KAAK,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,EAAE;MAAE,OAAO,EAAE;IAAE;IACtD,OAAOJ,YAAY,CAAChE,IAAI,CAAC,IAAI,EAAEmE,SAAS,EAAEC,KAAK,CAAC;EACpD,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIkB,aAAa,GAAG9F,eAAe,CAAC+F,MAAM;AAC1C,IAAIC,oBAAoB,GAAG,EAAE,CAACD,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;AAC/DxE,gBAAgB,CAACvB,eAAe,EAAE;EAC9B+F,MAAM,EAAE,SAASA,MAAMA,CAACE,KAAK,EAAE/C,MAAM,EAAE;IACnC,OAAO4C,aAAa,CAACtF,IAAI,CACrB,IAAI,EACJyF,KAAK,GAAG,CAAC,GAAI,CAACA,KAAK,GAAG,IAAI,CAAC/C,MAAM,GAAG+C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAIA,KAAK,EACnE/C,MACJ,CAAC;EACL;AACJ,CAAC,EAAE8C,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}