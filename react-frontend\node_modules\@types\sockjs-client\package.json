{"name": "@types/sockjs-client", "version": "1.5.4", "description": "TypeScript definitions for sockjs-client", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sockjs-client", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "v<PERSON>v", "url": "https://github.com/vladev"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/arus<PERSON>ov"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON><PERSON>", "githubUsername": "renjfk", "url": "https://github.com/renjfk"}, {"name": "<PERSON>", "githubUsername": "PutilovAI", "url": "https://github.com/PutilovAI"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sockjs-client"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "94b4ca3cce5957e776507050587589a347711fcb38bd2e75b0660f2175318b4d", "typeScriptVersion": "4.5"}