{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  XhrDriver = require('../driver/xhr');\nfunction XHRLocalObject(method, url, payload /*, opts */) {\n  XhrDriver.call(this, method, url, payload, {\n    noCredentials: true\n  });\n}\ninherits(XHRLocalObject, XhrDriver);\nXHRLocalObject.enabled = XhrDriver.enabled;\nmodule.exports = XHRLocalObject;", "map": {"version": 3, "names": ["inherits", "require", "XhrDriver", "XHRLocalObject", "method", "url", "payload", "call", "noCredentials", "enabled", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/transport/sender/xhr-local.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRLocalObject(method, url, payload /*, opts */) {\n  XhrDriver.call(this, method, url, payload, {\n    noCredentials: true\n  });\n}\n\ninherits(XHRLocalObject, XhrDriver);\n\nXHRLocalObject.enabled = XhrDriver.enabled;\n\nmodule.exports = XHRLocalObject;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAGxC,SAASE,cAAcA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC,aAAa;EACxDJ,SAAS,CAACK,IAAI,CAAC,IAAI,EAAEH,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACzCE,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ;AAEAR,QAAQ,CAACG,cAAc,EAAED,SAAS,CAAC;AAEnCC,cAAc,CAACM,OAAO,GAAGP,SAAS,CAACO,OAAO;AAE1CC,MAAM,CAACC,OAAO,GAAGR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}