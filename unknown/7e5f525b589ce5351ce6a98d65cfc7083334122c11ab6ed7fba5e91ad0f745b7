{"ast": null, "code": "\"use strict\";\n\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\nfunction setup(env) {\n  createDebug.debug = createDebug;\n  createDebug.default = createDebug;\n  createDebug.coerce = coerce;\n  createDebug.disable = disable;\n  createDebug.enable = enable;\n  createDebug.enabled = enabled;\n  createDebug.humanize = require('ms');\n  Object.keys(env).forEach(function (key) {\n    createDebug[key] = env[key];\n  });\n  /**\n  * Active `debug` instances.\n  */\n\n  createDebug.instances = [];\n  /**\n  * The currently active debug mode names, and names to skip.\n  */\n\n  createDebug.names = [];\n  createDebug.skips = [];\n  /**\n  * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n  *\n  * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n  */\n\n  createDebug.formatters = {};\n  /**\n  * Selects a color for a debug namespace\n  * @param {String} namespace The namespace string for the for the debug instance to be colored\n  * @return {Number|String} An ANSI color code for the given namespace\n  * @api private\n  */\n\n  function selectColor(namespace) {\n    var hash = 0;\n    for (var i = 0; i < namespace.length; i++) {\n      hash = (hash << 5) - hash + namespace.charCodeAt(i);\n      hash |= 0; // Convert to 32bit integer\n    }\n    return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n  }\n  createDebug.selectColor = selectColor;\n  /**\n  * Create a debugger with the given `namespace`.\n  *\n  * @param {String} namespace\n  * @return {Function}\n  * @api public\n  */\n\n  function createDebug(namespace) {\n    var prevTime;\n    function debug() {\n      // Disabled?\n      if (!debug.enabled) {\n        return;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var self = debug; // Set `diff` timestamp\n\n      var curr = Number(new Date());\n      var ms = curr - (prevTime || curr);\n      self.diff = ms;\n      self.prev = prevTime;\n      self.curr = curr;\n      prevTime = curr;\n      args[0] = createDebug.coerce(args[0]);\n      if (typeof args[0] !== 'string') {\n        // Anything else let's inspect with %O\n        args.unshift('%O');\n      } // Apply any `formatters` transformations\n\n      var index = 0;\n      args[0] = args[0].replace(/%([a-zA-Z%])/g, function (match, format) {\n        // If we encounter an escaped % then don't increase the array index\n        if (match === '%%') {\n          return match;\n        }\n        index++;\n        var formatter = createDebug.formatters[format];\n        if (typeof formatter === 'function') {\n          var val = args[index];\n          match = formatter.call(self, val); // Now we need to remove `args[index]` since it's inlined in the `format`\n\n          args.splice(index, 1);\n          index--;\n        }\n        return match;\n      }); // Apply env-specific formatting (colors, etc.)\n\n      createDebug.formatArgs.call(self, args);\n      var logFn = self.log || createDebug.log;\n      logFn.apply(self, args);\n    }\n    debug.namespace = namespace;\n    debug.enabled = createDebug.enabled(namespace);\n    debug.useColors = createDebug.useColors();\n    debug.color = selectColor(namespace);\n    debug.destroy = destroy;\n    debug.extend = extend; // Debug.formatArgs = formatArgs;\n    // debug.rawLog = rawLog;\n    // env-specific initialization logic for debug instances\n\n    if (typeof createDebug.init === 'function') {\n      createDebug.init(debug);\n    }\n    createDebug.instances.push(debug);\n    return debug;\n  }\n  function destroy() {\n    var index = createDebug.instances.indexOf(this);\n    if (index !== -1) {\n      createDebug.instances.splice(index, 1);\n      return true;\n    }\n    return false;\n  }\n  function extend(namespace, delimiter) {\n    return createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n  }\n  /**\n  * Enables a debug mode by namespaces. This can include modes\n  * separated by a colon and wildcards.\n  *\n  * @param {String} namespaces\n  * @api public\n  */\n\n  function enable(namespaces) {\n    createDebug.save(namespaces);\n    createDebug.names = [];\n    createDebug.skips = [];\n    var i;\n    var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n    var len = split.length;\n    for (i = 0; i < len; i++) {\n      if (!split[i]) {\n        // ignore empty strings\n        continue;\n      }\n      namespaces = split[i].replace(/\\*/g, '.*?');\n      if (namespaces[0] === '-') {\n        createDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n      } else {\n        createDebug.names.push(new RegExp('^' + namespaces + '$'));\n      }\n    }\n    for (i = 0; i < createDebug.instances.length; i++) {\n      var instance = createDebug.instances[i];\n      instance.enabled = createDebug.enabled(instance.namespace);\n    }\n  }\n  /**\n  * Disable debug output.\n  *\n  * @api public\n  */\n\n  function disable() {\n    createDebug.enable('');\n  }\n  /**\n  * Returns true if the given mode name is enabled, false otherwise.\n  *\n  * @param {String} name\n  * @return {Boolean}\n  * @api public\n  */\n\n  function enabled(name) {\n    if (name[name.length - 1] === '*') {\n      return true;\n    }\n    var i;\n    var len;\n    for (i = 0, len = createDebug.skips.length; i < len; i++) {\n      if (createDebug.skips[i].test(name)) {\n        return false;\n      }\n    }\n    for (i = 0, len = createDebug.names.length; i < len; i++) {\n      if (createDebug.names[i].test(name)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n  * Coerce `val`.\n  *\n  * @param {Mixed} val\n  * @return {Mixed}\n  * @api private\n  */\n\n  function coerce(val) {\n    if (val instanceof Error) {\n      return val.stack || val.message;\n    }\n    return val;\n  }\n  createDebug.enable(createDebug.load());\n  return createDebug;\n}\nmodule.exports = setup;", "map": {"version": 3, "names": ["setup", "env", "createDebug", "debug", "default", "coerce", "disable", "enable", "enabled", "humanize", "require", "Object", "keys", "for<PERSON>ach", "key", "instances", "names", "skips", "formatters", "selectColor", "namespace", "hash", "i", "length", "charCodeAt", "colors", "Math", "abs", "prevTime", "_len", "arguments", "args", "Array", "_key", "self", "curr", "Number", "Date", "ms", "diff", "prev", "unshift", "index", "replace", "match", "format", "formatter", "val", "call", "splice", "formatArgs", "logFn", "log", "apply", "useColors", "color", "destroy", "extend", "init", "push", "indexOf", "delimiter", "namespaces", "save", "split", "len", "RegExp", "substr", "instance", "name", "test", "Error", "stack", "message", "load", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/node_modules/debug/src/common.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\nfunction setup(env) {\n  createDebug.debug = createDebug;\n  createDebug.default = createDebug;\n  createDebug.coerce = coerce;\n  createDebug.disable = disable;\n  createDebug.enable = enable;\n  createDebug.enabled = enabled;\n  createDebug.humanize = require('ms');\n  Object.keys(env).forEach(function (key) {\n    createDebug[key] = env[key];\n  });\n  /**\n  * Active `debug` instances.\n  */\n\n  createDebug.instances = [];\n  /**\n  * The currently active debug mode names, and names to skip.\n  */\n\n  createDebug.names = [];\n  createDebug.skips = [];\n  /**\n  * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n  *\n  * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n  */\n\n  createDebug.formatters = {};\n  /**\n  * Selects a color for a debug namespace\n  * @param {String} namespace The namespace string for the for the debug instance to be colored\n  * @return {Number|String} An ANSI color code for the given namespace\n  * @api private\n  */\n\n  function selectColor(namespace) {\n    var hash = 0;\n\n    for (var i = 0; i < namespace.length; i++) {\n      hash = (hash << 5) - hash + namespace.charCodeAt(i);\n      hash |= 0; // Convert to 32bit integer\n    }\n\n    return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n  }\n\n  createDebug.selectColor = selectColor;\n  /**\n  * Create a debugger with the given `namespace`.\n  *\n  * @param {String} namespace\n  * @return {Function}\n  * @api public\n  */\n\n  function createDebug(namespace) {\n    var prevTime;\n\n    function debug() {\n      // Disabled?\n      if (!debug.enabled) {\n        return;\n      }\n\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      var self = debug; // Set `diff` timestamp\n\n      var curr = Number(new Date());\n      var ms = curr - (prevTime || curr);\n      self.diff = ms;\n      self.prev = prevTime;\n      self.curr = curr;\n      prevTime = curr;\n      args[0] = createDebug.coerce(args[0]);\n\n      if (typeof args[0] !== 'string') {\n        // Anything else let's inspect with %O\n        args.unshift('%O');\n      } // Apply any `formatters` transformations\n\n\n      var index = 0;\n      args[0] = args[0].replace(/%([a-zA-Z%])/g, function (match, format) {\n        // If we encounter an escaped % then don't increase the array index\n        if (match === '%%') {\n          return match;\n        }\n\n        index++;\n        var formatter = createDebug.formatters[format];\n\n        if (typeof formatter === 'function') {\n          var val = args[index];\n          match = formatter.call(self, val); // Now we need to remove `args[index]` since it's inlined in the `format`\n\n          args.splice(index, 1);\n          index--;\n        }\n\n        return match;\n      }); // Apply env-specific formatting (colors, etc.)\n\n      createDebug.formatArgs.call(self, args);\n      var logFn = self.log || createDebug.log;\n      logFn.apply(self, args);\n    }\n\n    debug.namespace = namespace;\n    debug.enabled = createDebug.enabled(namespace);\n    debug.useColors = createDebug.useColors();\n    debug.color = selectColor(namespace);\n    debug.destroy = destroy;\n    debug.extend = extend; // Debug.formatArgs = formatArgs;\n    // debug.rawLog = rawLog;\n    // env-specific initialization logic for debug instances\n\n    if (typeof createDebug.init === 'function') {\n      createDebug.init(debug);\n    }\n\n    createDebug.instances.push(debug);\n    return debug;\n  }\n\n  function destroy() {\n    var index = createDebug.instances.indexOf(this);\n\n    if (index !== -1) {\n      createDebug.instances.splice(index, 1);\n      return true;\n    }\n\n    return false;\n  }\n\n  function extend(namespace, delimiter) {\n    return createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n  }\n  /**\n  * Enables a debug mode by namespaces. This can include modes\n  * separated by a colon and wildcards.\n  *\n  * @param {String} namespaces\n  * @api public\n  */\n\n\n  function enable(namespaces) {\n    createDebug.save(namespaces);\n    createDebug.names = [];\n    createDebug.skips = [];\n    var i;\n    var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n    var len = split.length;\n\n    for (i = 0; i < len; i++) {\n      if (!split[i]) {\n        // ignore empty strings\n        continue;\n      }\n\n      namespaces = split[i].replace(/\\*/g, '.*?');\n\n      if (namespaces[0] === '-') {\n        createDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n      } else {\n        createDebug.names.push(new RegExp('^' + namespaces + '$'));\n      }\n    }\n\n    for (i = 0; i < createDebug.instances.length; i++) {\n      var instance = createDebug.instances[i];\n      instance.enabled = createDebug.enabled(instance.namespace);\n    }\n  }\n  /**\n  * Disable debug output.\n  *\n  * @api public\n  */\n\n\n  function disable() {\n    createDebug.enable('');\n  }\n  /**\n  * Returns true if the given mode name is enabled, false otherwise.\n  *\n  * @param {String} name\n  * @return {Boolean}\n  * @api public\n  */\n\n\n  function enabled(name) {\n    if (name[name.length - 1] === '*') {\n      return true;\n    }\n\n    var i;\n    var len;\n\n    for (i = 0, len = createDebug.skips.length; i < len; i++) {\n      if (createDebug.skips[i].test(name)) {\n        return false;\n      }\n    }\n\n    for (i = 0, len = createDebug.names.length; i < len; i++) {\n      if (createDebug.names[i].test(name)) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n  /**\n  * Coerce `val`.\n  *\n  * @param {Mixed} val\n  * @return {Mixed}\n  * @api private\n  */\n\n\n  function coerce(val) {\n    if (val instanceof Error) {\n      return val.stack || val.message;\n    }\n\n    return val;\n  }\n\n  createDebug.enable(createDebug.load());\n  return createDebug;\n}\n\nmodule.exports = setup;\n\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,GAAG,EAAE;EAClBC,WAAW,CAACC,KAAK,GAAGD,WAAW;EAC/BA,WAAW,CAACE,OAAO,GAAGF,WAAW;EACjCA,WAAW,CAACG,MAAM,GAAGA,MAAM;EAC3BH,WAAW,CAACI,OAAO,GAAGA,OAAO;EAC7BJ,WAAW,CAACK,MAAM,GAAGA,MAAM;EAC3BL,WAAW,CAACM,OAAO,GAAGA,OAAO;EAC7BN,WAAW,CAACO,QAAQ,GAAGC,OAAO,CAAC,IAAI,CAAC;EACpCC,MAAM,CAACC,IAAI,CAACX,GAAG,CAAC,CAACY,OAAO,CAAC,UAAUC,GAAG,EAAE;IACtCZ,WAAW,CAACY,GAAG,CAAC,GAAGb,GAAG,CAACa,GAAG,CAAC;EAC7B,CAAC,CAAC;EACF;AACF;AACA;;EAEEZ,WAAW,CAACa,SAAS,GAAG,EAAE;EAC1B;AACF;AACA;;EAEEb,WAAW,CAACc,KAAK,GAAG,EAAE;EACtBd,WAAW,CAACe,KAAK,GAAG,EAAE;EACtB;AACF;AACA;AACA;AACA;;EAEEf,WAAW,CAACgB,UAAU,GAAG,CAAC,CAAC;EAC3B;AACF;AACA;AACA;AACA;AACA;;EAEE,SAASC,WAAWA,CAACC,SAAS,EAAE;IAC9B,IAAIC,IAAI,GAAG,CAAC;IAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACzCD,IAAI,GAAG,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGD,SAAS,CAACI,UAAU,CAACF,CAAC,CAAC;MACnDD,IAAI,IAAI,CAAC,CAAC,CAAC;IACb;IAEA,OAAOnB,WAAW,CAACuB,MAAM,CAACC,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,GAAGnB,WAAW,CAACuB,MAAM,CAACF,MAAM,CAAC;EACvE;EAEArB,WAAW,CAACiB,WAAW,GAAGA,WAAW;EACrC;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE,SAASjB,WAAWA,CAACkB,SAAS,EAAE;IAC9B,IAAIQ,QAAQ;IAEZ,SAASzB,KAAKA,CAAA,EAAG;MACf;MACA,IAAI,CAACA,KAAK,CAACK,OAAO,EAAE;QAClB;MACF;MAEA,KAAK,IAAIqB,IAAI,GAAGC,SAAS,CAACP,MAAM,EAAEQ,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;MAC9B;MAEA,IAAIC,IAAI,GAAG/B,KAAK,CAAC,CAAC;;MAElB,IAAIgC,IAAI,GAAGC,MAAM,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MAC7B,IAAIC,EAAE,GAAGH,IAAI,IAAIP,QAAQ,IAAIO,IAAI,CAAC;MAClCD,IAAI,CAACK,IAAI,GAAGD,EAAE;MACdJ,IAAI,CAACM,IAAI,GAAGZ,QAAQ;MACpBM,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChBP,QAAQ,GAAGO,IAAI;MACfJ,IAAI,CAAC,CAAC,CAAC,GAAG7B,WAAW,CAACG,MAAM,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAAC;MAErC,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAC/B;QACAA,IAAI,CAACU,OAAO,CAAC,IAAI,CAAC;MACpB,CAAC,CAAC;;MAGF,IAAIC,KAAK,GAAG,CAAC;MACbX,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAEC,MAAM,EAAE;QAClE;QACA,IAAID,KAAK,KAAK,IAAI,EAAE;UAClB,OAAOA,KAAK;QACd;QAEAF,KAAK,EAAE;QACP,IAAII,SAAS,GAAG5C,WAAW,CAACgB,UAAU,CAAC2B,MAAM,CAAC;QAE9C,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;UACnC,IAAIC,GAAG,GAAGhB,IAAI,CAACW,KAAK,CAAC;UACrBE,KAAK,GAAGE,SAAS,CAACE,IAAI,CAACd,IAAI,EAAEa,GAAG,CAAC,CAAC,CAAC;;UAEnChB,IAAI,CAACkB,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;UACrBA,KAAK,EAAE;QACT;QAEA,OAAOE,KAAK;MACd,CAAC,CAAC,CAAC,CAAC;;MAEJ1C,WAAW,CAACgD,UAAU,CAACF,IAAI,CAACd,IAAI,EAAEH,IAAI,CAAC;MACvC,IAAIoB,KAAK,GAAGjB,IAAI,CAACkB,GAAG,IAAIlD,WAAW,CAACkD,GAAG;MACvCD,KAAK,CAACE,KAAK,CAACnB,IAAI,EAAEH,IAAI,CAAC;IACzB;IAEA5B,KAAK,CAACiB,SAAS,GAAGA,SAAS;IAC3BjB,KAAK,CAACK,OAAO,GAAGN,WAAW,CAACM,OAAO,CAACY,SAAS,CAAC;IAC9CjB,KAAK,CAACmD,SAAS,GAAGpD,WAAW,CAACoD,SAAS,CAAC,CAAC;IACzCnD,KAAK,CAACoD,KAAK,GAAGpC,WAAW,CAACC,SAAS,CAAC;IACpCjB,KAAK,CAACqD,OAAO,GAAGA,OAAO;IACvBrD,KAAK,CAACsD,MAAM,GAAGA,MAAM,CAAC,CAAC;IACvB;IACA;;IAEA,IAAI,OAAOvD,WAAW,CAACwD,IAAI,KAAK,UAAU,EAAE;MAC1CxD,WAAW,CAACwD,IAAI,CAACvD,KAAK,CAAC;IACzB;IAEAD,WAAW,CAACa,SAAS,CAAC4C,IAAI,CAACxD,KAAK,CAAC;IACjC,OAAOA,KAAK;EACd;EAEA,SAASqD,OAAOA,CAAA,EAAG;IACjB,IAAId,KAAK,GAAGxC,WAAW,CAACa,SAAS,CAAC6C,OAAO,CAAC,IAAI,CAAC;IAE/C,IAAIlB,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBxC,WAAW,CAACa,SAAS,CAACkC,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;MACtC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA,SAASe,MAAMA,CAACrC,SAAS,EAAEyC,SAAS,EAAE;IACpC,OAAO3D,WAAW,CAAC,IAAI,CAACkB,SAAS,IAAI,OAAOyC,SAAS,KAAK,WAAW,GAAG,GAAG,GAAGA,SAAS,CAAC,GAAGzC,SAAS,CAAC;EACvG;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAGE,SAASb,MAAMA,CAACuD,UAAU,EAAE;IAC1B5D,WAAW,CAAC6D,IAAI,CAACD,UAAU,CAAC;IAC5B5D,WAAW,CAACc,KAAK,GAAG,EAAE;IACtBd,WAAW,CAACe,KAAK,GAAG,EAAE;IACtB,IAAIK,CAAC;IACL,IAAI0C,KAAK,GAAG,CAAC,OAAOF,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE,EAAEE,KAAK,CAAC,QAAQ,CAAC;IAC9E,IAAIC,GAAG,GAAGD,KAAK,CAACzC,MAAM;IAEtB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,GAAG,EAAE3C,CAAC,EAAE,EAAE;MACxB,IAAI,CAAC0C,KAAK,CAAC1C,CAAC,CAAC,EAAE;QACb;QACA;MACF;MAEAwC,UAAU,GAAGE,KAAK,CAAC1C,CAAC,CAAC,CAACqB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;MAE3C,IAAImB,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACzB5D,WAAW,CAACe,KAAK,CAAC0C,IAAI,CAAC,IAAIO,MAAM,CAAC,GAAG,GAAGJ,UAAU,CAACK,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;MACtE,CAAC,MAAM;QACLjE,WAAW,CAACc,KAAK,CAAC2C,IAAI,CAAC,IAAIO,MAAM,CAAC,GAAG,GAAGJ,UAAU,GAAG,GAAG,CAAC,CAAC;MAC5D;IACF;IAEA,KAAKxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,WAAW,CAACa,SAAS,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,IAAI8C,QAAQ,GAAGlE,WAAW,CAACa,SAAS,CAACO,CAAC,CAAC;MACvC8C,QAAQ,CAAC5D,OAAO,GAAGN,WAAW,CAACM,OAAO,CAAC4D,QAAQ,CAAChD,SAAS,CAAC;IAC5D;EACF;EACA;AACF;AACA;AACA;AACA;;EAGE,SAASd,OAAOA,CAAA,EAAG;IACjBJ,WAAW,CAACK,MAAM,CAAC,EAAE,CAAC;EACxB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAGE,SAASC,OAAOA,CAAC6D,IAAI,EAAE;IACrB,IAAIA,IAAI,CAACA,IAAI,CAAC9C,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MACjC,OAAO,IAAI;IACb;IAEA,IAAID,CAAC;IACL,IAAI2C,GAAG;IAEP,KAAK3C,CAAC,GAAG,CAAC,EAAE2C,GAAG,GAAG/D,WAAW,CAACe,KAAK,CAACM,MAAM,EAAED,CAAC,GAAG2C,GAAG,EAAE3C,CAAC,EAAE,EAAE;MACxD,IAAIpB,WAAW,CAACe,KAAK,CAACK,CAAC,CAAC,CAACgD,IAAI,CAACD,IAAI,CAAC,EAAE;QACnC,OAAO,KAAK;MACd;IACF;IAEA,KAAK/C,CAAC,GAAG,CAAC,EAAE2C,GAAG,GAAG/D,WAAW,CAACc,KAAK,CAACO,MAAM,EAAED,CAAC,GAAG2C,GAAG,EAAE3C,CAAC,EAAE,EAAE;MACxD,IAAIpB,WAAW,CAACc,KAAK,CAACM,CAAC,CAAC,CAACgD,IAAI,CAACD,IAAI,CAAC,EAAE;QACnC,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAGE,SAAShE,MAAMA,CAAC0C,GAAG,EAAE;IACnB,IAAIA,GAAG,YAAYwB,KAAK,EAAE;MACxB,OAAOxB,GAAG,CAACyB,KAAK,IAAIzB,GAAG,CAAC0B,OAAO;IACjC;IAEA,OAAO1B,GAAG;EACZ;EAEA7C,WAAW,CAACK,MAAM,CAACL,WAAW,CAACwE,IAAI,CAAC,CAAC,CAAC;EACtC,OAAOxE,WAAW;AACpB;AAEAyE,MAAM,CAACC,OAAO,GAAG5E,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}