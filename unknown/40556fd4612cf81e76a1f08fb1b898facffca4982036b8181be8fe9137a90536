{"ast": null, "code": "'use strict';\n\nvar transportList = require('./transport-list');\nmodule.exports = require('./main')(transportList);\n\n// TODO can't get rid of this until all servers do\nif ('_sockjs_onload' in global) {\n  setTimeout(global._sockjs_onload, 1);\n}", "map": {"version": 3, "names": ["transportList", "require", "module", "exports", "global", "setTimeout", "_sockjs_onload"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/entry.js"], "sourcesContent": ["'use strict';\n\nvar transportList = require('./transport-list');\n\nmodule.exports = require('./main')(transportList);\n\n// TODO can't get rid of this until all servers do\nif ('_sockjs_onload' in global) {\n  setTimeout(global._sockjs_onload, 1);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE/CC,MAAM,CAACC,OAAO,GAAGF,OAAO,CAAC,QAAQ,CAAC,CAACD,aAAa,CAAC;;AAEjD;AACA,IAAI,gBAAgB,IAAII,MAAM,EAAE;EAC9BC,UAAU,CAACD,MAAM,CAACE,cAAc,EAAE,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}