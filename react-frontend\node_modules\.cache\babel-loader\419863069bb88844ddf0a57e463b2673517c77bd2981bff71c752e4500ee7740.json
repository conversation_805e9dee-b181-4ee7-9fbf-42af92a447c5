{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new\\\\Meditech\\\\react-frontend\\\\src\\\\pages\\\\video-consultations\\\\VideoConsultationsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { Video, Calendar, Clock, History, Monitor, Users, Shield, Play, Eye } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { videoCallService } from '../../services/videoCallService';\nimport ConsultationHistory from '../../components/video/ConsultationHistory';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 24px;\n  background: #f7fafc;\n  min-height: 100vh;\n`;\n_c = VideoContainer;\nconst Header = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 32px;\n  margin-bottom: 32px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n`;\n_c2 = Header;\nconst HeaderContent = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 20px;\n`;\n_c3 = HeaderContent;\nconst HeaderInfo = styled.div``;\n_c4 = HeaderInfo;\nconst Title = styled.h1`\n  color: #2d3748;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n_c5 = Title;\nconst Subtitle = styled.p`\n  color: #718096;\n  font-size: 1.1rem;\n  line-height: 1.6;\n`;\n_c6 = Subtitle;\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n`;\n_c7 = HeaderActions;\nconst ActionButton = styled.button`\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  background: ${props => props.variant === 'primary' ? '#4299e1' : '#edf2f7'};\n  color: ${props => props.variant === 'primary' ? 'white' : '#4a5568'};\n\n  &:hover {\n    background: ${props => props.variant === 'primary' ? '#3182ce' : '#e2e8f0'};\n    transform: translateY(-1px);\n  }\n`;\n_c8 = ActionButton;\nconst TabContainer = styled.div`\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n`;\n_c9 = TabContainer;\nconst TabHeader = styled.div`\n  display: flex;\n  border-bottom: 1px solid #e2e8f0;\n`;\n_c0 = TabHeader;\nconst Tab = styled.button`\n  flex: 1;\n  padding: 16px 24px;\n  border: none;\n  background: ${props => props.active ? '#4299e1' : 'white'};\n  color: ${props => props.active ? 'white' : '#4a5568'};\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n\n  &:hover {\n    background: ${props => props.active ? '#3182ce' : '#f7fafc'};\n  }\n`;\n_c1 = Tab;\nconst TabContent = styled.div`\n  padding: 32px;\n`;\n_c10 = TabContent;\nconst UpcomingSection = styled.div``;\n_c11 = UpcomingSection;\nconst SectionTitle = styled.h2`\n  color: #2d3748;\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 20px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c12 = SectionTitle;\nconst ConsultationGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 20px;\n  margin-bottom: 32px;\n`;\n_c13 = ConsultationGrid;\nconst ConsultationCard = styled.div`\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  border: 1px solid #e2e8f0;\n  transition: all 0.2s ease;\n\n  &:hover {\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n    transform: translateY(-2px);\n  }\n`;\n_c14 = ConsultationCard;\nconst CardHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n`;\n_c15 = CardHeader;\nconst ConsultationInfo = styled.div``;\n_c16 = ConsultationInfo;\nconst ConsultationDate = styled.div`\n  font-size: 16px;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 4px;\n`;\n_c17 = ConsultationDate;\nconst ConsultationType = styled.div`\n  font-size: 14px;\n  color: #718096;\n  text-transform: capitalize;\n`;\n_c18 = ConsultationType;\nconst StatusBadge = styled.span`\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n\n  background: ${props => {\n  switch (props.status) {\n    case 'SCHEDULED':\n      return '#bee3f8';\n    case 'IN_PROGRESS':\n      return '#fed7d7';\n    case 'WAITING_FOR_DOCTOR':\n      return '#feebc8';\n    case 'WAITING_FOR_PATIENT':\n      return '#c6f6d5';\n    default:\n      return '#e2e8f0';\n  }\n}};\n\n  color: ${props => {\n  switch (props.status) {\n    case 'SCHEDULED':\n      return '#2b6cb0';\n    case 'IN_PROGRESS':\n      return '#c53030';\n    case 'WAITING_FOR_DOCTOR':\n      return '#c05621';\n    case 'WAITING_FOR_PATIENT':\n      return '#2f855a';\n    default:\n      return '#4a5568';\n  }\n}};\n`;\n_c19 = StatusBadge;\nconst ParticipantInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n`;\n_c20 = ParticipantInfo;\nconst Avatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 12px;\n`;\n_c21 = Avatar;\nconst ParticipantName = styled.span`\n  font-weight: 500;\n  color: #2d3748;\n`;\n_c22 = ParticipantName;\nconst CardActions = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n_c23 = CardActions;\nconst CardButton = styled.button`\n  flex: 1;\n  padding: 8px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n\n  background: ${props => props.variant === 'primary' ? '#4299e1' : '#edf2f7'};\n  color: ${props => props.variant === 'primary' ? 'white' : '#4a5568'};\n\n  &:hover {\n    background: ${props => props.variant === 'primary' ? '#3182ce' : '#e2e8f0'};\n  }\n`;\n_c24 = CardButton;\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 60px 20px;\n  color: #718096;\n`;\n_c25 = EmptyState;\nconst FeatureGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-top: 32px;\n`;\n_c26 = FeatureGrid;\nconst FeatureCard = styled.div`\n  background: #f7fafc;\n  padding: 24px;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n  text-align: center;\n`;\n_c27 = FeatureCard;\nconst FeatureIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  background: #4299e1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  margin: 0 auto 16px;\n`;\n_c28 = FeatureIcon;\nconst FeatureTitle = styled.h3`\n  color: #2d3748;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n`;\n_c29 = FeatureTitle;\nconst FeatureDescription = styled.p`\n  color: #718096;\n  font-size: 0.9rem;\n  line-height: 1.5;\n`;\n_c30 = FeatureDescription;\nconst VideoConsultationsPage = () => {\n  _s();\n  const {\n    state\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('upcoming');\n  const [upcomingConsultations, setUpcomingConsultations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    loadUpcomingConsultations();\n  }, []);\n  const loadUpcomingConsultations = async () => {\n    try {\n      setLoading(true);\n      const consultations = await videoCallService.getUpcomingConsultations();\n      setUpcomingConsultations(consultations);\n    } catch (error) {\n      console.error('Error loading upcoming consultations:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getInitials = name => {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  };\n  const formatDate = dateTime => {\n    return new Date(dateTime).toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateTime => {\n    return new Date(dateTime).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleJoinConsultation = consultation => {\n    window.open(`/video-consultation/room/${consultation.roomId}`, '_blank');\n  };\n  const features = [{\n    icon: Video,\n    title: 'HD Video Calls',\n    description: 'Crystal clear video consultations with adaptive quality'\n  }, {\n    icon: Monitor,\n    title: 'Screen Sharing',\n    description: 'Share medical records and educational materials'\n  }, {\n    icon: Users,\n    title: 'Multi-party Support',\n    description: 'Include family members or specialists when needed'\n  }, {\n    icon: Shield,\n    title: 'Secure & Private',\n    description: 'End-to-end encrypted calls ensuring privacy'\n  }];\n  return /*#__PURE__*/_jsxDEV(VideoContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: /*#__PURE__*/_jsxDEV(HeaderContent, {\n        children: [/*#__PURE__*/_jsxDEV(HeaderInfo, {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            children: \"Video Consultations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n            children: \"Secure, high-quality video consultations with healthcare providers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeaderActions, {\n          children: /*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"secondary\",\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), \"Schedule Consultation\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabContainer, {\n      children: [/*#__PURE__*/_jsxDEV(TabHeader, {\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'upcoming',\n          onClick: () => setActiveTab('upcoming'),\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), \"Upcoming Consultations\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'history',\n          onClick: () => setActiveTab('history'),\n          children: [/*#__PURE__*/_jsxDEV(History, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), \"Consultation History\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabContent, {\n        children: activeTab === 'upcoming' ? /*#__PURE__*/_jsxDEV(UpcomingSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(Clock, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), \"Upcoming Consultations\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            children: \"Loading consultations...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 17\n          }, this) : upcomingConsultations.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            children: [/*#__PURE__*/_jsxDEV(Video, {\n              size: 48,\n              color: \"#cbd5e0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"No upcoming consultations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Schedule a video consultation with your healthcare provider.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(ConsultationGrid, {\n            children: upcomingConsultations.map(consultation => {\n              var _state$user, _state$user2;\n              return /*#__PURE__*/_jsxDEV(ConsultationCard, {\n                children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n                  children: [/*#__PURE__*/_jsxDEV(ConsultationInfo, {\n                    children: [/*#__PURE__*/_jsxDEV(ConsultationDate, {\n                      children: formatDate(consultation.scheduledStartTime)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(ConsultationType, {\n                      children: [consultation.type.replace('_', ' ').toLowerCase(), \" \\u2022 \", formatTime(consultation.scheduledStartTime)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(StatusBadge, {\n                    status: consultation.status,\n                    children: consultation.status.replace('_', ' ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ParticipantInfo, {\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    children: getInitials(((_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.role) === 'DOCTOR' ? consultation.patient.fullName : consultation.doctor.fullName)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ParticipantName, {\n                    children: ((_state$user2 = state.user) === null || _state$user2 === void 0 ? void 0 : _state$user2.role) === 'DOCTOR' ? consultation.patient.fullName : `Dr. ${consultation.doctor.fullName}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                  children: [/*#__PURE__*/_jsxDEV(CardButton, {\n                    variant: \"primary\",\n                    onClick: () => handleJoinConsultation(consultation),\n                    children: [/*#__PURE__*/_jsxDEV(Play, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 27\n                    }, this), \"Join Call\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(CardButton, {\n                    variant: \"secondary\",\n                    children: [/*#__PURE__*/_jsxDEV(Eye, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 27\n                    }, this), \"Details\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 23\n                }, this)]\n              }, consultation.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FeatureGrid, {\n            children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(FeatureCard, {\n              children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n                children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ConsultationHistory, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 370,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoConsultationsPage, \"y/kzb/mtWEaINmALZCLQAY42MBs=\", false, function () {\n  return [useAuth];\n});\n_c31 = VideoConsultationsPage;\nexport default VideoConsultationsPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31;\n$RefreshReg$(_c, \"VideoContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"HeaderContent\");\n$RefreshReg$(_c4, \"HeaderInfo\");\n$RefreshReg$(_c5, \"Title\");\n$RefreshReg$(_c6, \"Subtitle\");\n$RefreshReg$(_c7, \"HeaderActions\");\n$RefreshReg$(_c8, \"ActionButton\");\n$RefreshReg$(_c9, \"TabContainer\");\n$RefreshReg$(_c0, \"TabHeader\");\n$RefreshReg$(_c1, \"Tab\");\n$RefreshReg$(_c10, \"TabContent\");\n$RefreshReg$(_c11, \"UpcomingSection\");\n$RefreshReg$(_c12, \"SectionTitle\");\n$RefreshReg$(_c13, \"ConsultationGrid\");\n$RefreshReg$(_c14, \"ConsultationCard\");\n$RefreshReg$(_c15, \"CardHeader\");\n$RefreshReg$(_c16, \"ConsultationInfo\");\n$RefreshReg$(_c17, \"ConsultationDate\");\n$RefreshReg$(_c18, \"ConsultationType\");\n$RefreshReg$(_c19, \"StatusBadge\");\n$RefreshReg$(_c20, \"ParticipantInfo\");\n$RefreshReg$(_c21, \"Avatar\");\n$RefreshReg$(_c22, \"ParticipantName\");\n$RefreshReg$(_c23, \"CardActions\");\n$RefreshReg$(_c24, \"CardButton\");\n$RefreshReg$(_c25, \"EmptyState\");\n$RefreshReg$(_c26, \"FeatureGrid\");\n$RefreshReg$(_c27, \"FeatureCard\");\n$RefreshReg$(_c28, \"FeatureIcon\");\n$RefreshReg$(_c29, \"FeatureTitle\");\n$RefreshReg$(_c30, \"FeatureDescription\");\n$RefreshReg$(_c31, \"VideoConsultationsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "Video", "Calendar", "Clock", "History", "Monitor", "Users", "Shield", "Play", "Eye", "useAuth", "videoCallService", "ConsultationHistory", "jsxDEV", "_jsxDEV", "VideoContainer", "div", "_c", "Header", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c3", "HeaderInfo", "_c4", "Title", "h1", "_c5", "Subtitle", "p", "_c6", "HeaderActions", "_c7", "ActionButton", "button", "props", "variant", "_c8", "TabContainer", "_c9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c0", "Tab", "active", "_c1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c10", "UpcomingSection", "_c11", "SectionTitle", "h2", "_c12", "ConsultationGrid", "_c13", "ConsultationCard", "_c14", "<PERSON><PERSON><PERSON><PERSON>", "_c15", "ConsultationInfo", "_c16", "ConsultationDate", "_c17", "ConsultationType", "_c18", "StatusBadge", "span", "status", "_c19", "ParticipantInfo", "_c20", "Avatar", "_c21", "ParticipantName", "_c22", "CardActions", "_c23", "CardButton", "_c24", "EmptyState", "_c25", "FeatureGrid", "_c26", "FeatureCard", "_c27", "FeatureIcon", "_c28", "FeatureTitle", "h3", "_c29", "FeatureDescription", "_c30", "VideoConsultationsPage", "_s", "state", "activeTab", "setActiveTab", "upcomingConsultations", "setUpcomingConsultations", "loading", "setLoading", "loadUpcomingConsultations", "consultations", "getUpcomingConsultations", "error", "console", "getInitials", "name", "split", "map", "n", "join", "toUpperCase", "formatDate", "dateTime", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleJoinConsultation", "consultation", "window", "open", "roomId", "features", "icon", "title", "description", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "length", "color", "_state$user", "_state$user2", "scheduledStartTime", "type", "replace", "toLowerCase", "user", "role", "patient", "fullName", "doctor", "id", "feature", "index", "_c31", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/src/pages/video-consultations/VideoConsultationsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport {\n  Video,\n  Calendar,\n  Clock,\n  Plus,\n  History,\n  Monitor,\n  Users,\n  Shield,\n  Play,\n  Eye,\n  Filter\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { videoCallService, VideoConsultation } from '../../services/videoCallService';\nimport ConsultationHistory from '../../components/video/ConsultationHistory';\n\nconst VideoContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 24px;\n  background: #f7fafc;\n  min-height: 100vh;\n`;\n\nconst Header = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 32px;\n  margin-bottom: 32px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n`;\n\nconst HeaderContent = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 20px;\n`;\n\nconst HeaderInfo = styled.div``;\n\nconst Title = styled.h1`\n  color: #2d3748;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n\nconst Subtitle = styled.p`\n  color: #718096;\n  font-size: 1.1rem;\n  line-height: 1.6;\n`;\n\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  background: ${props => props.variant === 'primary' ? '#4299e1' : '#edf2f7'};\n  color: ${props => props.variant === 'primary' ? 'white' : '#4a5568'};\n\n  &:hover {\n    background: ${props => props.variant === 'primary' ? '#3182ce' : '#e2e8f0'};\n    transform: translateY(-1px);\n  }\n`;\n\nconst TabContainer = styled.div`\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n`;\n\nconst TabHeader = styled.div`\n  display: flex;\n  border-bottom: 1px solid #e2e8f0;\n`;\n\nconst Tab = styled.button<{ active: boolean }>`\n  flex: 1;\n  padding: 16px 24px;\n  border: none;\n  background: ${props => props.active ? '#4299e1' : 'white'};\n  color: ${props => props.active ? 'white' : '#4a5568'};\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n\n  &:hover {\n    background: ${props => props.active ? '#3182ce' : '#f7fafc'};\n  }\n`;\n\nconst TabContent = styled.div`\n  padding: 32px;\n`;\n\nconst UpcomingSection = styled.div``;\n\nconst SectionTitle = styled.h2`\n  color: #2d3748;\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 20px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst ConsultationGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 20px;\n  margin-bottom: 32px;\n`;\n\nconst ConsultationCard = styled.div`\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  border: 1px solid #e2e8f0;\n  transition: all 0.2s ease;\n\n  &:hover {\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n    transform: translateY(-2px);\n  }\n`;\n\nconst CardHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n`;\n\nconst ConsultationInfo = styled.div``;\n\nconst ConsultationDate = styled.div`\n  font-size: 16px;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 4px;\n`;\n\nconst ConsultationType = styled.div`\n  font-size: 14px;\n  color: #718096;\n  text-transform: capitalize;\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n\n  background: ${props => {\n    switch (props.status) {\n      case 'SCHEDULED': return '#bee3f8';\n      case 'IN_PROGRESS': return '#fed7d7';\n      case 'WAITING_FOR_DOCTOR': return '#feebc8';\n      case 'WAITING_FOR_PATIENT': return '#c6f6d5';\n      default: return '#e2e8f0';\n    }\n  }};\n\n  color: ${props => {\n    switch (props.status) {\n      case 'SCHEDULED': return '#2b6cb0';\n      case 'IN_PROGRESS': return '#c53030';\n      case 'WAITING_FOR_DOCTOR': return '#c05621';\n      case 'WAITING_FOR_PATIENT': return '#2f855a';\n      default: return '#4a5568';\n    }\n  }};\n`;\n\nconst ParticipantInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n`;\n\nconst Avatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 12px;\n`;\n\nconst ParticipantName = styled.span`\n  font-weight: 500;\n  color: #2d3748;\n`;\n\nconst CardActions = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n\nconst CardButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  flex: 1;\n  padding: 8px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n\n  background: ${props => props.variant === 'primary' ? '#4299e1' : '#edf2f7'};\n  color: ${props => props.variant === 'primary' ? 'white' : '#4a5568'};\n\n  &:hover {\n    background: ${props => props.variant === 'primary' ? '#3182ce' : '#e2e8f0'};\n  }\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 60px 20px;\n  color: #718096;\n`;\n\nconst FeatureGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-top: 32px;\n`;\n\nconst FeatureCard = styled.div`\n  background: #f7fafc;\n  padding: 24px;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n  text-align: center;\n`;\n\nconst FeatureIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  background: #4299e1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  margin: 0 auto 16px;\n`;\n\nconst FeatureTitle = styled.h3`\n  color: #2d3748;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n`;\n\nconst FeatureDescription = styled.p`\n  color: #718096;\n  font-size: 0.9rem;\n  line-height: 1.5;\n`;\n\nconst VideoConsultationsPage: React.FC = () => {\n  const { state } = useAuth();\n  const [activeTab, setActiveTab] = useState<'upcoming' | 'history'>('upcoming');\n  const [upcomingConsultations, setUpcomingConsultations] = useState<VideoConsultation[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadUpcomingConsultations();\n  }, []);\n\n  const loadUpcomingConsultations = async () => {\n    try {\n      setLoading(true);\n      const consultations = await videoCallService.getUpcomingConsultations();\n      setUpcomingConsultations(consultations);\n    } catch (error) {\n      console.error('Error loading upcoming consultations:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getInitials = (name: string): string => {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  };\n\n  const formatDate = (dateTime: string): string => {\n    return new Date(dateTime).toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const formatTime = (dateTime: string): string => {\n    return new Date(dateTime).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const handleJoinConsultation = (consultation: VideoConsultation) => {\n    window.open(`/video-consultation/room/${consultation.roomId}`, '_blank');\n  };\n\n  const features = [\n    {\n      icon: Video,\n      title: 'HD Video Calls',\n      description: 'Crystal clear video consultations with adaptive quality'\n    },\n    {\n      icon: Monitor,\n      title: 'Screen Sharing',\n      description: 'Share medical records and educational materials'\n    },\n    {\n      icon: Users,\n      title: 'Multi-party Support',\n      description: 'Include family members or specialists when needed'\n    },\n    {\n      icon: Shield,\n      title: 'Secure & Private',\n      description: 'End-to-end encrypted calls ensuring privacy'\n    }\n  ];\n\n  return (\n    <VideoContainer>\n      <Header>\n        <HeaderContent>\n          <HeaderInfo>\n            <Title>Video Consultations</Title>\n            <Subtitle>\n              Secure, high-quality video consultations with healthcare providers\n            </Subtitle>\n          </HeaderInfo>\n          <HeaderActions>\n            <ActionButton variant=\"secondary\">\n              <Calendar size={20} />\n              Schedule Consultation\n            </ActionButton>\n          </HeaderActions>\n        </HeaderContent>\n      </Header>\n\n      <TabContainer>\n        <TabHeader>\n          <Tab\n            active={activeTab === 'upcoming'}\n            onClick={() => setActiveTab('upcoming')}\n          >\n            <Calendar size={20} />\n            Upcoming Consultations\n          </Tab>\n          <Tab\n            active={activeTab === 'history'}\n            onClick={() => setActiveTab('history')}\n          >\n            <History size={20} />\n            Consultation History\n          </Tab>\n        </TabHeader>\n\n        <TabContent>\n          {activeTab === 'upcoming' ? (\n            <UpcomingSection>\n              <SectionTitle>\n                <Clock size={24} />\n                Upcoming Consultations\n              </SectionTitle>\n\n              {loading ? (\n                <EmptyState>Loading consultations...</EmptyState>\n              ) : upcomingConsultations.length === 0 ? (\n                <EmptyState>\n                  <Video size={48} color=\"#cbd5e0\" />\n                  <h3>No upcoming consultations</h3>\n                  <p>Schedule a video consultation with your healthcare provider.</p>\n                </EmptyState>\n              ) : (\n                <ConsultationGrid>\n                  {upcomingConsultations.map(consultation => (\n                    <ConsultationCard key={consultation.id}>\n                      <CardHeader>\n                        <ConsultationInfo>\n                          <ConsultationDate>\n                            {formatDate(consultation.scheduledStartTime)}\n                          </ConsultationDate>\n                          <ConsultationType>\n                            {consultation.type.replace('_', ' ').toLowerCase()} • {formatTime(consultation.scheduledStartTime)}\n                          </ConsultationType>\n                        </ConsultationInfo>\n                        <StatusBadge status={consultation.status}>\n                          {consultation.status.replace('_', ' ')}\n                        </StatusBadge>\n                      </CardHeader>\n\n                      <ParticipantInfo>\n                        <Avatar>\n                          {getInitials(state.user?.role === 'DOCTOR' ? consultation.patient.fullName : consultation.doctor.fullName)}\n                        </Avatar>\n                        <ParticipantName>\n                          {state.user?.role === 'DOCTOR'\n                            ? consultation.patient.fullName\n                            : `Dr. ${consultation.doctor.fullName}`\n                          }\n                        </ParticipantName>\n                      </ParticipantInfo>\n\n                      <CardActions>\n                        <CardButton\n                          variant=\"primary\"\n                          onClick={() => handleJoinConsultation(consultation)}\n                        >\n                          <Play size={16} />\n                          Join Call\n                        </CardButton>\n                        <CardButton variant=\"secondary\">\n                          <Eye size={16} />\n                          Details\n                        </CardButton>\n                      </CardActions>\n                    </ConsultationCard>\n                  ))}\n                </ConsultationGrid>\n              )}\n\n              <FeatureGrid>\n                {features.map((feature, index) => (\n                  <FeatureCard key={index}>\n                    <FeatureIcon>\n                      <feature.icon size={24} />\n                    </FeatureIcon>\n                    <FeatureTitle>{feature.title}</FeatureTitle>\n                    <FeatureDescription>{feature.description}</FeatureDescription>\n                  </FeatureCard>\n                ))}\n              </FeatureGrid>\n            </UpcomingSection>\n          ) : (\n            <ConsultationHistory />\n          )}\n        </TabContent>\n      </TabContainer>\n    </VideoContainer>\n  );\n};\n\nexport default VideoConsultationsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SACEC,KAAK,EACLC,QAAQ,EACRC,KAAK,EAELC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,GAAG,QAEE,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,gBAAgB,QAA2B,iCAAiC;AACrF,OAAOC,mBAAmB,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,cAAc,GAAGf,MAAM,CAACgB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,MAAM,GAAGlB,MAAM,CAACgB,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,MAAM;AAQZ,MAAME,aAAa,GAAGpB,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GANID,aAAa;AAQnB,MAAME,UAAU,GAAGtB,MAAM,CAACgB,GAAG,EAAE;AAACO,GAAA,GAA1BD,UAAU;AAEhB,MAAME,KAAK,GAAGxB,MAAM,CAACyB,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,QAAQ,GAAG3B,MAAM,CAAC4B,CAAC;AACzB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,QAAQ;AAMd,MAAMG,aAAa,GAAG9B,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGhC,MAAM,CAACiC,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AAC5E,WAAWD,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,OAAO,GAAG,SAAS;AACrE;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AAC9E;AACA;AACA,CAAC;AAACC,GAAA,GAlBIJ,YAAY;AAoBlB,MAAMK,YAAY,GAAGrC,MAAM,CAACgB,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GALID,YAAY;AAOlB,MAAME,SAAS,GAAGvC,MAAM,CAACgB,GAAG;AAC5B;AACA;AACA,CAAC;AAACwB,GAAA,GAHID,SAAS;AAKf,MAAME,GAAG,GAAGzC,MAAM,CAACiC,MAA2B;AAC9C;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWR,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,OAAO,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBR,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,SAAS,GAAG,SAAS;AAC/D;AACA,CAAC;AAACC,GAAA,GAjBIF,GAAG;AAmBT,MAAMG,UAAU,GAAG5C,MAAM,CAACgB,GAAG;AAC7B;AACA,CAAC;AAAC6B,IAAA,GAFID,UAAU;AAIhB,MAAME,eAAe,GAAG9C,MAAM,CAACgB,GAAG,EAAE;AAAC+B,IAAA,GAA/BD,eAAe;AAErB,MAAME,YAAY,GAAGhD,MAAM,CAACiD,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GARIF,YAAY;AAUlB,MAAMG,gBAAgB,GAAGnD,MAAM,CAACgB,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GALID,gBAAgB;AAOtB,MAAME,gBAAgB,GAAGrD,MAAM,CAACgB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GAXID,gBAAgB;AAatB,MAAME,UAAU,GAAGvD,MAAM,CAACgB,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GALID,UAAU;AAOhB,MAAME,gBAAgB,GAAGzD,MAAM,CAACgB,GAAG,EAAE;AAAC0C,IAAA,GAAhCD,gBAAgB;AAEtB,MAAME,gBAAgB,GAAG3D,MAAM,CAACgB,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAAC4C,IAAA,GALID,gBAAgB;AAOtB,MAAME,gBAAgB,GAAG7D,MAAM,CAACgB,GAAG;AACnC;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GAJID,gBAAgB;AAMtB,MAAME,WAAW,GAAG/D,MAAM,CAACgE,IAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB9B,KAAK,IAAI;EACrB,QAAQA,KAAK,CAAC+B,MAAM;IAClB,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC,KAAK,aAAa;MAAE,OAAO,SAAS;IACpC,KAAK,oBAAoB;MAAE,OAAO,SAAS;IAC3C,KAAK,qBAAqB;MAAE,OAAO,SAAS;IAC5C;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AACH;AACA,WAAW/B,KAAK,IAAI;EAChB,QAAQA,KAAK,CAAC+B,MAAM;IAClB,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC,KAAK,aAAa;MAAE,OAAO,SAAS;IACpC,KAAK,oBAAoB;MAAE,OAAO,SAAS;IAC3C,KAAK,qBAAqB;MAAE,OAAO,SAAS;IAC5C;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AACH,CAAC;AAACC,IAAA,GA1BIH,WAAW;AA4BjB,MAAMI,eAAe,GAAGnE,MAAM,CAACgB,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GALID,eAAe;AAOrB,MAAME,MAAM,GAAGrE,MAAM,CAACgB,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsD,IAAA,GAXID,MAAM;AAaZ,MAAME,eAAe,GAAGvE,MAAM,CAACgE,IAAI;AACnC;AACA;AACA,CAAC;AAACQ,IAAA,GAHID,eAAe;AAKrB,MAAME,WAAW,GAAGzE,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA,CAAC;AAAC0D,IAAA,GAHID,WAAW;AAKjB,MAAME,UAAU,GAAG3E,MAAM,CAACiC,MAA6C;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AAC5E,WAAWD,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,OAAO,GAAG,SAAS;AACrE;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AAC9E;AACA,CAAC;AAACyC,IAAA,GApBID,UAAU;AAsBhB,MAAME,UAAU,GAAG7E,MAAM,CAACgB,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAAC8D,IAAA,GAJID,UAAU;AAMhB,MAAME,WAAW,GAAG/E,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACgE,IAAA,GALID,WAAW;AAOjB,MAAME,WAAW,GAAGjF,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkE,IAAA,GANID,WAAW;AAQjB,MAAME,WAAW,GAAGnF,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoE,IAAA,GAVID,WAAW;AAYjB,MAAME,YAAY,GAAGrF,MAAM,CAACsF,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,YAAY;AAOlB,MAAMG,kBAAkB,GAAGxF,MAAM,CAAC4B,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAAC6D,IAAA,GAJID,kBAAkB;AAMxB,MAAME,sBAAgC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7C,MAAM;IAAEC;EAAM,CAAC,GAAGlF,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACmF,SAAS,EAAEC,YAAY,CAAC,GAAGhG,QAAQ,CAAyB,UAAU,CAAC;EAC9E,MAAM,CAACiG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlG,QAAQ,CAAsB,EAAE,CAAC;EAC3F,MAAM,CAACmG,OAAO,EAAEC,UAAU,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdoG,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,aAAa,GAAG,MAAMzF,gBAAgB,CAAC0F,wBAAwB,CAAC,CAAC;MACvEL,wBAAwB,CAACI,aAAa,CAAC;IACzC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMM,WAAW,GAAIC,IAAY,IAAa;IAC5C,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAC9D,CAAC;EAED,MAAMC,UAAU,GAAIC,QAAgB,IAAa;IAC/C,OAAO,IAAIC,IAAI,CAACD,QAAQ,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIP,QAAgB,IAAa;IAC/C,OAAO,IAAIC,IAAI,CAACD,QAAQ,CAAC,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,sBAAsB,GAAIC,YAA+B,IAAK;IAClEC,MAAM,CAACC,IAAI,CAAC,4BAA4BF,YAAY,CAACG,MAAM,EAAE,EAAE,QAAQ,CAAC;EAC1E,CAAC;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAEhI,KAAK;IACXiI,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE5H,OAAO;IACb6H,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE3H,KAAK;IACX4H,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE1H,MAAM;IACZ2H,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACErH,OAAA,CAACC,cAAc;IAAAqH,QAAA,gBACbtH,OAAA,CAACI,MAAM;MAAAkH,QAAA,eACLtH,OAAA,CAACM,aAAa;QAAAgH,QAAA,gBACZtH,OAAA,CAACQ,UAAU;UAAA8G,QAAA,gBACTtH,OAAA,CAACU,KAAK;YAAA4G,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClC1H,OAAA,CAACa,QAAQ;YAAAyG,QAAA,EAAC;UAEV;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACb1H,OAAA,CAACgB,aAAa;UAAAsG,QAAA,eACZtH,OAAA,CAACkB,YAAY;YAACG,OAAO,EAAC,WAAW;YAAAiG,QAAA,gBAC/BtH,OAAA,CAACZ,QAAQ;cAACuI,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET1H,OAAA,CAACuB,YAAY;MAAA+F,QAAA,gBACXtH,OAAA,CAACyB,SAAS;QAAA6F,QAAA,gBACRtH,OAAA,CAAC2B,GAAG;UACFC,MAAM,EAAEmD,SAAS,KAAK,UAAW;UACjC6C,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,UAAU,CAAE;UAAAsC,QAAA,gBAExCtH,OAAA,CAACZ,QAAQ;YAACuI,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN1H,OAAA,CAAC2B,GAAG;UACFC,MAAM,EAAEmD,SAAS,KAAK,SAAU;UAChC6C,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,SAAS,CAAE;UAAAsC,QAAA,gBAEvCtH,OAAA,CAACV,OAAO;YAACqI,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEvB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEZ1H,OAAA,CAAC8B,UAAU;QAAAwF,QAAA,EACRvC,SAAS,KAAK,UAAU,gBACvB/E,OAAA,CAACgC,eAAe;UAAAsF,QAAA,gBACdtH,OAAA,CAACkC,YAAY;YAAAoF,QAAA,gBACXtH,OAAA,CAACX,KAAK;cAACsI,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,0BAErB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,EAEdvC,OAAO,gBACNnF,OAAA,CAAC+D,UAAU;YAAAuD,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,GAC/CzC,qBAAqB,CAAC4C,MAAM,KAAK,CAAC,gBACpC7H,OAAA,CAAC+D,UAAU;YAAAuD,QAAA,gBACTtH,OAAA,CAACb,KAAK;cAACwI,IAAI,EAAE,EAAG;cAACG,KAAK,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC1H,OAAA;cAAAsH,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC1H,OAAA;cAAAsH,QAAA,EAAG;YAA4D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,gBAEb1H,OAAA,CAACqC,gBAAgB;YAAAiF,QAAA,EACdrC,qBAAqB,CAACY,GAAG,CAACiB,YAAY;cAAA,IAAAiB,WAAA,EAAAC,YAAA;cAAA,oBACrChI,OAAA,CAACuC,gBAAgB;gBAAA+E,QAAA,gBACftH,OAAA,CAACyC,UAAU;kBAAA6E,QAAA,gBACTtH,OAAA,CAAC2C,gBAAgB;oBAAA2E,QAAA,gBACftH,OAAA,CAAC6C,gBAAgB;sBAAAyE,QAAA,EACdrB,UAAU,CAACa,YAAY,CAACmB,kBAAkB;oBAAC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACnB1H,OAAA,CAAC+C,gBAAgB;sBAAAuE,QAAA,GACdR,YAAY,CAACoB,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,UAAG,EAAC3B,UAAU,CAACK,YAAY,CAACmB,kBAAkB,CAAC;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACnB1H,OAAA,CAACiD,WAAW;oBAACE,MAAM,EAAE2D,YAAY,CAAC3D,MAAO;oBAAAmE,QAAA,EACtCR,YAAY,CAAC3D,MAAM,CAACgF,OAAO,CAAC,GAAG,EAAE,GAAG;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEb1H,OAAA,CAACqD,eAAe;kBAAAiE,QAAA,gBACdtH,OAAA,CAACuD,MAAM;oBAAA+D,QAAA,EACJ5B,WAAW,CAAC,EAAAqC,WAAA,GAAAjD,KAAK,CAACuD,IAAI,cAAAN,WAAA,uBAAVA,WAAA,CAAYO,IAAI,MAAK,QAAQ,GAAGxB,YAAY,CAACyB,OAAO,CAACC,QAAQ,GAAG1B,YAAY,CAAC2B,MAAM,CAACD,QAAQ;kBAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpG,CAAC,eACT1H,OAAA,CAACyD,eAAe;oBAAA6D,QAAA,EACb,EAAAU,YAAA,GAAAlD,KAAK,CAACuD,IAAI,cAAAL,YAAA,uBAAVA,YAAA,CAAYM,IAAI,MAAK,QAAQ,GAC1BxB,YAAY,CAACyB,OAAO,CAACC,QAAQ,GAC7B,OAAO1B,YAAY,CAAC2B,MAAM,CAACD,QAAQ;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAE1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAElB1H,OAAA,CAAC2D,WAAW;kBAAA2D,QAAA,gBACVtH,OAAA,CAAC6D,UAAU;oBACTxC,OAAO,EAAC,SAAS;oBACjBuG,OAAO,EAAEA,CAAA,KAAMf,sBAAsB,CAACC,YAAY,CAAE;oBAAAQ,QAAA,gBAEpDtH,OAAA,CAACN,IAAI;sBAACiI,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAEpB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb1H,OAAA,CAAC6D,UAAU;oBAACxC,OAAO,EAAC,WAAW;oBAAAiG,QAAA,gBAC7BtH,OAAA,CAACL,GAAG;sBAACgI,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAEnB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAvCOZ,YAAY,CAAC4B,EAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwCpB,CAAC;YAAA,CACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CACnB,eAED1H,OAAA,CAACiE,WAAW;YAAAqD,QAAA,EACTJ,QAAQ,CAACrB,GAAG,CAAC,CAAC8C,OAAO,EAAEC,KAAK,kBAC3B5I,OAAA,CAACmE,WAAW;cAAAmD,QAAA,gBACVtH,OAAA,CAACqE,WAAW;gBAAAiD,QAAA,eACVtH,OAAA,CAAC2I,OAAO,CAACxB,IAAI;kBAACQ,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACd1H,OAAA,CAACuE,YAAY;gBAAA+C,QAAA,EAAEqB,OAAO,CAACvB;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAC5C1H,OAAA,CAAC0E,kBAAkB;gBAAA4C,QAAA,EAAEqB,OAAO,CAACtB;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqB,CAAC;YAAA,GAL9CkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAElB1H,OAAA,CAACF,mBAAmB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACvB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAErB,CAAC;AAAC7C,EAAA,CA7LID,sBAAgC;EAAA,QAClBhF,OAAO;AAAA;AAAAiJ,IAAA,GADrBjE,sBAAgC;AA+LtC,eAAeA,sBAAsB;AAAC,IAAAzE,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAkE,IAAA;AAAAC,YAAA,CAAA3I,EAAA;AAAA2I,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAA/H,GAAA;AAAA+H,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAA7G,IAAA;AAAA6G,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}