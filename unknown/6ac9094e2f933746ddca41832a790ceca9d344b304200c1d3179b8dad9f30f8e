{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\stomp-subscription.ts"], "sourcesContent": ["import { StompHeaders } from './stomp-headers.js';\n\n/**\n * Call [Client#subscribe]{@link Client#subscribe} to create a StompSubscription.\n *\n * Part of `@stomp/stompjs`.\n */\nexport interface StompSubscription {\n  /**\n   * Id associated with this subscription.\n   */\n  id: string;\n\n  /**\n   * Unsubscribe. See [Client#unsubscribe]{@link Client#unsubscribe} for an example.\n   */\n  unsubscribe: (headers?: StompHeaders) => void;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}