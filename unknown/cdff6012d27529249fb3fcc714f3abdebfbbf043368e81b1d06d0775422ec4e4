{"ast": null, "code": "import { BYTE } from './byte.js';\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl {\n  /**\n   * body of the frame\n   */\n  get body() {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n    return this._body || '';\n  }\n  /**\n   * body as Uint8Array\n   */\n  get binaryBody() {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n    // At this stage it will definitely have a valid value\n    return this._binaryBody;\n  }\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader\n    } = params;\n    this.command = command;\n    this.headers = Object.assign({}, headers || {});\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n  static fromRawFrame(rawFrame, escapeHeaderValues) {\n    const headers = {};\n    const trim = str => str.replace(/^\\s+|\\s+$/g, '');\n    // In case of repeated headers, as per standards, first value need to be used\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n      if (escapeHeaderValues && rawFrame.command !== 'CONNECT' && rawFrame.command !== 'CONNECTED') {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n      headers[key] = value;\n    }\n    return new FrameImpl({\n      command: rawFrame.command,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues\n    });\n  }\n  /**\n   * @internal\n   */\n  toString() {\n    return this.serializeCmdAndHeaders();\n  }\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n  serialize() {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n  serializeCmdAndHeaders() {\n    const lines = [this.command];\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n      if (this.escapeHeaderValues && this.command !== 'CONNECT' && this.command !== 'CONNECTED') {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n    if (this.isBinaryBody || !this.isBodyEmpty() && !this.skipContentLengthHeader) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n  isBodyEmpty() {\n    return this.bodyLength() === 0;\n  }\n  bodyLength() {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n  static sizeOfUTF8(s) {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n  static toUnit8Array(cmdAndHeaders, binaryBody) {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n  static marshall(params) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n  /**\n   *  Escape header values\n   */\n  static hdrValueEscape(str) {\n    return str.replace(/\\\\/g, '\\\\\\\\').replace(/\\r/g, '\\\\r').replace(/\\n/g, '\\\\n').replace(/:/g, '\\\\c');\n  }\n  /**\n   * UnEscape header values\n   */\n  static hdrValueUnEscape(str) {\n    return str.replace(/\\\\r/g, '\\r').replace(/\\\\n/g, '\\n').replace(/\\\\c/g, ':').replace(/\\\\\\\\/g, '\\\\');\n  }\n}", "map": {"version": 3, "names": ["BYTE", "FrameImpl", "body", "_body", "isBinaryBody", "TextDecoder", "decode", "_binaryBody", "binaryBody", "TextEncoder", "encode", "constructor", "params", "command", "headers", "escapeHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "fromRawFrame", "rawFrame", "trim", "str", "replace", "header", "reverse", "idx", "indexOf", "key", "value", "hdrValueUnEscape", "toString", "serializeCmdAndHeaders", "serialize", "cmdAndHeaders", "toUnit8Array", "buffer", "NULL", "lines", "name", "keys", "push", "hdrValueEscape", "isBodyEmpty", "<PERSON><PERSON><PERSON><PERSON>", "join", "LF", "length", "sizeOfUTF8", "s", "uint8CmdAndHeaders", "nullTerminator", "Uint8Array", "uint8Frame", "set", "marshall", "frame"], "sources": ["C:\\Users\\<USER>\\Desktop\\new\\Meditech\\react-frontend\\node_modules\\@stomp\\stompjs\\src\\frame-impl.ts"], "sourcesContent": ["import { BYTE } from './byte.js';\nimport type { <PERSON>ram<PERSON> } from './i-frame.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { IRawFrameType } from './types.js';\n\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl implements IFrame {\n  /**\n   * STOMP Command\n   */\n  public command: string;\n\n  /**\n   * Headers, key value pairs.\n   */\n  public headers: StompHeaders;\n\n  /**\n   * Is this frame binary (based on whether body/binaryBody was passed when creating this frame).\n   */\n  public isBinaryBody: boolean;\n\n  /**\n   * body of the frame\n   */\n  get body(): string {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n    return this._body || '';\n  }\n  private _body: string | undefined;\n\n  /**\n   * body as Uint8Array\n   */\n  get binaryBody(): Uint8Array {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n    // At this stage it will definitely have a valid value\n    return this._binaryBody as Uint8Array;\n  }\n  private _binaryBody: Uint8Array | undefined;\n\n  private escapeHeaderValues: boolean;\n  private skipContentLengthHeader: boolean;\n\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader,\n    } = params;\n    this.command = command;\n    this.headers = (Object as any).assign({}, headers || {});\n\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n  public static fromRawFrame(\n    rawFrame: IRawFrameType,\n    escapeHeaderValues: boolean\n  ): FrameImpl {\n    const headers: StompHeaders = {};\n    const trim = (str: string): string => str.replace(/^\\s+|\\s+$/g, '');\n\n    // In case of repeated headers, as per standards, first value need to be used\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n\n      if (\n        escapeHeaderValues &&\n        rawFrame.command !== 'CONNECT' &&\n        rawFrame.command !== 'CONNECTED'\n      ) {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n\n      headers[key] = value;\n    }\n\n    return new FrameImpl({\n      command: rawFrame.command as string,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  public toString(): string {\n    return this.serializeCmdAndHeaders();\n  }\n\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n  public serialize(): string | ArrayBuffer {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(\n        cmdAndHeaders,\n        this._binaryBody as Uint8Array\n      ).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n\n  private serializeCmdAndHeaders(): string {\n    const lines = [this.command];\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n      if (\n        this.escapeHeaderValues &&\n        this.command !== 'CONNECT' &&\n        this.command !== 'CONNECTED'\n      ) {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n    if (\n      this.isBinaryBody ||\n      (!this.isBodyEmpty() && !this.skipContentLengthHeader)\n    ) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n\n  private isBodyEmpty(): boolean {\n    return this.bodyLength() === 0;\n  }\n\n  private bodyLength(): number {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n  private static sizeOfUTF8(s: string): number {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n\n  private static toUnit8Array(\n    cmdAndHeaders: string,\n    binaryBody: Uint8Array\n  ): Uint8Array {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(\n      uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length\n    );\n\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(\n      nullTerminator,\n      uint8CmdAndHeaders.length + binaryBody.length\n    );\n\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n  public static marshall(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n\n  /**\n   *  Escape header values\n   */\n  private static hdrValueEscape(str: string): string {\n    return str\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/:/g, '\\\\c');\n  }\n\n  /**\n   * UnEscape header values\n   */\n  private static hdrValueUnEscape(str: string): string {\n    return str\n      .replace(/\\\\r/g, '\\r')\n      .replace(/\\\\n/g, '\\n')\n      .replace(/\\\\c/g, ':')\n      .replace(/\\\\\\\\/g, '\\\\');\n  }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAKhC;;;;;AAKA,OAAM,MAAOC,SAAS;EAgBpB;;;EAGA,IAAIC,IAAIA,CAAA;IACN,IAAI,CAAC,IAAI,CAACC,KAAK,IAAI,IAAI,CAACC,YAAY,EAAE;MACpC,IAAI,CAACD,KAAK,GAAG,IAAIE,WAAW,EAAE,CAACC,MAAM,CAAC,IAAI,CAACC,WAAW,CAAC;IACzD;IACA,OAAO,IAAI,CAACJ,KAAK,IAAI,EAAE;EACzB;EAGA;;;EAGA,IAAIK,UAAUA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACD,WAAW,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;MAC3C,IAAI,CAACG,WAAW,GAAG,IAAIE,WAAW,EAAE,CAACC,MAAM,CAAC,IAAI,CAACP,KAAK,CAAC;IACzD;IACA;IACA,OAAO,IAAI,CAACI,WAAyB;EACvC;EAMA;;;;;EAKAI,YAAYC,MAOX;IACC,MAAM;MACJC,OAAO;MACPC,OAAO;MACPZ,IAAI;MACJM,UAAU;MACVO,kBAAkB;MAClBC;IAAuB,CACxB,GAAGJ,MAAM;IACV,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAIG,MAAc,CAACC,MAAM,CAAC,EAAE,EAAEJ,OAAO,IAAI,EAAE,CAAC;IAExD,IAAIN,UAAU,EAAE;MACd,IAAI,CAACD,WAAW,GAAGC,UAAU;MAC7B,IAAI,CAACJ,YAAY,GAAG,IAAI;IAC1B,CAAC,MAAM;MACL,IAAI,CAACD,KAAK,GAAGD,IAAI,IAAI,EAAE;MACvB,IAAI,CAACE,YAAY,GAAG,KAAK;IAC3B;IACA,IAAI,CAACW,kBAAkB,GAAGA,kBAAkB,IAAI,KAAK;IACrD,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB,IAAI,KAAK;EACjE;EAEA;;;;;EAKO,OAAOG,YAAYA,CACxBC,QAAuB,EACvBL,kBAA2B;IAE3B,MAAMD,OAAO,GAAiB,EAAE;IAChC,MAAMO,IAAI,GAAIC,GAAW,IAAaA,GAAG,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IAEnE;IACA,KAAK,MAAMC,MAAM,IAAIJ,QAAQ,CAACN,OAAO,CAACW,OAAO,EAAE,EAAE;MAC/C,MAAMC,GAAG,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;MAE/B,MAAMC,GAAG,GAAGP,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC;MAC3B,IAAIK,KAAK,GAAGR,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC;MAE3B,IACET,kBAAkB,IAClBK,QAAQ,CAACP,OAAO,KAAK,SAAS,IAC9BO,QAAQ,CAACP,OAAO,KAAK,WAAW,EAChC;QACAgB,KAAK,GAAG5B,SAAS,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;MAC3C;MAEAf,OAAO,CAACc,GAAG,CAAC,GAAGC,KAAK;IACtB;IAEA,OAAO,IAAI5B,SAAS,CAAC;MACnBY,OAAO,EAAEO,QAAQ,CAACP,OAAiB;MACnCC,OAAO;MACPN,UAAU,EAAEY,QAAQ,CAACZ,UAAU;MAC/BO;KACD,CAAC;EACJ;EAEA;;;EAGOgB,QAAQA,CAAA;IACb,OAAO,IAAI,CAACC,sBAAsB,EAAE;EACtC;EAEA;;;;;;;EAOOC,SAASA,CAAA;IACd,MAAMC,aAAa,GAAG,IAAI,CAACF,sBAAsB,EAAE;IAEnD,IAAI,IAAI,CAAC5B,YAAY,EAAE;MACrB,OAAOH,SAAS,CAACkC,YAAY,CAC3BD,aAAa,EACb,IAAI,CAAC3B,WAAyB,CAC/B,CAAC6B,MAAM;IACV,CAAC,MAAM;MACL,OAAOF,aAAa,GAAG,IAAI,CAAC/B,KAAK,GAAGH,IAAI,CAACqC,IAAI;IAC/C;EACF;EAEQL,sBAAsBA,CAAA;IAC5B,MAAMM,KAAK,GAAG,CAAC,IAAI,CAACzB,OAAO,CAAC;IAC5B,IAAI,IAAI,CAACG,uBAAuB,EAAE;MAChC,OAAO,IAAI,CAACF,OAAO,CAAC,gBAAgB,CAAC;IACvC;IAEA,KAAK,MAAMyB,IAAI,IAAItB,MAAM,CAACuB,IAAI,CAAC,IAAI,CAAC1B,OAAO,IAAI,EAAE,CAAC,EAAE;MAClD,MAAMe,KAAK,GAAG,IAAI,CAACf,OAAO,CAACyB,IAAI,CAAC;MAChC,IACE,IAAI,CAACxB,kBAAkB,IACvB,IAAI,CAACF,OAAO,KAAK,SAAS,IAC1B,IAAI,CAACA,OAAO,KAAK,WAAW,EAC5B;QACAyB,KAAK,CAACG,IAAI,CAAC,GAAGF,IAAI,IAAItC,SAAS,CAACyC,cAAc,CAAC,GAAGb,KAAK,EAAE,CAAC,EAAE,CAAC;MAC/D,CAAC,MAAM;QACLS,KAAK,CAACG,IAAI,CAAC,GAAGF,IAAI,IAAIV,KAAK,EAAE,CAAC;MAChC;IACF;IACA,IACE,IAAI,CAACzB,YAAY,IAChB,CAAC,IAAI,CAACuC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC3B,uBAAwB,EACtD;MACAsB,KAAK,CAACG,IAAI,CAAC,kBAAkB,IAAI,CAACG,UAAU,EAAE,EAAE,CAAC;IACnD;IACA,OAAON,KAAK,CAACO,IAAI,CAAC7C,IAAI,CAAC8C,EAAE,CAAC,GAAG9C,IAAI,CAAC8C,EAAE,GAAG9C,IAAI,CAAC8C,EAAE;EAChD;EAEQH,WAAWA,CAAA;IACjB,OAAO,IAAI,CAACC,UAAU,EAAE,KAAK,CAAC;EAChC;EAEQA,UAAUA,CAAA;IAChB,MAAMpC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,OAAOA,UAAU,GAAGA,UAAU,CAACuC,MAAM,GAAG,CAAC;EAC3C;EAEA;;;;EAIQ,OAAOC,UAAUA,CAACC,CAAS;IACjC,OAAOA,CAAC,GAAG,IAAIxC,WAAW,EAAE,CAACC,MAAM,CAACuC,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC;EACnD;EAEQ,OAAOZ,YAAYA,CACzBD,aAAqB,EACrB1B,UAAsB;IAEtB,MAAM0C,kBAAkB,GAAG,IAAIzC,WAAW,EAAE,CAACC,MAAM,CAACwB,aAAa,CAAC;IAClE,MAAMiB,cAAc,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAMC,UAAU,GAAG,IAAID,UAAU,CAC/BF,kBAAkB,CAACH,MAAM,GAAGvC,UAAU,CAACuC,MAAM,GAAGI,cAAc,CAACJ,MAAM,CACtE;IAEDM,UAAU,CAACC,GAAG,CAACJ,kBAAkB,CAAC;IAClCG,UAAU,CAACC,GAAG,CAAC9C,UAAU,EAAE0C,kBAAkB,CAACH,MAAM,CAAC;IACrDM,UAAU,CAACC,GAAG,CACZH,cAAc,EACdD,kBAAkB,CAACH,MAAM,GAAGvC,UAAU,CAACuC,MAAM,CAC9C;IAED,OAAOM,UAAU;EACnB;EACA;;;;;EAKO,OAAOE,QAAQA,CAAC3C,MAOtB;IACC,MAAM4C,KAAK,GAAG,IAAIvD,SAAS,CAACW,MAAM,CAAC;IACnC,OAAO4C,KAAK,CAACvB,SAAS,EAAE;EAC1B;EAEA;;;EAGQ,OAAOS,cAAcA,CAACpB,GAAW;IACvC,OAAOA,GAAG,CACPC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;EACzB;EAEA;;;EAGQ,OAAOO,gBAAgBA,CAACR,GAAW;IACzC,OAAOA,GAAG,CACPC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;EAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}