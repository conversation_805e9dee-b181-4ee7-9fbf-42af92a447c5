{"ast": null, "code": "'use strict';\n\n/* Simplified implementation of DOM2 EventTarget.\n *   http://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-EventTarget\n */\nfunction EventTarget() {\n  this._listeners = {};\n}\nEventTarget.prototype.addEventListener = function (eventType, listener) {\n  if (!(eventType in this._listeners)) {\n    this._listeners[eventType] = [];\n  }\n  var arr = this._listeners[eventType];\n  // #4\n  if (arr.indexOf(listener) === -1) {\n    // Make a copy so as not to interfere with a current dispatchEvent.\n    arr = arr.concat([listener]);\n  }\n  this._listeners[eventType] = arr;\n};\nEventTarget.prototype.removeEventListener = function (eventType, listener) {\n  var arr = this._listeners[eventType];\n  if (!arr) {\n    return;\n  }\n  var idx = arr.indexOf(listener);\n  if (idx !== -1) {\n    if (arr.length > 1) {\n      // Make a copy so as not to interfere with a current dispatchEvent.\n      this._listeners[eventType] = arr.slice(0, idx).concat(arr.slice(idx + 1));\n    } else {\n      delete this._listeners[eventType];\n    }\n    return;\n  }\n};\nEventTarget.prototype.dispatchEvent = function () {\n  var event = arguments[0];\n  var t = event.type;\n  // equivalent of Array.prototype.slice.call(arguments, 0);\n  var args = arguments.length === 1 ? [event] : Array.apply(null, arguments);\n  // TODO: This doesn't match the real behavior; per spec, onfoo get\n  // their place in line from the /first/ time they're set from\n  // non-null. Although WebKit bumps it to the end every time it's\n  // set.\n  if (this['on' + t]) {\n    this['on' + t].apply(this, args);\n  }\n  if (t in this._listeners) {\n    // Grab a reference to the listeners list. removeEventListener may alter the list.\n    var listeners = this._listeners[t];\n    for (var i = 0; i < listeners.length; i++) {\n      listeners[i].apply(this, args);\n    }\n  }\n};\nmodule.exports = EventTarget;", "map": {"version": 3, "names": ["EventTarget", "_listeners", "prototype", "addEventListener", "eventType", "listener", "arr", "indexOf", "concat", "removeEventListener", "idx", "length", "slice", "dispatchEvent", "event", "arguments", "t", "type", "args", "Array", "apply", "listeners", "i", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/event/eventtarget.js"], "sourcesContent": ["'use strict';\n\n/* Simplified implementation of DOM2 EventTarget.\n *   http://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-EventTarget\n */\n\nfunction EventTarget() {\n  this._listeners = {};\n}\n\nEventTarget.prototype.addEventListener = function(eventType, listener) {\n  if (!(eventType in this._listeners)) {\n    this._listeners[eventType] = [];\n  }\n  var arr = this._listeners[eventType];\n  // #4\n  if (arr.indexOf(listener) === -1) {\n    // Make a copy so as not to interfere with a current dispatchEvent.\n    arr = arr.concat([listener]);\n  }\n  this._listeners[eventType] = arr;\n};\n\nEventTarget.prototype.removeEventListener = function(eventType, listener) {\n  var arr = this._listeners[eventType];\n  if (!arr) {\n    return;\n  }\n  var idx = arr.indexOf(listener);\n  if (idx !== -1) {\n    if (arr.length > 1) {\n      // Make a copy so as not to interfere with a current dispatchEvent.\n      this._listeners[eventType] = arr.slice(0, idx).concat(arr.slice(idx + 1));\n    } else {\n      delete this._listeners[eventType];\n    }\n    return;\n  }\n};\n\nEventTarget.prototype.dispatchEvent = function() {\n  var event = arguments[0];\n  var t = event.type;\n  // equivalent of Array.prototype.slice.call(arguments, 0);\n  var args = arguments.length === 1 ? [event] : Array.apply(null, arguments);\n  // TODO: This doesn't match the real behavior; per spec, onfoo get\n  // their place in line from the /first/ time they're set from\n  // non-null. Although WebKit bumps it to the end every time it's\n  // set.\n  if (this['on' + t]) {\n    this['on' + t].apply(this, args);\n  }\n  if (t in this._listeners) {\n    // Grab a reference to the listeners list. removeEventListener may alter the list.\n    var listeners = this._listeners[t];\n    for (var i = 0; i < listeners.length; i++) {\n      listeners[i].apply(this, args);\n    }\n  }\n};\n\nmodule.exports = EventTarget;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AAEA,SAASA,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;AACtB;AAEAD,WAAW,CAACE,SAAS,CAACC,gBAAgB,GAAG,UAASC,SAAS,EAAEC,QAAQ,EAAE;EACrE,IAAI,EAAED,SAAS,IAAI,IAAI,CAACH,UAAU,CAAC,EAAE;IACnC,IAAI,CAACA,UAAU,CAACG,SAAS,CAAC,GAAG,EAAE;EACjC;EACA,IAAIE,GAAG,GAAG,IAAI,CAACL,UAAU,CAACG,SAAS,CAAC;EACpC;EACA,IAAIE,GAAG,CAACC,OAAO,CAACF,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;IAChC;IACAC,GAAG,GAAGA,GAAG,CAACE,MAAM,CAAC,CAACH,QAAQ,CAAC,CAAC;EAC9B;EACA,IAAI,CAACJ,UAAU,CAACG,SAAS,CAAC,GAAGE,GAAG;AAClC,CAAC;AAEDN,WAAW,CAACE,SAAS,CAACO,mBAAmB,GAAG,UAASL,SAAS,EAAEC,QAAQ,EAAE;EACxE,IAAIC,GAAG,GAAG,IAAI,CAACL,UAAU,CAACG,SAAS,CAAC;EACpC,IAAI,CAACE,GAAG,EAAE;IACR;EACF;EACA,IAAII,GAAG,GAAGJ,GAAG,CAACC,OAAO,CAACF,QAAQ,CAAC;EAC/B,IAAIK,GAAG,KAAK,CAAC,CAAC,EAAE;IACd,IAAIJ,GAAG,CAACK,MAAM,GAAG,CAAC,EAAE;MAClB;MACA,IAAI,CAACV,UAAU,CAACG,SAAS,CAAC,GAAGE,GAAG,CAACM,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC,CAACF,MAAM,CAACF,GAAG,CAACM,KAAK,CAACF,GAAG,GAAG,CAAC,CAAC,CAAC;IAC3E,CAAC,MAAM;MACL,OAAO,IAAI,CAACT,UAAU,CAACG,SAAS,CAAC;IACnC;IACA;EACF;AACF,CAAC;AAEDJ,WAAW,CAACE,SAAS,CAACW,aAAa,GAAG,YAAW;EAC/C,IAAIC,KAAK,GAAGC,SAAS,CAAC,CAAC,CAAC;EACxB,IAAIC,CAAC,GAAGF,KAAK,CAACG,IAAI;EAClB;EACA,IAAIC,IAAI,GAAGH,SAAS,CAACJ,MAAM,KAAK,CAAC,GAAG,CAACG,KAAK,CAAC,GAAGK,KAAK,CAACC,KAAK,CAAC,IAAI,EAAEL,SAAS,CAAC;EAC1E;EACA;EACA;EACA;EACA,IAAI,IAAI,CAAC,IAAI,GAAGC,CAAC,CAAC,EAAE;IAClB,IAAI,CAAC,IAAI,GAAGA,CAAC,CAAC,CAACI,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;EAClC;EACA,IAAIF,CAAC,IAAI,IAAI,CAACf,UAAU,EAAE;IACxB;IACA,IAAIoB,SAAS,GAAG,IAAI,CAACpB,UAAU,CAACe,CAAC,CAAC;IAClC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACV,MAAM,EAAEW,CAAC,EAAE,EAAE;MACzCD,SAAS,CAACC,CAAC,CAAC,CAACF,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IAChC;EACF;AACF,CAAC;AAEDK,MAAM,CAACC,OAAO,GAAGxB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}