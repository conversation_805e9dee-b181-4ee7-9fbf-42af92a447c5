{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  XhrDriver = require('../driver/xhr');\nfunction XHRCorsObject(method, url, payload, opts) {\n  XhrDriver.call(this, method, url, payload, opts);\n}\ninherits(XHRCorsObject, XhrDriver);\nXHRCorsObject.enabled = XhrDriver.enabled && XhrDriver.supportsCORS;\nmodule.exports = XHRCorsObject;", "map": {"version": 3, "names": ["inherits", "require", "XhrDriver", "XHRCorsObject", "method", "url", "payload", "opts", "call", "enabled", "supportsCORS", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/new/Meditech/react-frontend/node_modules/sockjs-client/lib/transport/sender/xhr-cors.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRCorsObject(method, url, payload, opts) {\n  XhrDriver.call(this, method, url, payload, opts);\n}\n\ninherits(XHRCorsObject, XhrDriver);\n\nXHRCorsObject.enabled = XhrDriver.enabled && XhrDriver.supportsCORS;\n\nmodule.exports = XHRCorsObject;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAGxC,SAASE,aAAaA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACjDL,SAAS,CAACM,IAAI,CAAC,IAAI,EAAEJ,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,CAAC;AAClD;AAEAP,QAAQ,CAACG,aAAa,EAAED,SAAS,CAAC;AAElCC,aAAa,CAACM,OAAO,GAAGP,SAAS,CAACO,OAAO,IAAIP,SAAS,CAACQ,YAAY;AAEnEC,MAAM,CAACC,OAAO,GAAGT,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}